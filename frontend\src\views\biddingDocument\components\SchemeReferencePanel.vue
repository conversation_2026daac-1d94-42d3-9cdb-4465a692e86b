<template>
  <div class="scheme-reference-panel" :class="{ 'scheme-reference-panel-collapsed': collapsed }">
    <!-- 面板头部 -->
    <div class="scheme-reference-panel-header" @click="toggleCollapsed">
      <div class="header-left">
        <FileTextOutlined class="scheme-icon" />
        <span class="title">方案参考</span>
        <span v-if="schemeName" class="scheme-name">（{{ schemeName }}）</span>
      </div>
      <div class="header-right">
        <a-button type="text" size="small" @click.stop="toggleCollapsed">
          <LeftOutlined :class="{ 'collapsed-icon': collapsed }" />
        </a-button>
      </div>
    </div>

    <!-- 面板内容 -->
    <div class="scheme-reference-panel-content" v-show="!collapsed">
      <!-- 方案内容显示 -->
      <div class="scheme-content-section" v-if="schemeContent">
        <div class="scheme-content-wrapper">
          <TiptapEditor
            :model-value="schemeContent"
            :editable="false"
            :show-toolbar="false"
            :min-height="'400px'"
            placeholder="方案内容为空"
          />
        </div>
      </div>

      <!-- 无方案状态 -->
      <div class="no-scheme-state" v-else>
        <a-empty description="暂无选中的招标方案">
          <template #image>
            <FileTextOutlined style="font-size: 48px; color: #d9d9d9;" />
          </template>
        </a-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { FileTextOutlined, LeftOutlined } from '@ant-design/icons-vue'
import TiptapEditor from '@/components/TiptapEditor/index.vue'

// Props定义
const props = defineProps({
  // 方案数据
  schemeData: {
    type: Object,
    default: null
  },
  // 方案内容（已经合并好的 TiptapJSON）
  schemeContent: {
    type: Object,
    default: null
  }
})

// 响应式数据
const collapsed = ref(false)

// 计算属性
const schemeName = computed(() => {
  return props.schemeData?.name || ''
})

// 方法
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}
</script>

<style lang="less" scoped>
.scheme-reference-panel {
  width: 21cm;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  margin-right: 16px;
  transition: all 0.3s ease;

  &.scheme-reference-panel-collapsed {
    width: 48px;
    min-width: 48px;
    max-width: 48px;
  }

  .scheme-reference-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #f8f9fa;
    cursor: pointer;
    user-select: none;
    min-height: 48px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      overflow: hidden;

      .scheme-icon {
        color: #1890ff;
        font-size: 16px;
        flex-shrink: 0;
      }

      .title {
        font-weight: 600;
        color: #262626;
        white-space: nowrap;
        flex-shrink: 0;
      }

      .scheme-name {
        color: #666;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-shrink: 0;

      .collapsed-icon {
        transform: rotate(180deg);
      }
    }
  }

  .scheme-reference-panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .scheme-content-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .scheme-content-wrapper {
        flex: 1;
        padding: 16px;
        overflow: auto;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin: 16px;
        background: #fafafa;
      }
    }

    .no-scheme-state {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
    }
  }
}

// 折叠状态下隐藏内容
.scheme-reference-panel-collapsed {
  .header-left .title,
  .header-left .scheme-name,
  .scheme-reference-panel-content {
    display: none;
  }

  .scheme-reference-panel-header {
    justify-content: center;
    
    .header-left {
      justify-content: center;
    }
    
    .header-right {
      display: none;
    }
  }
}
</style> 