'use strict';

/**
 * AI聊天配置文件
 * 包含DeepSeek API配置、对话设置、系统提示词等
 */
module.exports = {
  // DeepSeek API配置
  deepseek: {
    baseURL: 'https://api.deepseek.com',
    apiKey: '***********************************',
    model: 'deepseek-chat',
    maxTokens: 2048,
    temperature: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
    topP: 1,
    timeout: 30000, // 30秒超时
  },

  // 对话设置
  chat: {
    maxHistoryMessages: 20,        // 最大历史消息数量
    defaultTitle: '新对话',        // 默认会话标题
    titleMaxLength: 30,           // 标题最大长度
    autoGenerateTitle: true,      // 是否自动生成标题
    contextWindowSize: 4000,      // 上下文窗口大小（token数）
    streamEnabled: true,          // 是否启用流式响应
  },

  // 系统提示词
  systemPrompts: {
    // default: '你是一个有用的AI助手，请用中文回答用户的问题。',
    default: '你是一个专业的招标文件助手，擅长帮助用户处理招标相关的问题，包括招标文件编写、方案制作、流程咨询等。请用专业、准确的语言回答用户的问题。',
    tender: '你是一个专业的招标文件助手，擅长帮助用户处理招标相关的问题，包括招标文件编写、方案制作、流程咨询等。请用专业、准确的语言回答用户的问题。',
    document: '你是一个文档处理专家，可以帮助用户编写、修改和优化各种文档内容。请提供清晰、结构化的建议。'
  },

  // 错误消息配置
  errorMessages: {
    networkError: '网络连接失败，请检查网络设置后重试。',
    apiKeyError: 'API密钥配置错误，请联系管理员。',
    rateLimitError: '请求频率过高，请稍后再试。',
    timeoutError: '请求超时，请稍后重试。',
    defaultError: '抱歉，AI服务暂时不可用，请稍后再试。'
  }
}; 