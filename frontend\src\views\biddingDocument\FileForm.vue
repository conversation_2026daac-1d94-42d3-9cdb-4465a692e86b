<template>
  <a-spin :spinning="isLoading || isExportingDocx">
    <div class="page-header">
      <a-page-header :title="isEditMode ? '编辑招标文件' : '新建招标文件'" @back="() => $router.go(-1)" />

      <div class="flex gap-x-[10px]">
        <a-button type="primary" @click="handleSubmit" :loading="isLoading" :disabled="isExportingDocx">{{ isEditMode ? '更新文件' : '创建文件' }}</a-button>
        <a-button @click="handleExportToDocx" :loading="isExportingDocx" :disabled="!editorVisible || isLoading || (!isEditMode && !formState.template_id)">
          导出为 DOCX
        </a-button>
        <a-button type="primary" @click="handleVersionHistory" :disabled="!isEditMode">版本历史</a-button>
      </div>
    </div>
    <div class="flex">
      <!-- 方案参考面板 -->
      <SchemeReferencePanel v-if="selectedSchemeFull && selectedSchemeFullContent" :scheme-data="selectedSchemeFull" :scheme-content="selectedSchemeFullContent" />

      <!-- 本地文件预览面板 -->
      <DocxPreview
        v-if="selectedLocalFile || selectedLocalFilePath"
        :file-data="selectedLocalFile"
        :file-name="selectedLocalFileName"
        :file-path="selectedLocalFilePath"
      />

      <a-card :loading="isLoading" style="width: 100%" :bodyStyle="{ height: 'calc(100vh - 180px)', overflow: 'auto' }">
        <a-form ref="formRef" layout="vertical" :model="formState" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-item label="文件名称" name="name">
            <a-input v-model:value="formState.name" placeholder="请输入文件名称" />
          </a-form-item>

          <a-form-item label="招标文件模板" name="template_id">
            <a-tree-select
              v-model:value="formState.template_id"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder="请选择招标文件模板"
              show-search
              tree-default-expand-all
              :tree-data="allDocumentTemplates"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              tree-node-filter-prop="name"
              :allowClear="!isEditMode"
              :disabled="isEditMode"
              @change="handleTemplateChange"
            />
          </a-form-item>

          <a-form-item label="招标方案" name="scheme_id">
            <a-select
              v-model:value="formState.scheme_id"
              :options="allTenderSchemes.map(s => ({ value: s.id, label: s.name, scheme: s }))"
              placeholder="请选择招标方案（可选，用于快速填写相同字段）"
              show-search
              :filter-option="filterSchemeOption"
              :allowClear="true"
              @change="handleSchemeChange"
            >
              <template #dropdownRender="{ menuNode }">
                <VNodes :vnodes="menuNode" />
                <a-divider style="margin: 4px 0" />
                <div class="local-file-option" @click="selectLocalFileOption">
                  <FileWordOutlined class="local-file-icon" />
                  <span class="local-file-text">选择本地文件 (.docx)</span>
                  <span class="local-file-description">上传并预览本地 Word 文档作为参考</span>
                </div>
              </template>
            </a-select>
            <div class="ant-form-item-explain" style="margin-top: 4px; color: #666; font-size: 12px">
              选择招标方案后，系统会自动将方案中已填写的字段值填充到对应的模板字段中，方便快速创建内容
              <br />
              也可以选择"本地文件(.docx)"来上传并预览本地 Word 文档作为参考
            </div>
            <!-- 本地文件选择器 -->
            <div v-if="showLocalFileSelector" class="local-file-selector">
              <input ref="fileInputRef" type="file" accept=".docx" @change="handleLocalFileChange" style="display: none" />
              <a-button type="dashed" block @click="triggerFileSelector">
                <FileWordOutlined />
                选择本地 Word 文档 (.docx)
              </a-button>
              <div v-if="selectedLocalFile" class="selected-file-info">
                <FileWordOutlined style="color: #1890ff; margin-right: 8px" />
                {{ selectedLocalFileName }}
                <a-button type="link" size="small" @click="clearLocalFile">移除</a-button>
              </div>
              <div v-if="selectedLocalFileName && !selectedLocalFile" class="local-file-tip">
                <a-alert
                  message="本地文件关联信息"
                  :description="`该文件${isEditMode ? '之前' : ''}关联了本地文档：${selectedLocalFileName}。${
                    selectedLocalFilePath ? '正在通过协议加载预览...' : '如需重新预览，请重新选择该文件。'
                  }`"
                  :type="selectedLocalFilePath ? 'success' : 'info'"
                  show-icon
                  style="margin-top: 8px"
                />
              </div>
            </div>
          </a-form-item>

          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="3" />
          </a-form-item>

          <a-form-item label="文件内容" name="content">
            <div class="editor-risk-container">
              <div class="editor-container-wrapper">
                <tiptap-editor
                  v-if="editorVisible"
                  ref="tiptapEditorRef"
                  v-model="formState.content"
                  :editable="!!formState.template_id"
                  :fill-mode="!!formState.template_id"
                  :show-toolbar="false"
                  min-height="400px"
                  placeholder="请选择模板加载内容..."
                  :risk-data="riskAssessmentData?.field_risks || []"
                />
                <div v-else class="editor-placeholder-wrapper">{{ editorPlaceholderText }}</div>
              </div>
            </div>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 风险面板 -->
      <RiskPanel
        v-if="isEditMode && editorVisible"
        :risk-assessment-data="riskAssessmentData"
        :loading="riskAssessmentLoading"
        @scroll-to-field="handleScrollToField"
        @highlight-field="handleHighlightField"
        @refresh-assessment="handleRefreshRiskAssessment"
        @start-assessment="handleStartRiskAssessment"
      />
    </div>

    <!-- 版本历史组件 -->
    <VersionHistory
      v-model:visible="versionHistoryVisible"
      :history-versions="historyVersions"
      :template-content="selectedTemplateFull?.content"
      :current-field-values="formState.content ? extractFieldValuesFromTiptapJSON(formState.content) : {}"
    />

    <!-- 更新备注对话框 -->
    <a-modal
      v-model:open="updateRemarkModalVisible"
      title="填写更新备注"
      :confirm-loading="isLoading"
      @ok="handleUpdateRemarkSubmit"
      @cancel="handleUpdateRemarkCancel"
      :mask-closable="false"
      width="500px"
      ok-text="确认保存"
      cancel-text="取消"
    >
      <div style="margin-bottom: 16px">
        <a-alert message="检测到文件内容已发生变化" description="请填写本次更新的备注信息，说明具体修改了什么内容。" type="info" show-icon style="margin-bottom: 16px" />
      </div>
      <a-form ref="updateRemarkFormRef" :model="updateRemarkForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="更新备注" name="remark" :rules="[{ required: true, message: '请填写更新备注', trigger: 'blur' }]">
          <a-textarea
            v-model:value="updateRemarkForm.remark"
            placeholder="请详细说明本次修改的内容，方便后续查看历史版本时了解改动情况"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-spin>
</template>

<script setup>
import { ref, reactive, watch, onMounted, onUnmounted, nextTick, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { FileWordOutlined } from '@ant-design/icons-vue'
import api from '@/api'
import TiptapEditor from '@/components/TiptapEditor/index.vue'
import VersionHistory from '@/components/VersionHistory.vue'
import RiskPanel from '@/components/TiptapEditor/RiskPanel.vue'
import SchemeReferencePanel from '@/views/biddingDocument/components/SchemeReferencePanel.vue'
import DocxPreview from '@/views/biddingDocument/components/DocxPreview.vue'
import webSocketService from '@/utils/webSocketService'
import { exportTiptapContentToDocx } from '@/utils/docxExporter'
import { mergeTemplateWithFieldValues, extractFieldValuesFromTiptapJSON, sanitizeTextNodesInTiptapJSON, getDefaultTiptapDoc } from '@/utils/templateUtils'

const route = useRoute()
const router = useRouter()

// 用于渲染 VNode 的组件
const VNodes = {
  props: {
    vnodes: { type: [Array, Object], required: true }
  },
  render() {
    return this.vnodes
  }
}

const formRef = ref(null)
const tiptapEditorRef = ref(null)
const isLoading = ref(false)
const isExportingDocx = ref(false)
const editorVisible = ref(false) // 编辑器延迟加载
const versionHistoryVisible = ref(false)
const historyVersions = ref([])

// 从路由参数获取文件ID，判断是否为编辑模式
const fileId = ref(route.params.id || null)
const isEditMode = computed(() => !!fileId.value)

const initialFormState = () => ({
  id: undefined,
  name: '',
  template_id: null,
  scheme_id: null,
  remark: '',
  content: { type: 'doc', content: [{ type: 'paragraph' }] }
})

const formState = reactive(initialFormState())
const selectedTemplateFull = ref(null) // 存储选中的完整模板对象，包含content
const selectedSchemeFull = ref(null) // 存储选中的完整方案对象，包含content (字段值)
const selectedSchemeTemplate = ref(null) // 存储选中方案关联的模板对象

// 本地文件相关状态
const fileInputRef = ref(null)
const selectedLocalFile = ref(null) // 存储选中的本地文件对象
const selectedLocalFileName = ref('')
const selectedLocalFilePath = ref('') // 存储本地文件路径（用于协议加载）
const showLocalFileSelector = ref(false) // 是否显示本地文件选择器
const LOCAL_FILE_OPTION_VALUE = null // 本地文件选项的特殊值，使用null避免外键约束

// 计算方案参考内容（合并方案字段值与其关联的模板内容）
const selectedSchemeFullContent = computed(() => {
  if (!selectedSchemeFull.value || !selectedSchemeTemplate.value) {
    return null
  }

  // 获取方案的字段值
  let schemeFieldValues = {}
  try {
    if (selectedSchemeFull.value.content) {
      schemeFieldValues = typeof selectedSchemeFull.value.content === 'string' ? JSON.parse(selectedSchemeFull.value.content) : selectedSchemeFull.value.content
    }
  } catch (error) {
    console.error('解析方案字段值失败:', error)
    return null
  }

  // 获取方案关联的模板内容
  const templateContent = selectedSchemeTemplate.value.content
  if (!templateContent) {
    return null
  }

  try {
    // 合并模板内容与方案字段值
    const mergedContent = mergeTemplateWithFieldValues(templateContent, schemeFieldValues)
    sanitizeTextNodesInTiptapJSON(mergedContent)
    return mergedContent
  } catch (error) {
    console.error('合并方案参考内容失败:', error)
    return null
  }
})

// 存储原始内容，用于检查是否有变化
const originalContent = ref({})
const contentChanged = ref(false)

// 更新备注对话框相关
const updateRemarkModalVisible = ref(false)
const updateRemarkFormRef = ref()
const updateRemarkForm = reactive({
  remark: ''
})
const pendingUpdateData = ref(null) // 存储待提交的数据

// 风险评估相关状态
const riskAssessmentData = ref(null)
const riskAssessmentLoading = ref(false)
const currentRiskAssessmentId = ref(null) // 存储当前文件的风险评估ID
// 风险提示相关状态已移至 TiptapEditor 组件内部

const rules = {
  name: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
  template_id: [{ required: true, message: '请选择招标文件模板', trigger: 'change' }],
  content: [
    {
      required: true,
      validator: async (_, value) => {
        if (
          !value ||
          !value.content ||
          value.content.length === 0 ||
          (value.content.length === 1 && value.content[0].type === 'paragraph' && (!value.content[0].content || value.content[0].content.length === 0))
        ) {
          return Promise.reject('文件内容不能为空')
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ]
}

const allDocumentTemplates = ref([])
const allTenderSchemes = ref([])

// 检查内容是否有变化的函数
const checkContentChanged = currentFieldValues => {
  if (!isEditMode.value) {
    return false
  }

  const originalContentStr = JSON.stringify(originalContent.value || {})
  const currentContentStr = JSON.stringify(currentFieldValues || {})

  return originalContentStr !== currentContentStr
}

// 编辑器占位符文本，参考 SchemeForm 的实现
const editorPlaceholderText = computed(() => {
  if (isLoading.value && !isEditMode.value && !formState.template_id) return '请先选择一个招标文件模板...'
  if (isLoading.value) return '正在加载内容...'
  if (isEditMode.value && !editorVisible.value) return '正在加载文件内容...'
  if (!formState.template_id && !isEditMode.value) return '请先选择一个招标文件模板'
  if (!editorVisible.value && formState.template_id) return '模板已选择，正在准备编辑器...'
  return '编辑器加载中或模板内容为空'
})

// 风险评估相关方法
const handleStartRiskAssessment = async () => {
  if (!isEditMode.value || !fileId.value) {
    message.warning('请在编辑现有文件时进行风险评估')
    return
  }

  try {
    riskAssessmentLoading.value = true

    // 清空当前风险评估数据，因为新的评估正在进行
    riskAssessmentData.value = null
    currentRiskAssessmentId.value = null

    const response = await api.riskAssessment.startAssessment({ document_type: 'bidding_document', document_id: fileId.value })

    if (response?.success) {
      message.success('风险评估已启动，评估完成后将自动显示结果')
      // 注意：这里不需要设置 riskAssessmentLoading.value = false，
      // 保持loading状态直到评估完成或失败
    } else {
      message.error(response?.message || '启动风险评估失败')
      riskAssessmentLoading.value = false
    }
  } catch (error) {
    console.error('启动风险评估失败:', error)
    message.error('启动风险评估时发生错误: ' + (error.message || '未知错误'))
    riskAssessmentLoading.value = false
  }
}

const handleRefreshRiskAssessment = async () => {
  await loadRiskAssessmentData()
}

const loadRiskAssessmentData = async () => {
  if (!isEditMode.value || !currentRiskAssessmentId.value) {
    riskAssessmentData.value = null
    return
  }

  try {
    riskAssessmentLoading.value = true

    // 直接使用风险评估ID获取评估信息
    const response = await api.riskAssessment.getAssessmentInfo({ risk_assessment_id: currentRiskAssessmentId.value })

    if (response?.success && response.data) {
      const assessment = response.data

      // 检查评估状态是否为完成
      if (['low', 'medium', 'high'].includes(assessment.status) && assessment.ai_result) {
        // 解析AI结果（如果是字符串格式）
        let aiResult = assessment.ai_result
        if (typeof aiResult === 'string') {
          try {
            aiResult = JSON.parse(aiResult)
          } catch (e) {
            console.error('解析AI评估结果失败:', e)
            riskAssessmentData.value = null
            return
          }
        }

        riskAssessmentData.value = aiResult

        // 更新编辑器的风险高亮
        if (tiptapEditorRef.value && aiResult?.field_risks) {
          nextTick(() => {
            tiptapEditorRef.value.updateRiskHighlights(aiResult.field_risks)
          })
        }
      } else {
        // 评估未完成或无结果
        riskAssessmentData.value = null
      }
    } else {
      riskAssessmentData.value = null
    }
  } catch (error) {
    console.error('加载风险评估数据失败:', error)
    riskAssessmentData.value = null
  } finally {
    riskAssessmentLoading.value = false
  }
}

const handleScrollToField = risk => {
  // 滚动到指定字段
  if (tiptapEditorRef.value) {
    tiptapEditorRef.value.scrollToField(risk.field_key)
  }
}

const handleHighlightField = (risk, highlight) => {
  // 临时高亮字段
  if (tiptapEditorRef.value) {
    tiptapEditorRef.value.highlightField(risk.field_key, highlight)
  }
}

// WebSocket事件监听
const setupRiskAssessmentListeners = () => {
  const handleRiskAssessmentCompleted = data => {
    const { documentType, documentId, taskId } = data
    if (documentType === 'bidding_document' && documentId === Number(fileId.value)) {
      message.success('风险评估已完成！')

      // 更新当前文件的风险评估ID
      currentRiskAssessmentId.value = taskId

      // 结束loading状态
      riskAssessmentLoading.value = false

      // 重新加载风险评估数据
      loadRiskAssessmentData()
    }
  }

  const handleRiskAssessmentFailed = data => {
    const { documentType, documentId } = data
    if (documentType === 'bidding_document' && documentId === Number(fileId.value)) {
      message.error('风险评估失败：' + data.error)
      riskAssessmentLoading.value = false
    }
  }

  webSocketService.on('risk_assessment_completed', handleRiskAssessmentCompleted)
  webSocketService.on('risk_assessment_failed', handleRiskAssessmentFailed)

  // 返回清理函数
  return () => {
    webSocketService.off('risk_assessment_completed', handleRiskAssessmentCompleted)
    webSocketService.off('risk_assessment_failed', handleRiskAssessmentFailed)
  }
}

// 获取所有招标文件模板
const fetchAllDocumentTemplates = async () => {
  try {
    const response = await api.biddingDocumentTemplate.getTree()
    if (response.success) {
      allDocumentTemplates.value = response.data || []
    } else {
      message.error('获取招标文件模板列表失败: ' + response.message)
    }
  } catch (error) {
    message.error('加载招标文件模板错误: ' + error.message)
  }
}

// 获取所有招标方案
const fetchAllTenderSchemes = async () => {
  try {
    const response = await api.tenderScheme.list()
    if (response.success) {
      allTenderSchemes.value = response.data?.list || []
    } else {
      message.error('获取招标方案列表失败: ' + response.message)
    }
  } catch (error) {
    message.error('加载招标方案列表错误: ' + error.message)
  }
}

// 加载编辑数据
const loadEditData = async id => {
  isLoading.value = true
  editorVisible.value = false
  try {
    const response = await api.biddingDocument.getById({ id })
    if (response.success && response.data) {
      const data = response.data
      formState.id = data.id
      formState.name = data.name
      formState.template_id = data.template_id
      formState.scheme_id = data.scheme_id
      formState.remark = data.remark

      // 保存风险评估ID（如果存在）
      currentRiskAssessmentId.value = data.risk_assessment_id || null

      // data.content 现在是字段值对象 { field_key: "value" }
      const fieldValues = data.content && typeof data.content === 'object' ? data.content : {}

      // 在编辑模式下保存原始内容用于比较
      if (isEditMode.value) {
        originalContent.value = JSON.parse(JSON.stringify(fieldValues))
      }

      // 获取模板内容并合并字段值
      if (data.template_id) {
        console.log('加载模板内容，ID:', data.template_id)
        const templateRes = await api.biddingDocumentTemplate.getById({ id: data.template_id })
        if (templateRes.success && templateRes.data) {
          selectedTemplateFull.value = templateRes.data

          // 获取模板内容
          let templateContent = templateRes.data.content
          if (typeof templateContent === 'string') {
            try {
              templateContent = JSON.parse(templateContent)
            } catch (e) {
              console.error('模板内容JSON解析失败:', e)
              templateContent = getDefaultTiptapDoc()
            }
          }

          if (templateContent && templateContent.type === 'doc') {
            // 清理模板内容
            sanitizeTextNodesInTiptapJSON(templateContent)

            // 合并模板内容与字段值
            const mergedContent = mergeTemplateWithFieldValues(templateContent, fieldValues)
            sanitizeTextNodesInTiptapJSON(mergedContent)

            formState.content = mergedContent
            console.log('模板内容与字段值合并完成')
          } else {
            console.warn('模板内容格式不正确，使用默认文档结构')
            formState.content = getDefaultTiptapDoc()
          }
        } else {
          console.error('获取模板内容失败:', templateRes.message)
          formState.content = getDefaultTiptapDoc()

          // 在编辑模式下保存原始内容用于比较，即使模板加载失败
          if (isEditMode.value) {
            originalContent.value = JSON.parse(JSON.stringify(fieldValues))
          }
        }
      } else {
        console.warn('招标文件没有关联模板')
        formState.content = getDefaultTiptapDoc()

        // 在编辑模式下保存原始内容用于比较，即使没有模板
        if (isEditMode.value) {
          originalContent.value = JSON.parse(JSON.stringify(fieldValues))
        }
      }

      // 处理方案关联逻辑
      if (data.scheme_id) {
        // 有具体的方案ID，加载方案数据
        const schemeRes = await api.tenderScheme.getById({ id: data.scheme_id })
        if (schemeRes.success) {
          selectedSchemeFull.value = schemeRes.data

          // 同时获取方案关联的模板内容，用于方案参考面板显示
          if (schemeRes.data.template_id) {
            try {
              console.log('正在获取方案关联的模板内容（编辑模式），ID:', schemeRes.data.template_id)
              const schemeTemplateRes = await api.tenderSchemeTemplate.getById({ id: schemeRes.data.template_id })
              if (schemeTemplateRes.success && schemeTemplateRes.data) {
                selectedSchemeTemplate.value = schemeTemplateRes.data
                console.log('方案关联的模板内容已获取（编辑模式）')
              } else {
                console.error('获取方案模板内容失败（编辑模式）:', schemeTemplateRes.message)
                selectedSchemeTemplate.value = null
              }
            } catch (error) {
              console.error('获取方案模板内容时出错（编辑模式）:', error)
              selectedSchemeTemplate.value = null
            }
          }
        }
      } else {
        // scheme_id 为 null，检查是否有本地文件关联
        const localFiles = getLocalFilesData()
        const localFileData = localFiles.find(item => item.id === data.id)

        if (localFileData) {
          // 有本地文件关联，显示本地文件选择器并恢复文件信息
          showLocalFileSelector.value = true
          selectedLocalFileName.value = localFileData.fileName
          selectedLocalFilePath.value = localFileData.filePath || localFileData.fileName

          // 更新最后访问时间
          localFileData.lastAccessed = new Date().toISOString()
          const localFiles = getLocalFilesData()
          const index = localFiles.findIndex(item => item.id === data.id)
          if (index >= 0) {
            localFiles[index] = localFileData
            saveLocalFilesData(localFiles)
          }

          console.log('检测到本地文件关联，已恢复文件信息:', localFileData)
        } else {
          // 没有本地文件关联，真正的没选方案
          console.log('没有选择方案，也没有本地文件关联')
        }
      }
    } else {
      message.error('加载文件数据失败: ' + response.message)
      router.push({ name: 'BiddingDocumentFileList' })
    }
  } catch (error) {
    message.error('加载编辑数据错误: ' + error.message)
    router.push({ name: 'BiddingDocumentFileList' })
  } finally {
    isLoading.value = false
    nextTick(() => {
      editorVisible.value = true
    })
  }
}

// 处理模板选择变化
const handleTemplateChange = async value => {
  console.log('模板选择变化:', value)

  // 编辑模式下不允许更换模板
  if (isEditMode.value) return

  if (value) {
    // 从树形数据中查找模板
    const findTemplateInTree = (nodes, id) => {
      for (const node of nodes) {
        if (node.id === id) return node
        if (node.children) {
          const found = findTemplateInTree(node.children, id)
          if (found) return found
        }
      }
      return null
    }

    const templateData = findTemplateInTree(allDocumentTemplates.value, value)

    // 如果当前已有内容且不是默认空内容，需要确认是否替换
    const hasExistingContent =
      formState.content &&
      formState.content.content &&
      formState.content.content.length > 0 &&
      !(
        formState.content.content.length === 1 &&
        formState.content.content[0].type === 'paragraph' &&
        (!formState.content.content[0].content || formState.content.content[0].content.length === 0)
      )

    if (hasExistingContent) {
      Modal.confirm({
        title: '确认切换招标文件模板',
        content: '切换招标文件模板后，当前编辑的内容将被替换为新模板的内容，此操作不可撤销。是否继续？',
        okText: '确认切换',
        cancelText: '取消',
        onOk: async () => {
          await loadTemplateContentById(value, templateData)
        },
        onCancel: () => {
          // 恢复原来的选择
          const originalTemplateId = selectedTemplateFull.value?.id || null
          formState.template_id = originalTemplateId
        }
      })
    } else {
      // 无论是从树形数据还是通过API获取，都统一处理
      await loadTemplateContentById(value, templateData)
    }
  } else {
    selectedTemplateFull.value = null
    // 清空模板选择时，重置为空内容
    formState.content = getDefaultTiptapDoc()
    console.log('清空模板选择，重置内容')
  }
}

// 通过模板ID加载模板内容（参考TemplateForm的实现）
const loadTemplateContentById = async (templateId, templateFromOption = null) => {
  if (!templateId) return

  editorVisible.value = false
  isLoading.value = true

  try {
    let templateData = templateFromOption

    // 如果没有从option获取到完整数据，则通过API获取
    if (!templateData || !templateData.content) {
      console.log('正在获取模板详情，ID:', templateId)
      const response = await api.biddingDocumentTemplate.getById({ id: templateId })
      console.log('模板详情响应:', response)

      if (response.success && response.data) {
        templateData = response.data
      } else {
        message.error('获取模板详情失败: ' + response.message)
        return
      }
    }

    selectedTemplateFull.value = templateData

    // 检查模板是否有内容
    if (templateData.content && templateData.content.content && Array.isArray(templateData.content.content)) {
      console.log('模板内容:', templateData.content)

      // 获取并清理模板内容
      let templateContent = JSON.parse(JSON.stringify(templateData.content))
      sanitizeTextNodesInTiptapJSON(templateContent)

      // 直接加载模板内容
      formState.content = templateContent
      console.log('模板内容已加载到formState.content')

      message.success('模板内容加载成功')

      // 如果已选择方案，尝试填充字段
      if (selectedSchemeFull.value) {
        await tryFillSchemeFields()
      }
    } else {
      console.warn('模板没有内容或内容格式不正确')
      message.warn('模板没有内容，将创建空文件')
      formState.content = getDefaultTiptapDoc()
    }
  } catch (error) {
    console.error('加载模板内容时出错:', error)
    message.error('加载模板内容时出错: ' + error.message)
    selectedTemplateFull.value = null
  } finally {
    isLoading.value = false
    nextTick(() => {
      editorVisible.value = true
      console.log('编辑器已重新显示')
    })
  }
}

// 处理方案选择变化
const handleSchemeChange = async (value, option) => {
  console.log('方案选择变化:', value, option)

  // 处理本地文件选择
  if (value === LOCAL_FILE_OPTION_VALUE) {
    // 清空方案相关状态
    selectedSchemeFull.value = null
    selectedSchemeTemplate.value = null
    // 显示本地文件选择器
    showLocalFileSelector.value = true
    console.log('选择本地文件选项，显示文件选择器')
    return
  }

  // 隐藏本地文件选择器
  showLocalFileSelector.value = false
  // 清空本地文件选择
  selectedLocalFile.value = null
  selectedLocalFileName.value = ''
  selectedLocalFilePath.value = ''

  if (value) {
    try {
      let schemeData = option?.scheme

      // 如果没有从option获取到完整数据，则通过API获取
      if (!schemeData || !schemeData.content) {
        console.log('正在获取方案详情，ID:', value)
        const schemeDetails = await api.tenderScheme.getById({ id: value })
        if (schemeDetails.success) {
          schemeData = schemeDetails.data
        } else {
          message.error('获取方案详情失败：' + schemeDetails.message)
          return
        }
      }

      selectedSchemeFull.value = schemeData
      console.log('方案数据已设置:', schemeData)

      // 获取方案关联的模板内容，用于方案参考面板显示
      if (schemeData.template_id) {
        try {
          console.log('正在获取方案关联的模板内容，ID:', schemeData.template_id)
          const templateRes = await api.tenderSchemeTemplate.getById({ id: schemeData.template_id })
          if (templateRes.success && templateRes.data) {
            selectedSchemeTemplate.value = templateRes.data
            console.log('方案关联的模板内容已获取')
          } else {
            console.error('获取方案模板内容失败:', templateRes.message)
            selectedSchemeTemplate.value = null
          }
        } catch (error) {
          console.error('获取方案模板内容时出错:', error)
          selectedSchemeTemplate.value = null
        }
      } else {
        selectedSchemeTemplate.value = null
      }

      // 如果已选择模板，尝试填充字段
      if (selectedTemplateFull.value) {
        // 在编辑模式下，询问用户是否要填充方案字段（可能会覆盖现有内容）
        if (isEditMode.value) {
          Modal.confirm({
            title: '确认填充方案字段',
            content: '填充方案字段将会覆盖当前编辑的内容。是否继续？',
            okText: '确认填充',
            cancelText: '取消',
            onOk: async () => {
              await tryFillSchemeFields()
            }
          })
        } else {
          await tryFillSchemeFields()
        }
      }
    } catch (error) {
      console.error('处理方案选择时出错:', error)
      message.error('处理方案选择时出错: ' + error.message)
    }
  } else {
    selectedSchemeFull.value = null
    selectedSchemeTemplate.value = null
    // 清空本地文件选择
    showLocalFileSelector.value = false
    selectedLocalFile.value = null
    selectedLocalFileName.value = ''
    selectedLocalFilePath.value = ''
    console.log('清空方案选择')

    // 如果有模板，恢复模板原始内容
    if (selectedTemplateFull.value && selectedTemplateFull.value.content) {
      formState.content = JSON.parse(JSON.stringify(selectedTemplateFull.value.content))
      console.log('恢复模板原始内容')
    }
  }
}

// 尝试将方案字段填充到当前编辑器内容（基于当前选定模板的字段）
const tryFillSchemeFields = async () => {
  // 如果没有选择模板，直接返回
  if (!selectedTemplateFull.value || !selectedTemplateFull.value.content) {
    console.log('没有选择模板或模板没有内容，跳过字段填充')
    return
  }

  // 如果没有选择方案，保持模板原始内容，不做任何处理
  if (!selectedSchemeFull.value || !selectedSchemeFull.value.content) {
    console.log('没有选择方案，保持模板原始内容')
    return
  }

  console.log('开始填充方案字段到模板内容')
  editorVisible.value = false

  try {
    const templateDoc = JSON.parse(JSON.stringify(selectedTemplateFull.value.content))
    const schemeValues = typeof selectedSchemeFull.value.content === 'string' ? JSON.parse(selectedSchemeFull.value.content) : selectedSchemeFull.value.content

    if (typeof schemeValues !== 'object' || schemeValues === null) {
      message.warn('招标方案的字段内容格式不正确，无法自动填充。')
      return
    }

    // 清理模板内容
    sanitizeTextNodesInTiptapJSON(templateDoc)

    // 合并模板内容与字段值
    const filledContent = mergeTemplateWithFieldValues(templateDoc, schemeValues)

    // 清理合并后的内容
    sanitizeTextNodesInTiptapJSON(filledContent)

    formState.content = filledContent
    console.log('方案字段填充完成')
    message.success('方案字段已自动填充到模板中')
  } catch (error) {
    console.error('填充方案字段时出错:', error)
    message.error('填充方案字段时出错: ' + error.message)
  } finally {
    nextTick(() => {
      editorVisible.value = true
      console.log('编辑器已重新显示（字段填充后）')
    })
  }
}

// 实际提交更新的函数
const submitUpdate = async payload => {
  try {
    isLoading.value = true

    let response
    if (isEditMode.value) {
      response = await api.biddingDocument.update(payload)
    } else {
      response = await api.biddingDocument.create(payload)
    }

    if (response.success) {
      message.success(isEditMode.value ? '文件更新成功' : '文件创建成功')

      // 如果有本地文件，保存文件关联
      if (selectedLocalFileName.value && response.data?.id) {
        saveLocalFileAssociation(response.data.id, selectedLocalFileName.value, selectedLocalFilePath.value)
      }

      // 重置内容变化相关状态
      contentChanged.value = false
      updateRemarkForm.remark = ''
      pendingUpdateData.value = null
      router.push({ name: 'BiddingDocumentFileList' })
    } else {
      message.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('Error submitting update:', error)
    message.error('提交更新时发生错误。')
  } finally {
    isLoading.value = false
  }
}

// 处理更新备注对话框确认
const handleUpdateRemarkSubmit = async () => {
  try {
    // 先进行表单验证
    await updateRemarkFormRef.value.validate()

    if (!pendingUpdateData.value) {
      message.error('没有待提交的数据')
      return
    }

    // 添加更新备注到待提交数据
    const finalPayload = {
      ...pendingUpdateData.value,
      update_remark: updateRemarkForm.remark.trim()
    }

    updateRemarkModalVisible.value = false
    await submitUpdate(finalPayload)
  } catch (error) {
    // 表单验证失败，不执行提交
    console.log('更新备注表单验证失败:', error)
  }
}

// 处理更新备注对话框取消
const handleUpdateRemarkCancel = () => {
  updateRemarkModalVisible.value = false
  updateRemarkForm.remark = ''
  pendingUpdateData.value = null
  isLoading.value = false
  // 重置表单验证状态
  if (updateRemarkFormRef.value) {
    updateRemarkFormRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 从编辑器内容中提取字段值（包含样式）
    const currentFieldValues = extractFieldValuesFromTiptapJSON(formState.content)
    console.log('[FileForm.handleSubmit] 提取的字段值（含样式）:', currentFieldValues)

    // 检查内容是否有变化
    const hasContentChanged = checkContentChanged(currentFieldValues)
    contentChanged.value = hasContentChanged

    const payload = {
      ...formState,
      content: currentFieldValues // 只发送字段值对象
    }

    // 如果是编辑模式且内容有变化，弹出更新备注对话框
    if (isEditMode.value && hasContentChanged) {
      pendingUpdateData.value = payload
      updateRemarkForm.remark = '' // 清空之前的输入
      updateRemarkModalVisible.value = true
      return
    }

    // 如果没有内容变化，直接提交
    await submitUpdate(payload)
  } catch (errorInfo) {
    message.error('请填写所有必填项并检查内容格式')
  }
}

const filterSchemeOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 选择本地文件选项的处理方法
const selectLocalFileOption = () => {
  // 设置 scheme_id 为本地文件的特殊值
  formState.scheme_id = LOCAL_FILE_OPTION_VALUE

  // 清空方案相关状态
  selectedSchemeFull.value = null
  selectedSchemeTemplate.value = null

  // 清空其他本地文件选择状态（如果有的话）
  selectedLocalFile.value = null
  selectedLocalFileName.value = ''
  selectedLocalFilePath.value = ''

  // 显示本地文件选择器
  showLocalFileSelector.value = true

  console.log('选择本地文件选项，显示文件选择器')
}

// 处理本地文件选择器的触发
const triggerFileSelector = () => {
  if (fileInputRef.value) {
    fileInputRef.value.click()
  }
}

// 处理本地文件选择变化
const handleLocalFileChange = event => {
  const file = event.target.files[0]
  console.log('file', file)

  if (file) {
    // 检查文件类型
    if (!file.name.toLowerCase().endsWith('.docx')) {
      message.error('请选择 .docx 格式的文件')
      return
    }

    selectedLocalFile.value = file
    selectedLocalFileName.value = file.name

    console.log('本地文件已选择:', file.name)

    // 获取文件的完整路径（在 Electron 中可用）
    const filePath = file.path || file.webkitRelativePath || file.name
    selectedLocalFilePath.value = filePath
    console.log('文件路径信息:', filePath)

    message.success('本地文件选择成功，已在左侧显示预览')
  }
}

// 清空本地文件选择
const clearLocalFile = () => {
  selectedLocalFile.value = null
  selectedLocalFileName.value = ''
  selectedLocalFilePath.value = ''
  formState.scheme_id = null
  showLocalFileSelector.value = false

  // 清空文件输入框
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }

  // 清空本地文件关联（仅在编辑模式下清除已保存的关联）
  if (isEditMode.value && fileId.value) {
    removeLocalFileAssociation(fileId.value)
  }

  message.success('本地文件关联已清除')
}

// 获取本地文件存储数组
const getLocalFilesData = () => {
  const data = localStorage.getItem('local_files_data')
  return data ? JSON.parse(data) : []
}

// 保存本地文件存储数组
const saveLocalFilesData = data => {
  localStorage.setItem('local_files_data', JSON.stringify(data))
}

// 保存本地文件关联
const saveLocalFileAssociation = (documentId, fileName, filePath) => {
  if (documentId && fileName) {
    const localFiles = getLocalFilesData()

    // 检查是否已存在该文档的关联
    const existingIndex = localFiles.findIndex(item => item.id === documentId)

    const fileData = {
      id: documentId,
      fileName: fileName,
      filePath: filePath || fileName,
      createdAt: new Date().toISOString(),
      lastAccessed: new Date().toISOString()
    }

    if (existingIndex >= 0) {
      // 更新现有关联
      localFiles[existingIndex] = fileData
    } else {
      // 添加新关联
      localFiles.push(fileData)
    }

    saveLocalFilesData(localFiles)
    console.log('本地文件关联已保存:', fileData)
  }
}

// 移除本地文件关联
const removeLocalFileAssociation = documentId => {
  if (documentId) {
    const localFiles = getLocalFilesData()
    const filteredFiles = localFiles.filter(item => item.id !== documentId)
    saveLocalFilesData(filteredFiles)
    console.log('本地文件关联已移除:', documentId)
  }
}

// 恢复本地文件关联
const restoreLocalFileAssociation = documentId => {
  if (documentId) {
    const localFiles = getLocalFilesData()
    const fileData = localFiles.find(item => item.id === documentId)

    if (fileData) {
      selectedLocalFileName.value = fileData.fileName
      selectedLocalFilePath.value = fileData.filePath || fileData.fileName

      // 更新最后访问时间
      fileData.lastAccessed = new Date().toISOString()
      const index = localFiles.findIndex(item => item.id === documentId)
      if (index >= 0) {
        localFiles[index] = fileData
        saveLocalFilesData(localFiles)
      }

      console.log('本地文件关联已恢复:', fileData)
    }
  }
}

// 调试：查看本地文件存储状态
const debugLocalFilesData = () => {
  const localFiles = getLocalFilesData()
  console.log('本地文件存储状态:', localFiles)
  return localFiles
}

// 将调试函数暴露到 window 对象（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  window.debugLocalFiles = debugLocalFilesData
}

// 获取版本历史数据
const handleVersionHistory = async () => {
  if (!isEditMode.value || !fileId.value) {
    message.warning('请在编辑现有文件时查看版本历史')
    return
  }

  try {
    const response = await api.biddingDocument.getHistory({ document_id: fileId.value })
    if (response && response.success) {
      if (response.data.length === 0) {
        message.info('当前文件暂无历史版本')
        return
      }
      historyVersions.value = response.data
      versionHistoryVisible.value = true
    } else {
      message.error(response.message || '获取历史版本失败')
    }
  } catch (error) {
    console.error('获取版本历史失败:', error)
    message.error('获取版本历史时发生错误')
  }
}

// 导出为DOCX
const handleExportToDocx = async () => {
  if (!formState.content || !formState.content.content) {
    message.error('没有可导出的内容')
    return
  }

  isExportingDocx.value = true
  try {
    const fileName = formState.name || '招标文件'
    await exportTiptapContentToDocx(formState.content, fileName)
    message.success('导出成功')
  } catch (error) {
    console.error('导出DOCX失败:', error)
    message.error('导出失败: ' + error.message)
  } finally {
    isExportingDocx.value = false
  }
}

// 监听路由参数变化
watch(
  () => route.params.id,
  newId => {
    fileId.value = newId || null
    Object.assign(formState, initialFormState())
    selectedTemplateFull.value = null
    selectedSchemeFull.value = null
    selectedSchemeTemplate.value = null
    // 重置本地文件相关状态
    selectedLocalFile.value = null
    selectedLocalFileName.value = ''
    selectedLocalFilePath.value = ''
    showLocalFileSelector.value = false
    editorVisible.value = false

    fetchAllDocumentTemplates()
    fetchAllTenderSchemes()

    if (newId) {
      loadEditData(newId).then(() => {
        // 编辑数据加载完成后，加载风险评估数据
        loadRiskAssessmentData()
      })
    } else {
      nextTick(() => {
        // editorVisible.value = true
      })
    }
  },
  { immediate: true }
)

// 监听编辑器内容变化，实时检查是否有内容修改
watch(
  () => formState.content,
  () => {
    if (isEditMode.value && editorVisible.value && originalContent.value) {
      // 提取当前字段值并检查是否有变化（包含样式）
      const currentFieldValues = extractFieldValuesFromTiptapJSON(formState.content)
      const hasChanged = checkContentChanged(currentFieldValues)
      contentChanged.value = hasChanged
    }
  },
  { deep: true }
)

onMounted(() => {
  // 设置风险评估WebSocket监听器
  const cleanupRiskListeners = setupRiskAssessmentListeners()

  // 组件卸载时清理事件监听器
  onUnmounted(() => {
    cleanupRiskListeners()
  })

  // 数据获取移到 watch 中，确保每次路由变化时刷新
})
</script>

<style lang="less" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-risk-container {
  display: flex;
  gap: 0;
  min-height: 400px;
}

.editor-container-wrapper {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  min-height: 400px;
  background-color: #fff;
}

/* 三列布局优化 */
.flex {
  gap: 0;
}

/* 当有方案参考面板时，调整主内容区的样式 */
.flex:has(.scheme-reference-panel) .ant-card {
  margin-left: 0;
  margin-right: 0;
}

.editor-placeholder-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
  color: #999;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 2px;
  text-align: center;
}

/* 本地文件选择器样式 */
.local-file-selector {
  margin-bottom: 16px;

  .selected-file-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-top: 8px;
    background-color: #f6f8fa;
    border: 1px solid #e1e8ed;
    border-radius: 4px;
    font-size: 14px;
    color: #1890ff;
  }
}

/* 本地文件选项样式 */
.local-file-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  background-color: #f6f8fa;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  margin: 4px 0;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e6f4ff;
    border-color: #1890ff;
  }

  .local-file-icon {
    color: #1890ff;
    font-size: 16px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .local-file-text {
    font-weight: 500;
    color: #1890ff;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .local-file-description {
    color: #666;
    font-size: 12px;
    flex: 1;
  }
}
</style>
