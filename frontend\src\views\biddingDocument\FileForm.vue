<template>
  <a-spin :spinning="isLoading || isExportingDocx">
    <div class="page-header">
      <a-page-header :title="isEditMode ? '编辑招标文件' : '新建招标文件'" @back="() => $router.go(-1)" />

      <div class="flex gap-x-[10px]">
        <a-button type="primary" @click="handleSubmit" :loading="isLoading" :disabled="isExportingDocx">{{ isEditMode ? '更新文件' : '创建文件' }}</a-button>
        <a-button @click="handleExportToDocx" :loading="isExportingDocx" :disabled="!editorVisible || isLoading || (!isEditMode && !formState.template_id)">
          导出为 DOCX
        </a-button>
        <a-button type="primary" @click="handleVersionHistory" :disabled="!isEditMode">版本历史</a-button>
      </div>
    </div>
    <div class="flex">
      <!-- 方案参考面板 -->
      <SchemeReferencePanel v-if="selectedSchemeFull && selectedSchemeFullContent" :scheme-data="selectedSchemeFull" :scheme-content="selectedSchemeFullContent" />

      <!-- 本地文件预览面板 -->
      <DocxPreview
        v-if="selectedLocalFile || selectedLocalFilePath"
        :file-data="selectedLocalFile"
        :file-name="selectedLocalFileName"
        :file-path="selectedLocalFilePath"
      />

      <a-card :loading="isLoading" style="width: 100%" :bodyStyle="{ height: 'calc(100vh - 180px)', overflow: 'auto' }">
        <a-form ref="formRef" layout="vertical" :model="formState" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
          <a-form-item label="文件名称" name="name">
            <a-input v-model:value="formState.name" placeholder="请输入文件名称" />
          </a-form-item>

          <a-form-item label="招标文件模板" name="template_id">
            <a-tree-select
              v-model:value="formState.template_id"
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder="请选择招标文件模板"
              show-search
              tree-default-expand-all
              :tree-data="allDocumentTemplates"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              tree-node-filter-prop="name"
              :allowClear="!isEditMode"
              :disabled="isEditMode"
              @change="handleTemplateChange"
            />
          </a-form-item>

          <a-form-item label="招标方案" name="scheme_id">
            <a-select
              v-model:value="formState.scheme_id"
              :options="allTenderSchemes.map(s => ({ value: s.id, label: s.name, scheme: s }))"
              placeholder="请选择招标方案（可选，用于快速填写相同字段）"
              show-search
              :filter-option="filterSchemeOption"
              :allowClear="true"
              @change="handleSchemeChange"
            >
              <template #dropdownRender="{ menuNode }">
                <VNodes :vnodes="menuNode" />
                <a-divider style="margin: 4px 0" />
                <div class="local-file-option" @click="selectLocalFileOption">
                  <FileWordOutlined class="local-file-icon" />
                  <span class="local-file-text">选择本地文件 (.docx)</span>
                  <span class="local-file-description">上传并预览本地 Word 文档作为参考</span>
                </div>
              </template>
            </a-select>
            <div class="ant-form-item-explain" style="margin-top: 4px; color: #666; font-size: 12px">
              选择招标方案后，系统会自动将方案中已填写的字段值填充到对应的模板字段中，方便快速创建内容
              <br />
              也可以选择"本地文件(.docx)"来上传并预览本地 Word 文档作为参考
            </div>
            <!-- 本地文件选择器 -->
            <div v-if="showLocalFileSelector" class="local-file-selector">
              <input ref="fileInputRef" type="file" accept=".docx" @change="handleLocalFileChange" style="display: none" />
              <a-button type="dashed" block @click="triggerFileSelector">
                <FileWordOutlined />
                选择本地 Word 文档 (.docx)
              </a-button>
              <div v-if="selectedLocalFile" class="selected-file-info">
                <FileWordOutlined style="color: #1890ff; margin-right: 8px" />
                {{ selectedLocalFileName }}
                <a-button type="link" size="small" @click="clearLocalFile">移除</a-button>
              </div>
              <div v-if="selectedLocalFileName && !selectedLocalFile" class="local-file-tip">
                <a-alert
                  message="本地文件关联信息"
                  :description="`该文件${isEditMode ? '之前' : ''}关联了本地文档：${selectedLocalFileName}。${
                    selectedLocalFilePath ? '正在通过协议加载预览...' : '如需重新预览，请重新选择该文件。'
                  }`"
                  :type="selectedLocalFilePath ? 'success' : 'info'"
                  show-icon
                  style="margin-top: 8px"
                />
              </div>
            </div>
          </a-form-item>

          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="3" />
          </a-form-item>

          <a-form-item label="文件内容" name="content">
            <div class="editor-risk-container">
              <div class="editor-container-wrapper">
                <tiptap-editor
                  v-if="editorVisible"
                  ref="tiptapEditorRef"
                  v-model="formState.content"
                  :editable="!!formState.template_id"
                  :fill-mode="!!formState.template_id"
                  :show-toolbar="false"
                  min-height="400px"
                  placeholder="请选择模板加载内容..."
                  :risk-data="riskAssessmentData?.field_risks || []"
                />
                <div v-else class="editor-placeholder-wrapper">{{ editorPlaceholderText }}</div>
              </div>
            </div>
          </a-form-item>
        </a-form>
      </a-card>

      <!-- 风险面板 -->
      <RiskPanel
        v-if="isEditMode && editorVisible"
        :risk-assessment-data="riskAssessmentData"
        :loading="riskAssessmentLoading"
        @scroll-to-field="handleScrollToField"
        @highlight-field="handleHighlightField"
        @refresh-assessment="handleRefreshRiskAssessment"
        @start-assessment="handleStartRiskAssessment"
      />
    </div>

    <!-- 版本历史组件 -->
    <VersionHistory
      v-model:visible="versionHistoryVisible"
      :history-versions="historyVersions"
      :template-content="selectedTemplateFull?.content"
      :current-field-values="formState.content ? extractFieldValuesFromTiptapJSON(formState.content) : {}"
    />

    <!-- 更新备注对话框 -->
    <a-modal
      v-model:open="updateRemarkModalVisible"
      title="填写更新备注"
      :confirm-loading="isLoading"
      @ok="handleUpdateRemarkSubmit"
      @cancel="handleUpdateRemarkCancel"
      :mask-closable="false"
      width="500px"
      ok-text="确认保存"
      cancel-text="取消"
    >
      <div style="margin-bottom: 16px">
        <a-alert message="检测到文件内容已发生变化" description="请填写本次更新的备注信息，说明具体修改了什么内容。" type="info" show-icon style="margin-bottom: 16px" />
      </div>
      <a-form ref="updateRemarkFormRef" :model="updateRemarkForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="更新备注" name="remark" :rules="[{ required: true, message: '请填写更新备注', trigger: 'blur' }]">
          <a-textarea
            v-model:value="updateRemarkForm.remark"
            placeholder="请详细说明本次修改的内容，方便后续查看历史版本时了解改动情况"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-spin>
</template>

<script setup>
import { ref, reactive, watch, onMounted, onUnmounted, computed } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { FileWordOutlined } from '@ant-design/icons-vue'
import api from '@/api'
import TiptapEditor from '@/components/TiptapEditor/index.vue'
import VersionHistory from '@/components/VersionHistory.vue'
import RiskPanel from '@/components/TiptapEditor/RiskPanel.vue'
import SchemeReferencePanel from '@/views/biddingDocument/components/SchemeReferencePanel.vue'
import DocxPreview from '@/views/biddingDocument/components/DocxPreview.vue'
import { extractFieldValuesFromTiptapJSON, mergeTemplateWithFieldValues, sanitizeTextNodesInTiptapJSON, getDefaultTiptapDoc } from '@/utils/templateUtils'

// 导入组合式函数
import { useFormBase } from '@/composables/useFormBase'
// import { useEditorManager } from '@/composables/useEditorManager'
// import { useRiskAssessment } from '@/composables/useRiskAssessment'
// import { useVersionHistory } from '@/composables/useVersionHistory'
// import { useLocalFileManager } from '@/composables/useLocalFileManager'
// import { useDocxExporter } from '@/composables/useDocxExporter'
// import { useUpdateRemark } from '@/composables/useUpdateRemark'

// 用于渲染 VNode 的组件
const VNodes = {
  props: {
    vnodes: { type: [Array, Object], required: true }
  },
  render() {
    return this.vnodes
  }
}

// 初始表单状态定义
const initialFormState = () => ({
  id: undefined,
  name: '',
  template_id: null,
  scheme_id: null,
  remark: '',
  content: { type: 'doc', content: [{ type: 'paragraph' }] }
})

// 表单验证规则
const validationRules = {
  name: [{ required: true, message: '请输入文件名称', trigger: 'blur' }],
  template_id: [{ required: true, message: '请选择招标文件模板', trigger: 'change' }],
  content: [
    {
      required: true,
      validator: async (_, value) => {
        if (
          !value ||
          !value.content ||
          value.content.length === 0 ||
          (value.content.length === 1 && value.content[0].type === 'paragraph' && (!value.content[0].content || value.content[0].content.length === 0))
        ) {
          return Promise.reject('文件内容不能为空')
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ]
}

// 使用表单基础组合式函数
const {
  formRef,
  isLoading,
  isSubmitting,
  entityId: fileId,
  isEditMode,
  formState,
  rules,
  originalContent,
  contentChanged,
  checkContentChanged,
  resetFormState,
  setOriginalContent,
  handleApiError
} = useFormBase({
  entityType: 'biddingDocument',
  api: api.biddingDocument,
  initialFormState,
  validationRules
})

const handleSuccess = (msg, data) => {
  message.success(msg)
  router.push({ name: 'BiddingDocumentFileList' })
}

const navigateToList = () => {
  router.push({ name: 'BiddingDocumentFileList' })
}

// 临时编辑器状态
const tiptapEditorRef = ref(null)
const editorVisible = ref(false)
const editorContent = ref('')
const selectedTemplateFull = ref(null)
const currentTemplateContent = ref('')
const editorPlaceholderText = ref('请选择模板开始编辑...')

const showEditor = () => { editorVisible.value = true }
const hideEditor = () => { editorVisible.value = false }
const resetEditorState = () => {
  selectedTemplateFull.value = null
  currentTemplateContent.value = ''
  editorContent.value = ''
}

const loadTemplateContent = async () => {
  // 临时实现
  console.log('loadTemplateContent called')
}

const handleTemplateChangeWithConfirm = async () => {
  // 临时实现
  console.log('handleTemplateChangeWithConfirm called')
}

const extractCurrentFieldValues = () => {
  return extractFieldValuesFromTiptapJSON(formState.content)
}

const scrollToField = () => {}
const highlightField = () => {}

// 临时风险评估状态
const riskAssessmentData = ref(null)
const riskAssessmentLoading = ref(false)
const currentRiskAssessmentId = ref(null)

const handleStartRiskAssessment = () => {
  console.log('handleStartRiskAssessment called')
}
const handleRefreshRiskAssessment = () => {
  console.log('handleRefreshRiskAssessment called')
}
const loadRiskAssessmentData = () => {
  console.log('loadRiskAssessmentData called')
}
const setRiskAssessmentId = () => {}

// 临时版本历史状态
const versionHistoryVisible = ref(false)
const historyVersions = ref([])
const isLoadingHistory = ref(false)

const handleVersionHistory = () => {
  console.log('handleVersionHistory called')
}
const closeVersionHistory = () => {
  versionHistoryVisible.value = false
}
const resetVersionHistory = () => {
  historyVersions.value = []
}
const getVersionDetail = () => {}
const restoreToVersion = () => {}
const compareVersions = () => {}

// 临时本地文件管理状态
const fileInputRef = ref(null)
const selectedLocalFile = ref(null)
const selectedLocalFileName = ref('')
const selectedLocalFilePath = ref('')
const showLocalFileSelector = ref(false)
const LOCAL_FILE_OPTION_VALUE = 'local_file'

const selectLocalFileOption = () => {
  console.log('selectLocalFileOption called')
}
const triggerFileSelector = () => {
  console.log('triggerFileSelector called')
}
const handleLocalFileChange = () => {
  console.log('handleLocalFileChange called')
}
const clearLocalFile = () => {
  console.log('clearLocalFile called')
}
const resetLocalFileState = () => {
  selectedLocalFile.value = null
  selectedLocalFileName.value = ''
  selectedLocalFilePath.value = ''
  showLocalFileSelector.value = false
}
const saveLocalFileAssociation = () => {}
const removeLocalFileAssociation = () => {}
const restoreLocalFileAssociation = () => {}

// 临时导出功能状态
const isExportingDocx = ref(false)
const checkExportConditions = () => true
const handleExportToDocx = () => {
  console.log('handleExportToDocx called')
}

// 临时更新备注状态
const updateRemarkModalVisible = ref(false)
const updateRemarkForm = reactive({ remark: '' })
const updateRemarkFormRef = ref(null)
const pendingUpdateData = ref(null)

const handleSubmitWithRemarkCheck = () => {
  console.log('handleSubmitWithRemarkCheck called')
}
const showUpdateRemarkModal = () => {
  updateRemarkModalVisible.value = true
}
const hideUpdateRemarkModal = () => {
  updateRemarkModalVisible.value = false
}
const handleUpdateRemarkSubmit = () => {
  console.log('handleUpdateRemarkSubmit called')
}
const handleUpdateRemarkCancel = () => {
  console.log('handleUpdateRemarkCancel called')
}

// 简单的提交方法
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    message.success('表单验证通过')
    console.log('表单数据:', formState)
  } catch (error) {
    message.error('请填写所有必填项')
  }
}

// 基础数据加载方法
const loadDocumentTemplates = async () => {
  try {
    const response = await api.documentTemplate.getAll()
    if (response && response.success) {
      allDocumentTemplates.value = response.data || []
    }
  } catch (error) {
    console.error('加载文档模板失败:', error)
  }
}

const loadTenderSchemes = async () => {
  try {
    const response = await api.tenderScheme.getAll()
    if (response && response.success) {
      allTenderSchemes.value = response.data || []
    }
  } catch (error) {
    console.error('加载招标方案失败:', error)
  }
}

const loadEditData = async (id) => {
  try {
    isLoading.value = true
    const response = await api.biddingDocument.getById({ id })
    if (response && response.success) {
      Object.assign(formState, response.data)
      setOriginalContent(response.data.content)
      console.log('编辑数据加载成功')
    }
  } catch (error) {
    console.error('加载编辑数据失败:', error)
    message.error('加载数据失败')
  } finally {
    isLoading.value = false
  }
}

const handleTemplateChange = (templateId) => {
  console.log('模板变更:', templateId)
  // 这里可以添加模板变更逻辑
}

// 其他状态
const allDocumentTemplates = ref([])
const allTenderSchemes = ref([])
const selectedSchemeFull = ref(null) // 存储选中的完整方案对象，包含content (字段值)
const selectedSchemeTemplate = ref(null) // 存储选中方案关联的模板对象

// 计算方案参考内容（合并方案字段值与其关联的模板内容）
const selectedSchemeFullContent = computed(() => {
  if (!selectedSchemeFull.value || !selectedSchemeTemplate.value) {
    return null
  }

  // 获取方案的字段值
  let schemeFieldValues = {}
  try {
    if (selectedSchemeFull.value.content) {
      schemeFieldValues = typeof selectedSchemeFull.value.content === 'string' ? JSON.parse(selectedSchemeFull.value.content) : selectedSchemeFull.value.content
    }
  } catch (error) {
    console.error('解析方案字段值失败:', error)
    return null
  }

  // 获取方案关联的模板内容
  const templateContent = selectedSchemeTemplate.value.content
  if (!templateContent) {
    return null
  }

  try {
    // 合并模板内容与方案字段值
    const mergedContent = mergeTemplateWithFieldValues(templateContent, schemeFieldValues)
    sanitizeTextNodesInTiptapJSON(mergedContent)
    return mergedContent
  } catch (error) {
    console.error('合并方案参考内容失败:', error)
    return null
  }
})

// 提交处理函数（需要在 useUpdateRemark 之前定义）
async function handleSubmit(payload) {
  try {
    isSubmitting.value = true

    let response
    if (isEditMode.value) {
      response = await api.biddingDocument.update(payload)
    } else {
      response = await api.biddingDocument.create(payload)
    }

    if (response && response.success) {
      const entityName = '招标文件'
      const action = isEditMode.value ? '更新' : '创建'
      handleSuccess(`${entityName}${action}成功！`, response.data)

      // 保存本地文件关联（如果有）
      if (selectedLocalFile.value && response.data?.id) {
        saveLocalFileAssociation(response.data.id, selectedLocalFileName.value, selectedLocalFilePath.value)
      }

      return true
    } else {
      handleApiError(response)
      return false
    }
  } catch (error) {
    handleApiError(error)
    return false
  } finally {
    isSubmitting.value = false
  }
}

// 模板选择处理（重复定义，已删除）
// const handleTemplateChange = async templateId => {
//   if (!templateId) {
//     resetEditorState()
//     return
//   }

//   try {
//     const response = await api.biddingDocumentTemplate.getById({ id: templateId })
//     // 模板处理逻辑已移到组合式函数中
//   } catch (error) {
//     console.error('加载模板内容时出错:', error)
//   }
// }

// 重复的 loadEditData 方法已删除，使用上面定义的版本

// 方案选择处理
const handleSchemeChange = async schemeId => {
  if (schemeId === LOCAL_FILE_OPTION_VALUE) {
    selectLocalFileOption()
    return
  }

  if (!schemeId) {
    selectedSchemeFull.value = null
    selectedSchemeTemplate.value = null
    return
  }

  try {
    const response = await api.tenderScheme.getById({ id: schemeId })
    if (response.success && response.data) {
      selectedSchemeFull.value = response.data

      // 加载方案关联的模板
      if (response.data.template_id) {
        const templateRes = await api.tenderSchemeTemplate.getById({ id: response.data.template_id })
        if (templateRes.success && templateRes.data) {
          selectedSchemeTemplate.value = templateRes.data
        }
      }
    }
  } catch (error) {
    console.error('加载方案数据失败:', error)
    message.error('加载方案数据失败: ' + (error.message || '未知错误'))
  }
}

// 导出功能
const handleExportDocx = async () => {
  await handleExportToDocx('招标文件')
}

// 表单提交处理
const handleFormSubmit = async () => {
  try {
    await formRef.value.validate()

    // 提取当前字段值
    const currentFieldValues = extractCurrentFieldValues()

    // 构建提交数据
    const payload = {
      name: formState.name,
      template_id: formState.template_id,
      scheme_id: formState.scheme_id,
      remark: formState.remark,
      content: currentFieldValues
    }

    if (isEditMode.value) {
      payload.id = formState.id
    }

    // 使用更新备注检查提交
    await handleSubmitWithRemarkCheck(payload, currentFieldValues)
  } catch (error) {
    console.log('表单验证失败:', error)
  }
}

// 组件挂载和清理
onMounted(async () => {
  // 加载基础数据
  await Promise.all([
    loadDocumentTemplates(),
    loadTenderSchemes()
  ])

  // 设置风险评估监听器
  const cleanupRiskAssessment = setupRiskAssessmentListeners()

  // 如果是编辑模式，加载数据
  if (isEditMode.value && fileId.value) {
    await loadEditData(fileId.value)
    await loadRiskAssessmentData()
  } else {
    showEditor()
  }

  // 清理函数
  onUnmounted(() => {
    cleanupRiskAssessment()
  })
})

// 过滤函数
const filterSchemeOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 将调试函数暴露到 window 对象（仅开发环境）
if (process.env.NODE_ENV === 'development') {
  window.debugLocalFiles = debugLocalFilesData
}

// 监听路由参数变化
watch(
  () => route.params.id,
  newId => {
    entityId.value = newId || null
    resetFormState()
    resetEditorState()
    resetLocalFileState()
    hideEditor()

    // 重新加载基础数据
    loadDocumentTemplates()
    loadTenderSchemes()

    if (newId) {
      loadEditData(newId).then(() => {
        // 编辑数据加载完成后，加载风险评估数据
        loadRiskAssessmentData()
      })
    } else {
      showEditor()
    }
  },
  { immediate: true }
)

// 监听编辑器内容变化，实时检查是否有内容修改
watch(
  () => formState.content,
  () => {
    if (isEditMode.value && editorVisible.value && originalContent.value) {
      // 提取当前字段值并检查是否有变化
      const currentFieldValues = extractCurrentFieldValues()
      const hasChanged = checkContentChanged(currentFieldValues)
      contentChanged.value = hasChanged
    }
  },
  { deep: true }
)
</script>

<style lang="less" scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-risk-container {
  display: flex;
  gap: 0;
  min-height: 400px;
}

.editor-container-wrapper {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  min-height: 400px;
  background-color: #fff;
}

/* 三列布局优化 */
.flex {
  gap: 0;
}

/* 当有方案参考面板时，调整主内容区的样式 */
.flex:has(.scheme-reference-panel) .ant-card {
  margin-left: 0;
  margin-right: 0;
}

.editor-placeholder-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
  color: #999;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 2px;
  text-align: center;
}

/* 本地文件选择器样式 */
.local-file-selector {
  margin-bottom: 16px;

  .selected-file-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    margin-top: 8px;
    background-color: #f6f8fa;
    border: 1px solid #e1e8ed;
    border-radius: 4px;
    font-size: 14px;
    color: #1890ff;
  }
}

/* 本地文件选项样式 */
.local-file-option {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  background-color: #f6f8fa;
  border: 1px solid #e1e8ed;
  border-radius: 4px;
  margin: 4px 0;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e6f4ff;
    border-color: #1890ff;
  }

  .local-file-icon {
    color: #1890ff;
    font-size: 16px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .local-file-text {
    font-weight: 500;
    color: #1890ff;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .local-file-description {
    color: #666;
    font-size: 12px;
    flex: 1;
  }
}
</style>
