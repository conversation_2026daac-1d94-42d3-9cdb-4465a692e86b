// frontend/src/store/modules/auth.js
import { arr } from '@/utils/routers.js'
let arra = JSON.parse(JSON.stringify(arr))
import Router from '@/router/index.js'
import AppSiderVue from '@/layouts/AppSider.vue'
import Menu from '@/layouts/Menu.vue'
import api from '@/api';

const modules = import.meta.glob('./../../views/**/*.vue')

const state = {
  token: localStorage.getItem('token') || null,
  user: JSON.parse(localStorage.getItem('user')) || null,
  isAuthenticated: !!localStorage.getItem('token'),
  userroute: [],
  isDynamicRoutesAdded: false, // 是否拉取路由
  routes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    localStorage.setItem('addRoutes', JSON.stringify(routes))
  },
  SET_TOKEN(state, token) {
    state.token = token
    state.isAuthenticated = !!token
    if (token) {
      localStorage.setItem('token', token)
    } else {
      localStorage.removeItem('token')
    }
  },
  SET_USER(state, user) {
    state.user = user
    if (user) {
      localStorage.setItem('user', JSON.stringify(user))
    } else {
      localStorage.removeItem('user')
    }
  },
  LOGOUT(state) {
    state.token = null
    state.user = null
    state.isAuthenticated = false
    state.userroute = []
    state.routes = []
    state.isDynamicRoutesAdded = false
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    localStorage.removeItem('addRoutes')
    localStorage.removeItem('isDynamicRoutesAdded')
    localStorage.removeItem('userroute')
  },
  SET_USER_ROUTER(state, userroute) {
    state.userroute = userroute
    localStorage.setItem('userroute', JSON.stringify(userroute))
  },
  SET_ISDY_ROUTER(state, isDynamicRoutesAdded) {
    localStorage.setItem('isDynamicRoutesAdded', isDynamicRoutesAdded)
    state.isDynamicRoutesAdded = isDynamicRoutesAdded
  }
}

const actions = {
  /**
   * 用户登录
   * @param {Object} credentials - 登录凭据 { username, password }
   */
  async login({ commit }, credentials) {
    try {
      console.log('开始用户登录:', credentials.username)

      // 调用HTTP API进行登录
      const response = await api.user.login({ username: credentials.username, password: credentials.password })

      if (response.success && response.data) {
        const user = response.data

        // 生成临时token或使用后端返回的token
        const token = response.token || 'fake-jwt-token-' + Date.now()

        commit('SET_TOKEN', token)
        commit('SET_USER', user)

        console.log('用户登录成功:', user)
        return { success: true }
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      commit('LOGOUT')
      console.error('登录失败:', error.message)
      throw error
    }
  },

  /**
   * 用户注册
   * @param {Object} userData - 用户注册数据
   */
  async register({ commit }, userData) {
    try {
      console.log('开始用户注册:', userData.username)

      const response = await api.user.register(userData)

      if (response.success && response.data) {
        console.log('用户注册成功:', userData.username)
        // 注册后通常不自动登录，仅返回用户信息
        return response.data
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      console.error('注册失败:', error.message)
      throw error
    }
  },

  /**
   * 获取当前用户信息
   * 用于应用启动时验证token并恢复用户状态
   */
  async fetchCurrentUser({ commit, state }) {
    if (!state.token) {
      console.log('没有token，跳过获取用户信息')
      return null
    }

    try {
      // 如果localStorage中有用户信息且token有效，直接返回
      // 实际应用中应该有后端接口验证token有效性
      if (state.user) {
        console.log('从本地存储恢复用户信息:', state.user.username)
        return state.user
      }

      // TODO: 后续可添加获取当前用户的API接口
      // const response = await api.user.getCurrentUser();
      // if (response.success) {
      //   commit('SET_USER', response.data);
      //   return response.data;
      // }

      commit('LOGOUT')
      return null
    } catch (error) {
      console.error('获取用户信息失败:', error.message)
      commit('LOGOUT')
      return null
    }
  },

  /**
   * 用户登出
   */
  logout({ commit }) {
    console.log('用户登出')
    commit('LOGOUT')
    // TODO: 可选择通知后端token已失效
    // await api.user.logout();
  },

  /**
   * 设置用户路由
   */
  setuserroute({ commit }, userroute) {
    commit('SET_USER_ROUTER', userroute)
  },
  setisDynamicRoutesAdded({ commit }, isDynamicRoutesAdded) {
    commit('SET_ISDY_ROUTER', isDynamicRoutesAdded)
  },
  // 获取用户权限
  GetInfo({ commit, state }) {
    return new Promise(async (resolve, reject) => {
      let userId = state.user.role_id;
      console.log('用户id', userId)
      // let result = await api.getRoleById(userId)
      let result = await api.role.getById({id:userId})
      console.log('用户result', result.permissions)
      let permissions = ''
      if (Array.isArray(result.permissions) && result.permissions[0] == 'all') {
        permissions = [
          'TenderSchemeModule',
          'TenderSchemeTemplateList',
          'TenderSchemeTemplateCreate',
          'TenderSchemeTemplateEdit',
          'TenderSchemeProjectList',
          'TenderSchemeProjectCreate',
          'TenderSchemeProjectEdit',
          'TenderSchemeProjectView',
          'BiddingDocumentModule',
          'BiddingDocumentTemplateList',
          'BiddingDocumentTemplateNew',
          'BiddingDocumentTemplateEdit',
          'BiddingDocumentFileList',
          'BiddingDocumentFileNew',
          'BiddingDocumentFileEdit',
          'BiddingDocumentFileView',
          'SystemManagement',
          'SystemUsers',
          'SystemRoles',
          'SystemTemplateFieldList',
          'SystemTemplateFieldCreate',
          'SystemTemplateFieldEdit'
        ]
      } else {
        permissions = JSON.parse(result.permissions)
      }
      let userouter = permissions
      if (userouter.includes('TenderSchemeTemplateList')) {
        userouter.push('TenderSchemeTemplateCreate', 'TenderSchemeTemplateEdit')
      } 
      if (userouter.includes('TenderSchemeProjectList')) {
        // 招标方案-方案制作
        userouter.push('TenderSchemeProjectCreate', 'TenderSchemeProjectEdit', 'TenderSchemeProjectView')
      } 
      if (userouter.includes('BiddingDocumentTemplateList')) {
        // 招标文件-模板管理
        userouter.push('BiddingDocumentTemplateNew', 'BiddingDocumentTemplateEdit')
      } 
      if (userouter.includes('BiddingDocumentFileList')) {
        userouter.push('BiddingDocumentFileNew', 'BiddingDocumentFileEdit', 'BiddingDocumentFileView')
      } 
      if (userouter.includes('SystemTemplateFieldList')) {
        userouter.push('SystemTemplateFieldCreate', 'SystemTemplateFieldEdit')
      }
      await commit('SET_USER_ROUTER', userouter)
      await commit('SET_ISDY_ROUTER', true)
      resolve(userouter)
    }).catch(error => {
      reject(error)
    })
  },
  GenerateRoutes({ commit, state }, res) {
    return new Promise(resolve => {
      // 请求路由数据
      const filteredRoutes = filterTree(arra[0].children, state.userroute)
      for(let i = 0; i < filteredRoutes.length; i++){
        if(filteredRoutes[i].name == "TenderSchemeModule"){
          for(let j = 0; j < filteredRoutes[i].children.length; j++){
            if(filteredRoutes[i].children[j].name == "TenderSchemeTemplateList"){
              filteredRoutes[i].redirect =  { name: 'TenderSchemeTemplateList' }
              break
            }
            if(filteredRoutes[i].children[j].name == "TenderSchemeProjectList"){
              filteredRoutes[i].redirect =  { name: 'TenderSchemeProjectList' }
              break
            }
          }
        }
        if(filteredRoutes[i].name == "BiddingDocumentModule"){
          for(let j = 0; j < filteredRoutes[i].children.length; j++){
            if(filteredRoutes[i].children[j].name == "BiddingDocumentTemplateList"){
              filteredRoutes[i].redirect =  { name: 'BiddingDocumentTemplateList' }
              break
            }
            if(filteredRoutes[i].children[j].name == "BiddingDocumentFileList"){
              filteredRoutes[i].redirect =  { name: 'BiddingDocumentFileList' }
              break
            }
          }
        }
       if(filteredRoutes[i].name == "SystemManagement"){
          for(let j = 0; j < filteredRoutes[i].children.length; j++){
            if(filteredRoutes[i].children[j].name == "SystemUsers"){
              filteredRoutes[i].redirect =  { name: 'SystemUsers' }
              break
            }
            if(filteredRoutes[i].children[j].name == "SystemRoles"){
              filteredRoutes[i].redirect =  { name: 'SystemRoles' }
              break
            }
            if(filteredRoutes[i].children[j].name == "SystemTemplateFieldList"){
              filteredRoutes[i].redirect =  { name: 'SystemTemplateFieldList' }
              break
            }
          }
        }
      }
      // const sortedRoutes = filteredRoutes.sort((a, b) => {
      //   const order = {
      //     '/tender-scheme': 1,
      //     '/bidding-document': 2,
      //     '/system': 3
      //   };
      //   const pathA = a.path;
      //   const pathB = b.path;
      //   const priorityA = order[pathA] || 4;  
      //   const priorityB = order[pathB] || 4;
      //   return priorityA - priorityB; 
      // });
      const rewriteRoutes = filterAsyncRouter(filteredRoutes, false, true)
      commit('SET_ROUTES', rewriteRoutes)
      console.log('过滤出来的', filteredRoutes)
      resolve(rewriteRoutes)
    })
  }
}

const getters = {
  isAuthenticated: state => state.isAuthenticated,
  currentUser: state => state.user,
  userRole: state => (state.user && state.user.role_name ? state.user.role_name : null)
}

function filterTreess(arr, paths) {
  if (!arr || !arr.length || !paths || !paths.length) return []

  const result = []
  const topLevelNames = arr.map(item => item.name)

  // 分离顶级节点和非顶级节点
  const topLevelPaths = paths.filter(name => topLevelNames.includes(name))
  const childPaths = paths.filter(name => !topLevelNames.includes(name))

  // 深拷贝
  const deepClone = obj => {
    if (!obj) return obj
    // const clone = { ...obj };
    const clone = JSON.parse(JSON.stringify(obj))
    if (Array.isArray(obj.children)) {
      clone.children = obj.children.map(child => deepClone(child))
    }
    return clone
  }

  // 处理顶级节点
  for (const topName of topLevelPaths) {
    const topMatch = arr.find(item => item.name === topName)
    if (topMatch) {
      const clonedMatch = deepClone(topMatch)

      // 如果这个顶级节点有对应的子节点在childPaths中，则精确匹配
      const relatedChildren = childPaths.filter(childName => clonedMatch.children && clonedMatch.children.some(child => child.name === childName))

      if (relatedChildren.length > 0) {
        // 精确匹配模式，保留所有属性
        clonedMatch.children = clonedMatch.children.filter(child => relatedChildren.includes(child.name)).map(child => deepClone(child))
      }

      result.push(clonedMatch)
    }
  }

  // 处理仅子节点的情况（没有对应的顶级节点在paths中）
  for (const childName of childPaths) {
    // 检查这个子节点是否已经被包含在之前的结果中
    const alreadyIncluded = result.some(top => top.children && top.children.some(child => child.name === childName))

    if (!alreadyIncluded) {
      // 查找这个子节点的父级
      for (const topItem of arr) {
        if (topItem.children) {
          const childMatch = topItem.children.find(child => child.name === childName)
          if (childMatch) {
            // 确保父级还没被添加
            const parentExists = result.some(item => item.name === topItem.name)
            if (!parentExists) {
              const clonedParent = deepClone(topItem)
              clonedParent.children = [deepClone(childMatch)]
              result.push(clonedParent)
            }
            break
          }
        }
      }
    }
  }

  return result
}

function filterTree(arr, paths) {
  if (!arr || !arr.length || !paths || !paths.length) return []

  const result = []
  const topLevelNames = arr.map(item => item.name)

  // 分离顶级节点和非顶级节点
  const topLevelPaths = paths.filter(name => topLevelNames.includes(name))
  const childPaths = paths.filter(name => !topLevelNames.includes(name))

  // 深拷贝
  const deepClone = obj => {
    if (!obj) return obj
    const clone = JSON.parse(JSON.stringify(obj))
    if (Array.isArray(obj.children)) {
      clone.children = obj.children.map(child => deepClone(child))
    }
    return clone
  }

  // 1. 首先处理所有可能包含目标子节点的顶级路由
  const parentRoutes = arr.filter(route => route.children && route.children.some(child => paths.includes(child.name)))

  // 2. 克隆这些父路由并过滤子节点
  parentRoutes.forEach(parent => {
    const clonedParent = deepClone(parent)
    clonedParent.children = clonedParent.children.filter(child => paths.includes(child.name))

    // 确保父路由本身也在允许的路径中，或者它有允许的子路由
    if (topLevelPaths.includes(parent.name) || clonedParent.children.length > 0) {
      result.push(clonedParent)
    }
  })

  // 3. 处理顶级路由（没有子路由但本身在路径中的情况）
  topLevelPaths.forEach(topName => {
    if (!result.some(r => r.name === topName)) {
      const topMatch = arr.find(item => item.name === topName)
      if (topMatch) {
        result.push(deepClone(topMatch))
      }
    }
  })

  return result
}

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter(route => {
    if (type && route.children) {
      route.children = filterChildren(route.children)
    }
    if (route.component) {
      // 组件特殊处理
      if (route.component === 'layouts/AppSider') {
        route.component = AppSiderVue
      } else if (route.component === 'layouts/Menu') {
        route.component = Menu
      } else {
        route.component = loadView(route.component)
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type)
    } else {
      delete route['children']
      delete route['redirect']
    }
    return true
  })
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = []
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === 'ParentView' && !lastRouter) {
        el.children.forEach(c => {
          c.path = el.path + '/' + c.path
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c))
            return
          }
          children.push(c)
        })
        return
      }
    }
    if (lastRouter) {
      el.path = lastRouter.path + '/' + el.path
    }
    children = children.concat(el)
  })
  return children
}

function loadView(view) {
  let res
  for (const path in modules) {
    const dir = path.split('views/')[1].split('.vue')[0]
    if (dir === view) {
      res = () => modules[path]()
    }
  }
  return res
}

export default {
  namespaced: true, // 启用命名空间，避免模块间命名冲突
  state,
  mutations,
  actions,
  getters
}
