<template>
  <a-layout id="app-menu">
    <!-- <a-layout-sider
      theme="light"
      class="layout-sider"
      width="200"
    >
      <a-menu 
        theme="light" 
        mode="inline" 
        :selectedKeys="[selectedKey]"
        :openKeys="openKeys"
        @click="changeMenu"
        @openChange="onOpenChange"
      >
        <template v-for="menuItem in subMenu" :key="menuItem.name">
          <template v-if="menuItem.children && menuItem.children.length > 0 && !menuItem.meta.hideChildrenInMenu">
            <a-sub-menu :key="menuItem.name">
              <template #title>
                <span>
                  <component v-if="menuItem.meta.antIcon" :is="menuItem.meta.antIcon" />
                  <icon-font v-else-if="menuItem.meta.icon" :type="menuItem.meta.icon" />
                  {{ menuItem.meta.title }}
                </span>
              </template>
              <a-menu-item v-for="childItem in menuItem.children.filter(c => c.meta && c.meta.title && !c.meta.hidden)" :key="childItem.name">
                <router-link :to="{ name: childItem.name, params: childItem.params }">
                  <component v-if="childItem.meta.antIcon" :is="childItem.meta.antIcon" />
                  <icon-font v-else-if="childItem.meta.icon" :type="childItem.meta.icon" />
                  {{ childItem.meta.title }}
                </router-link>
              </a-menu-item>
            </a-sub-menu>
          </template>
          <template v-else>
            <a-menu-item :key="menuItem.name" v-if="menuItem.meta.showInMenu !== false">
              <router-link :to="{ name: menuItem.name, params: menuItem.params }">
                <component v-if="menuItem.meta.antIcon" :is="menuItem.meta.antIcon" />
                <icon-font v-else-if="menuItem.meta.icon" :type="menuItem.meta.icon" />
                <span>{{ menuItem.meta.title }}</span>
          </router-link>
        </a-menu-item>
          </template>
        </template>
      </a-menu>
    </a-layout-sider> -->
    <a-layout>
      <a-breadcrumb class="a-breadcrumb" style="margin-bottom: 16px;">
          <a-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index">
            <!-- <router-link v-if="item.name && index < breadcrumbs.length -1" :to="{ name: item.name }">{{ item.title }}</router-link> -->
            <!-- <router-link class="demoa" v-if="item.name && index < breadcrumbs.length -1" :to="{ name: item.name }">{{ item.title }}</router-link> -->
            <RouterLink class="demoa" v-if="item.name && index < breadcrumbs.length -1" :to="{ name: item.name }">{{ item.title }}</RouterLink>
            <span v-else>{{ item.title }}</span>
          </a-breadcrumb-item>
      </a-breadcrumb>
      <a-layout-content class="app-menu-content">
         <!-- 面包屑导航 -->
        <!-- <a-breadcrumb style="margin-bottom: 16px;">
          <a-breadcrumb-item v-for="(item, index) in breadcrumbs" :key="index">
            <router-link v-if="item.name && index < breadcrumbs.length -1" :to="{ name: item.name }">{{ item.title }}</router-link>
            <span v-else>{{ item.title }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb> -->
        <router-view v-slot="{ Component, route }">
          <keep-alive v-if="route.meta.keepAlive">
            <component :is="Component" :key="route.name" />
          </keep-alive>
          <component :is="Component" v-if="!route.meta.keepAlive" :key="route.name" />
        </router-view>
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup>
import { ref, watch, computed, onMounted, } from 'vue';
import { useRouter, useRoute, RouterLink } from 'vue-router';
import { useStore } from 'vuex';
// import constantRouterMap from '@/router/routerMap'; // 导入路由配置
import constantRouterMap from '@/router/index.js'; // 导入路由配置
// 导入需要的 Ant Design 图标，或者在 AppSider.vue 中已导入的图标也可以考虑共享
import { FileTextOutlined, FolderOpenOutlined, PlusOutlined, EditOutlined, EyeOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  id: { // id 现在是顶级菜单项的 name, e.g., 'TenderSchemeModule'
    type: String,
    default: ''
  }
});

const store = useStore();

const router = useRouter();
const route = useRoute();

const selectedKey = ref('');
const openKeys = ref([]);
const rootSubmenuKeys = ref([]); // 用于管理只展开一个子菜单

// 根据 props.id (顶级路由的name) 动态生成子菜单
const subMenu = computed(() => {
  if (!props.id) return [];
  // const mainLayoutRoute = constantRouterMap.find(r => r.path === '/');
  // const mainLayoutRoute = constantRouterMap.getRoutes().find(r => r.path === '/');
  const mainLayoutRoute = router.getRoutes().find(r => r.path === '/');
  if (!mainLayoutRoute || !mainLayoutRoute.children) return [];
  
  const parentRoute = mainLayoutRoute.children.find(child => child.name === props.id);
  if (parentRoute && parentRoute.children) {
    rootSubmenuKeys.value = parentRoute.children
      .filter(item => item.children && item.children.length > 0 && item.meta && item.meta.title && !item.path.includes('/: '.trim())) // 修正这里的空格问题
      .map(item => item.name);

    return parentRoute.children
      .filter(sub => sub.meta && sub.meta.title && !sub.meta.hidden && !sub.path.includes('/: '.trim())) // 修正这里的空格问题
      .map(child => ({
        ...child,
        meta: {
          ...child.meta,
          antIcon: child.meta.antIcon ? getAntIconComponent(child.meta.antIcon) : null
        }
      }));
  }
  return [];
});

function getAntIconComponent(iconName) {
  const icons = {
    FileTextOutlined, FolderOpenOutlined, PlusOutlined, EditOutlined, EyeOutlined
  };
  return icons[iconName];
}

// 面包屑数据
const breadcrumbs = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title);
  const breadcrumbItems = [];

  // 添加顶层菜单项 (从 props.id 获取 title)
  // const parentRoute = constantRouterMap.find(r => r.path === '/')?.children?.find(child => child.name === props.id);
  const parentRoute = router.getRoutes().find(r => r.path === '/')?.children?.find(child => child.name === props.id);
  if (parentRoute && parentRoute.meta && parentRoute.meta.title) {
    breadcrumbItems.push({ title: parentRoute.meta.title }); // 不可点击，或指向父路由首页
    // breadcrumbItems.push({ title: parentRoute.meta.title, name:parentRoute.name }); // 不可点击，或指向父路由首页
  }
  
  console.log("1111111111111111111",parentRoute)

  // 检查当前路由的 meta.breadcrumb
  if (route.meta && route.meta.breadcrumb) {
    route.meta.breadcrumb.forEach(crumb => {
      breadcrumbItems.push({ name: crumb.name, title: crumb.title });
    });
  } else if (matched.length > 0) {
    // 如果没有自定义面包屑，尝试从路由匹配生成
    // 跳过第一个匹配项，因为它通常是AppSider或Menu本身
    const relevantMatches = matched.slice(1); 
    relevantMatches.forEach(item => {
        // 只添加在当前子菜单下的项
        if(item.path.startsWith(parentRoute.path)){
            breadcrumbItems.push({ name: item.name, title: item.meta.title });
        }
    });
     // 如果只有一个层级（父级菜单项），且当前路由自身有标题，则添加当前路由标题
    if (relevantMatches.length === 0 && route.meta && route.meta.title && breadcrumbItems.length ===1 && breadcrumbItems[0].title !== route.meta.title) {
        breadcrumbItems.push({ title: route.meta.title });
    }
  }
  // 确保最后一个是当前页面标题，并且不是重复的
  if (route.meta && route.meta.title) {
      const lastBreadcrumb = breadcrumbItems[breadcrumbItems.length -1];
      if (!lastBreadcrumb || lastBreadcrumb.title !== route.meta.title) {
          if (breadcrumbItems.find(b => b.title === route.meta.title && !b.name)) {
              // 如果已经存在一个不可点击的同名项，则不再添加
          } else {
              breadcrumbItems.push({ title: route.meta.title });
          }
      }
  }
  // console.log("00",breadcrumbItems)
  // 去重，优先保留带name（可跳转）的
  const uniqueCrumbs = [];
  const titles = new Set();
  for (let i = breadcrumbItems.length - 1; i >= 0; i--) {
      const crumb = breadcrumbItems[i];
      if (!titles.has(crumb.title)) {
          uniqueCrumbs.unshift(crumb);
          titles.add(crumb.title);
      } else if (crumb.name && !uniqueCrumbs.find(uc => uc.title === crumb.title && uc.name)) {
          const index = uniqueCrumbs.findIndex(uc => uc.title === crumb.title);
          if (index !== -1) uniqueCrumbs.splice(index, 1);
          uniqueCrumbs.unshift(crumb);
      }
  }
  // console.log("11",uniqueCrumbs)
  return uniqueCrumbs;
});

// 监听路由变化以更新 selectedKey 和 openKeys
watch(
  () => route.name,
  (newRouteName) => {
    console.log("333",newRouteName)
    // selectedKey.value = newRouteName;
    // // 确定打开的子菜单 (如果适用)
    // const currentRoute = findRouteByName(subMenu.value, newRouteName);
    // if (currentRoute && currentRoute.parent) {
    //   openKeys.value = [currentRoute.parent.name];
    // } else if (currentRoute) {
    //   // 如果是一级子菜单项，则清空openKeys
    //   openKeys.value = [];
    // } else {
    //     // 如果当前路由不在子菜单中，尝试根据路径匹配
    //     const pathSegments = route.path.split('/').filter(Boolean);
    //     if (pathSegments.length > 2) {
    //         const parentNameGuess = subMenu.value.find(m => m.path.split('/').pop() === pathSegments[1])?.name;
    //         if(parentNameGuess) openKeys.value = [parentNameGuess];
    //     }
    // }
    // // 新增/编辑 模板字段 保持模板字段管理高亮
    // if(selectedKey.value == "SystemTemplateFieldCreate" || selectedKey.value == "SystemTemplateFieldEdit"){
    //   selectedKey.value = "SystemTemplateFieldList"
    // }
    // if(selectedKey.value == "BiddingDocumentFileNew" ||
    //   selectedKey.value == 'BiddingDocumentFileEdit' ||
    //   selectedKey.value == 'BiddingDocumentFileView'){
    //   selectedKey.value = "BiddingDocumentFileList"
    // }
    // if(selectedKey.value == "BiddingDocumentTemplateNew" ||
    //   selectedKey.value == "BiddingDocumentTemplateEdit" 
    // ){
    //   selectedKey.value = "BiddingDocumentTemplateList"
    // }
    // if(selectedKey.value == "TenderSchemeTemplateCreate" ||
    //   selectedKey.value == "TenderSchemeTemplateEdit" 
    // ){
    //   selectedKey.value = "TenderSchemeTemplateList"
    // }
    // if(selectedKey.value == "TenderSchemeProjectCreate" ||
    //   selectedKey.value == "TenderSchemeProjectEdit"  ||
    //   selectedKey.value == "TenderSchemeProjectView"  
    // ){
    //   selectedKey.value = "TenderSchemeProjectList"
    // }
  },
  { immediate: true }
);

const ha = async ()=>{
  try {
    await store.dispatch('auth/logout');
    router.push('/login'); 
  } catch (err) {
  
  } finally {
  
  }
}

function findRouteByName(menuItems, name) {
  for (const item of menuItems) {
    if (item.name === name) return { ...item, parent: null };
    if (item.children) {
      for (const child of item.children) {
        if (child.name === name) return { ...child, parent: item };
      }
    }
  }
  return null;
}


// 初始化和当顶级菜单切换时 (props.id 变化)
watch(() => props.id, (newTopMenuId) => {
  if (newTopMenuId) {
    selectedKey.value = ''; // 清空之前的选择
    openKeys.value = [];
    console.log("111",selectedKey.value)
    router.push({ name: route.name });
    return
    // 导航到该顶级菜单的默认子路由 (通常是 redirect 指定的第一个子路由)
    // const parentRoute = constantRouterMap.find(r => r.path === '/')?.children?.find(child => child.name === newTopMenuId);
    // const parentRoute = router.getRoutes().find(r => r.path === '/')?.children?.find(child => child.name === newTopMenuId);
    if (parentRoute && parentRoute.redirect && parentRoute.redirect.name) {
      router.push({ name: parentRoute.redirect.name });
      selectedKey.value = parentRoute.redirect.name;
    } else if (subMenu.value.length > 0) {
      // 如果没有redirect，导航到第一个可用的子菜单项
      let firstSubItem = subMenu.value[0];
      if(firstSubItem.children && firstSubItem.children.length > 0 && firstSubItem.meta && !firstSubItem.meta.hideChildrenInMenu) {
        firstSubItem = firstSubItem.children.filter(c => c.meta && c.meta.title && !c.meta.hidden)[0] || firstSubItem;
      }
      if (firstSubItem && firstSubItem.name){
        router.push({ name: firstSubItem.name });
        selectedKey.value = firstSubItem.name;
      }
    }
  }
}, { immediate: true });

onMounted(() => {
  // 初始加载时，确保 selectedKey 和 openKeys 与当前路由匹配
  if (route.name) {
    selectedKey.value = route.name;
    const currentRouteInfo = findRouteByName(subMenu.value, route.name);
    if (currentRouteInfo && currentRouteInfo.parent) {
      openKeys.value = [currentRouteInfo.parent.name];
    }
  }
});

function changeMenu(e) {
  // selectedKey 会通过 watch(() => route.name) 更新，因为点击菜单项会触发路由跳转
  // router.push({ name: e.key }); // 这行是多余的，因为 router-link 已经处理了跳转
}

function onOpenChange(keys) {
  const latestOpenKey = keys.find(key => openKeys.value.indexOf(key) === -1);
  if (rootSubmenuKeys.value.indexOf(latestOpenKey) === -1) {
    openKeys.value = keys;
  } else {
    openKeys.value = latestOpenKey ? [latestOpenKey] : [];
}
}

</script>
<style lang="less" scoped>
::v-deep .ant-breadcrumb ol li:nth-last-child(1) .ant-breadcrumb-link{
  color: #1677FF;
}
::v-deep .ant-breadcrumb-link a:hover{
  background-color: #e6f4ff;
  display: inline-block;
  height: 30px;
  margin-top: 5px;
  line-height: 30px;
}
.a-breadcrumb{
  background-color: #fff;
  height: 40px;
  line-height: 40px;
  padding-left: 15px;
}
#app-menu {
  height: 100%;
  display: flex; /* 使用 flex 布局确保子元素正确排列 */

  .layout-sider {
    border-top: 1px solid #e8e8e8;
    border-right: 1px solid #e8e8e8;
    background-color: #FAFAFA;
    overflow-y: auto; /* 允许垂直滚动 */
    width: 200px; /* 固定宽度 */
    flex-shrink: 0; /* 防止侧边栏在 flex 容器中收缩 */
  }
  .app-menu-content {
    padding: 16px;
    background-color: #fff; /* 或者 #f0f2f5 */
    flex-grow: 1; /* 内容区域占据剩余空间 */
    overflow-y: auto; /* 允许内容区滚动 */
  }
  .ant-menu-item a,
  .ant-menu-submenu-title span {
    display: flex; // 使用 flex 确保图标和文字对齐
    align-items: center;
    .anticon {
      margin-right: 8px; // 图标和文字之间的间距
    }
  }
}
</style>
