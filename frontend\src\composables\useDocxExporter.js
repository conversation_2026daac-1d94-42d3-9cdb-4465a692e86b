/**
 * DOCX导出功能组合式函数
 * 提供文档导出相关的状态管理和导出逻辑
 */

import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { exportTiptapContentToDocx } from '@/utils/docxExporter'
import { sanitizeTextNodesInTiptapJSON } from '@/utils/templateUtils'

/**
 * DOCX导出功能组合式函数
 * @param {Object} options 配置选项
 * @param {Object} options.formState 表单状态对象
 * @param {Ref} options.editorContent 编辑器内容的响应式引用
 * @param {boolean} options.editorVisible 编辑器是否可见
 * @param {boolean} options.isLoading 是否正在加载
 * @param {boolean} options.isSubmitting 是否正在提交
 * @param {boolean} options.isEditMode 是否为编辑模式
 * @returns {Object} 导出相关的状态和方法
 */
export function useDocxExporter(options) {
  const { 
    formState, 
    editorContent, 
    editorVisible, 
    isLoading, 
    isSubmitting, 
    isEditMode 
  } = options
  
  // 导出状态
  const isExportingDocx = ref(false)
  
  /**
   * 检查导出条件
   * @returns {Object} 检查结果 { canExport: boolean, reason?: string }
   */
  const checkExportConditions = () => {
    // 检查编辑器是否可见
    if (!editorVisible) {
      return { 
        canExport: false, 
        reason: '编辑器内容未加载或为空，无法导出。' 
      }
    }
    
    // 检查编辑器内容
    if (!editorContent.value || !editorContent.value.content || editorContent.value.content.length === 0) {
      return { 
        canExport: false, 
        reason: '编辑器内容为空，无法导出。' 
      }
    }
    
    // 检查是否正在加载或提交
    if (isLoading || isSubmitting) {
      return { 
        canExport: false, 
        reason: '请等待当前操作完成后再导出。' 
      }
    }
    
    // 检查模板是否已选择（非编辑模式）
    if (!isEditMode && !formState.template_id) {
      return { 
        canExport: false, 
        reason: '请先选择模板后再导出。' 
      }
    }
    
    return { canExport: true }
  }
  
  /**
   * 获取导出文件名
   * @param {string} defaultName 默认文件名
   * @param {string} suffix 文件后缀（可选）
   * @returns {string} 导出文件名
   */
  const getExportFileName = (defaultName, suffix = '.docx') => {
    const fileName = formState.name || defaultName
    return fileName.endsWith(suffix) ? fileName : `${fileName}${suffix}`
  }
  
  /**
   * 准备导出内容
   * @returns {Object} 准备好的导出内容
   */
  const prepareExportContent = () => {
    // 深拷贝编辑器内容，避免修改原始数据
    const contentToExport = JSON.parse(JSON.stringify(editorContent.value))
    
    // 清理文本节点，确保导出内容的完整性
    sanitizeTextNodesInTiptapJSON(contentToExport)
    
    return contentToExport
  }
  
  /**
   * 执行DOCX导出
   * @param {string} defaultName 默认文件名
   * @param {Object} exportOptions 导出选项
   * @returns {Promise<boolean>} 导出是否成功
   */
  const handleExportToDocx = async (defaultName = '文档', exportOptions = {}) => {
    // 检查导出条件
    const checkResult = checkExportConditions()
    if (!checkResult.canExport) {
      message.warning(checkResult.reason)
      return false
    }
    
    // 获取导出文件名
    const fileName = getExportFileName(defaultName)
    
    // 设置导出状态
    isExportingDocx.value = true
    
    // 显示加载消息
    const loadingMessage = exportOptions.loadingMessage || '正在生成 DOCX 文件...'
    message.loading({
      content: loadingMessage,
      key: 'exportDocxMessage',
      duration: 0
    })
    
    try {
      // 准备导出内容
      const contentToExport = prepareExportContent()
      
      // 执行导出
      await exportTiptapContentToDocx(contentToExport, fileName)
      
      // 显示成功消息
      const successMessage = exportOptions.successMessage || 'DOCX 文件导出成功！'
      message.success({
        content: successMessage,
        key: 'exportDocxMessage',
        duration: 3
      })
      
      return true
    } catch (error) {
      console.error('导出 DOCX 文件失败:', error)
      
      // 显示错误消息
      const errorMessage = exportOptions.errorMessage || 
        `导出 DOCX 文件失败: ${error.message || '未知错误'}`
      message.error({
        content: errorMessage,
        key: 'exportDocxMessage',
        duration: 4
      })
      
      return false
    } finally {
      isExportingDocx.value = false
    }
  }
  
  /**
   * 批量导出多个文档
   * @param {Array} documents 文档列表
   * @param {Function} getContentCallback 获取文档内容的回调函数
   * @param {Object} exportOptions 导出选项
   * @returns {Promise<Object>} 导出结果统计
   */
  const handleBatchExport = async (documents, getContentCallback, exportOptions = {}) => {
    if (!Array.isArray(documents) || documents.length === 0) {
      message.warning('没有可导出的文档')
      return { success: 0, failed: 0, total: 0 }
    }
    
    isExportingDocx.value = true
    
    const results = {
      success: 0,
      failed: 0,
      total: documents.length
    }
    
    message.loading({
      content: `正在批量导出 ${documents.length} 个文档...`,
      key: 'batchExportMessage',
      duration: 0
    })
    
    try {
      for (let i = 0; i < documents.length; i++) {
        const document = documents[i]
        
        try {
          // 更新进度消息
          message.loading({
            content: `正在导出第 ${i + 1}/${documents.length} 个文档: ${document.name}`,
            key: 'batchExportMessage',
            duration: 0
          })
          
          // 获取文档内容
          const content = await getContentCallback(document)
          if (!content) {
            throw new Error('无法获取文档内容')
          }
          
          // 准备导出内容
          const contentToExport = JSON.parse(JSON.stringify(content))
          sanitizeTextNodesInTiptapJSON(contentToExport)
          
          // 执行导出
          const fileName = getExportFileName(document.name)
          await exportTiptapContentToDocx(contentToExport, fileName)
          
          results.success++
        } catch (error) {
          console.error(`导出文档 ${document.name} 失败:`, error)
          results.failed++
        }
      }
      
      // 显示批量导出结果
      const successMessage = `批量导出完成！成功: ${results.success}，失败: ${results.failed}`
      message.success({
        content: successMessage,
        key: 'batchExportMessage',
        duration: 5
      })
      
    } catch (error) {
      console.error('批量导出失败:', error)
      message.error({
        content: `批量导出失败: ${error.message || '未知错误'}`,
        key: 'batchExportMessage',
        duration: 4
      })
    } finally {
      isExportingDocx.value = false
    }
    
    return results
  }
  
  /**
   * 重置导出状态
   */
  const resetExportState = () => {
    isExportingDocx.value = false
  }
  
  return {
    // 状态
    isExportingDocx,
    
    // 方法
    checkExportConditions,
    getExportFileName,
    prepareExportContent,
    handleExportToDocx,
    handleBatchExport,
    resetExportState
  }
}
