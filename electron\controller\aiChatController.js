'use strict';

const AiChatService = require('../service/aiChatService');

/**
 * AI聊天控制器
 * @class
 */
class AiChatController {
  constructor(ctx) {
    this.aiChatService = new AiChatService(ctx);
  }

  /**
   * 创建新的聊天会话
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async createSession(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body;
    const { userId, title } = params;

    if (!userId) {
      return { success: false, message: '用户ID不能为空' };
    }

    try {
      const result = await this.aiChatService.createSession(parseInt(userId), title);
      return { success: true, data: result };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  /**
   * 获取用户的会话列表
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async getSessionList(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {};
    const { userId, page, pageSize, status } = params;

    if (!userId) {
      return { success: false, message: '用户ID不能为空' };
    }

    try {
      const result = await this.aiChatService.getSessionsByUserId(parseInt(userId), {
        page: page ? parseInt(page) : 1,
        pageSize: pageSize ? parseInt(pageSize) : 10,
        status: status || 'active'
      });
      return { success: true, data: result };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  /**
   * 更新会话信息
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async updateSession(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body;
    const { sessionId, ...updateData } = params;

    const parsedSessionId = parseInt(sessionId);
    if (!parsedSessionId || isNaN(parsedSessionId)) {
      return { success: false, message: '无效的会话ID' };
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return { success: false, message: '没有提供需要更新的数据' };
    }

    try {
      const result = await this.aiChatService.updateSession(parsedSessionId, updateData);
      return { success: true, data: result };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  /**
   * 删除会话
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async deleteSession(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body;
    const { sessionId, userId } = params;

    const parsedSessionId = parseInt(sessionId);
    const parsedUserId = parseInt(userId);

    if (!parsedSessionId || isNaN(parsedSessionId) || !parsedUserId || isNaN(parsedUserId)) {
      return { success: false, message: '无效的会话ID或用户ID' };
    }

    try {
      const result = await this.aiChatService.deleteSession(parsedSessionId, parsedUserId);
      return { success: true, data: { deleted: result } };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  /**
   * 获取会话的消息历史
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async getMessages(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {};
    const { sessionId, limit, offset } = params;

    const parsedSessionId = parseInt(sessionId);
    if (!parsedSessionId || isNaN(parsedSessionId)) {
      return { success: false, message: '无效的会话ID' };
    }

    try {
      const result = await this.aiChatService.getMessagesBySessionId(parsedSessionId, {
        limit: limit ? parseInt(limit) : undefined,
        offset: offset ? parseInt(offset) : 0
      });
      return { success: true, data: result };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  /**
   * 流式聊天接口
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async streamChat(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body;
    const { sessionId, message, options = {} } = params;

    const parsedSessionId = parseInt(sessionId);
    if (!parsedSessionId || isNaN(parsedSessionId)) {
      ctx.status = 400;
      ctx.body = { success: false, message: '无效的会话ID' };
      return;
    }

    if (!message || !message.trim()) {
      ctx.status = 400;
      ctx.body = { success: false, message: '消息内容不能为空' };
      return;
    }

    // 获取原生Node.js响应对象
    const res = ctx.res;
    
    // 设置SSE响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control,Content-Type,X-User-ID',
      'Access-Control-Allow-Methods': 'GET,POST,OPTIONS'
    });

    // 流式响应处理函数
    const onChunk = (chunkData) => {
      try {
        const eventData = `data: ${JSON.stringify(chunkData)}\n\n`;
        res.write(eventData);
        
        // 关键：立即刷新缓冲区，确保数据实时传输
        if (res.flush && typeof res.flush === 'function') {
          res.flush();
        }
      } catch (error) {
        console.error('[streamChat] 写入流式数据失败:', error);
      }
    };

    try {
      // 调用流式AI服务
      const result = await this.aiChatService.sendMessageToAIStream(parsedSessionId, message, options, onChunk);
      
      // 发送完成信号
      res.write('data: [DONE]\n\n');
      
      // 刷新并结束响应
      if (res.flush && typeof res.flush === 'function') {
        res.flush();
      }
      res.end();

      // 注意：在SSE模式下，不能返回JSON响应，因为响应已经结束
    } catch (error) {
      console.error('[streamChat] AI流式对话错误:', error);
      
      // 发送错误信息
      const errorData = {
        type: 'error',
        message: error.message
      };
      res.write(`data: ${JSON.stringify(errorData)}\n\n`);
      res.write('data: [DONE]\n\n');
      
      // 刷新并结束响应
      if (res.flush && typeof res.flush === 'function') {
        res.flush();
      }
      res.end();
    }
  }
}

AiChatController.toString = () => '[class AiChatController]';

module.exports = AiChatController; 