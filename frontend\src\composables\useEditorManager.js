/**
 * 编辑器管理组合式函数
 * 提供编辑器状态管理、内容处理、模板加载等功能
 */

import { ref, computed, nextTick } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { mergeTemplateWithFieldValues, extractFieldValuesFromTiptapJSON, sanitizeTextNodesInTiptapJSON, getDefaultTiptapDoc } from '@/utils/templateUtils'

/**
 * 编辑器管理组合式函数
 * @param {Object} options 配置选项
 * @param {Object} options.formState 表单状态对象
 * @param {boolean} options.isEditMode 是否为编辑模式
 * @param {boolean} options.isLoading 是否正在加载
 * @returns {Object} 编辑器相关的状态和方法
 */
export function useEditorManager(options) {
  const { formState, isEditMode, isLoading } = options
  
  // 编辑器状态
  const editorRef = ref(null)
  const editorVisible = ref(false)
  const editorContent = ref(getDefaultTiptapDoc())
  
  // 模板相关状态
  const selectedTemplateFull = ref(null)
  const currentTemplateContent = ref(null)
  
  /**
   * 编辑器占位符文本
   */
  const editorPlaceholderText = computed(() => {
    if (isLoading.value && !isEditMode.value && !formState.template_id) return '请先选择一个模板...'
    if (isLoading.value) return '正在加载内容...'
    if (isEditMode.value && !editorVisible.value) return '正在加载内容...'
    if (!formState.template_id && !isEditMode.value) return '请先选择一个模板'
    if (!editorVisible.value && formState.template_id) return '模板已选择，正在准备编辑器...'
    return '编辑器加载中或模板内容为空'
  })
  
  /**
   * 显示编辑器
   */
  const showEditor = () => {
    nextTick(() => {
      editorVisible.value = true
    })
  }
  
  /**
   * 隐藏编辑器
   */
  const hideEditor = () => {
    editorVisible.value = false
  }
  
  /**
   * 重置编辑器状态
   */
  const resetEditorState = () => {
    editorVisible.value = false
    editorContent.value = getDefaultTiptapDoc()
    selectedTemplateFull.value = null
    currentTemplateContent.value = null
  }
  
  /**
   * 加载模板内容并合并字段值
   * @param {Object} templateData 模板数据
   * @param {Object} fieldValues 字段值对象
   */
  const loadTemplateContent = (templateData, fieldValues = {}) => {
    try {
      selectedTemplateFull.value = templateData
      
      // 获取模板内容
      let templateContent = templateData.content
      if (typeof templateContent === 'string') {
        try {
          templateContent = JSON.parse(templateContent)
        } catch (e) {
          console.error('模板内容JSON解析失败:', e)
          templateContent = getDefaultTiptapDoc()
        }
      }
      
      if (templateContent && templateContent.type === 'doc') {
        // 清理模板内容
        sanitizeTextNodesInTiptapJSON(templateContent)
        currentTemplateContent.value = templateContent
        
        // 合并模板内容与字段值
        const mergedContent = mergeTemplateWithFieldValues(templateContent, fieldValues)
        sanitizeTextNodesInTiptapJSON(mergedContent)
        
        editorContent.value = mergedContent
        formState.content = mergedContent
        
        console.log('模板内容加载完成')
        return true
      } else {
        console.warn('模板内容格式不正确')
        editorContent.value = getDefaultTiptapDoc()
        formState.content = getDefaultTiptapDoc()
        return false
      }
    } catch (error) {
      console.error('加载模板内容时出错:', error)
      editorContent.value = getDefaultTiptapDoc()
      formState.content = getDefaultTiptapDoc()
      return false
    }
  }
  
  /**
   * 处理模板变化（带确认对话框）
   * @param {Function} loadTemplateCallback 加载模板的回调函数
   * @param {Object} templateData 模板数据
   */
  const handleTemplateChangeWithConfirm = async (loadTemplateCallback, templateData) => {
    // 检查是否有现有内容
    const hasExistingContent = 
      formState.content &&
      formState.content.content &&
      formState.content.content.length > 0 &&
      !(
        formState.content.content.length === 1 &&
        formState.content.content[0].type === 'paragraph' &&
        (!formState.content.content[0].content || formState.content.content[0].content.length === 0)
      )
    
    if (hasExistingContent) {
      return new Promise((resolve) => {
        Modal.confirm({
          title: '确认切换模板',
          content: '切换模板后，当前编辑的内容将被替换为新模板的内容，此操作不可撤销。是否继续？',
          okText: '确认切换',
          cancelText: '取消',
          onOk: async () => {
            const result = await loadTemplateCallback(templateData)
            resolve(result)
          },
          onCancel: () => {
            // 恢复原来的选择
            const originalTemplateId = selectedTemplateFull.value?.id || null
            formState.template_id = originalTemplateId
            resolve(false)
          }
        })
      })
    } else {
      return await loadTemplateCallback(templateData)
    }
  }
  
  /**
   * 尝试填充方案字段到编辑器
   * @param {Object} schemeData 方案数据
   */
  const tryFillSchemeFields = async (schemeData) => {
    if (!selectedTemplateFull.value || !selectedTemplateFull.value.content) {
      console.log('没有选择模板或模板没有内容，跳过字段填充')
      return false
    }
    
    if (!schemeData || !schemeData.content) {
      console.log('没有方案数据，保持模板原始内容')
      return false
    }
    
    console.log('开始填充方案字段到模板内容')
    hideEditor()
    
    try {
      const templateDoc = JSON.parse(JSON.stringify(selectedTemplateFull.value.content))
      const schemeValues = typeof schemeData.content === 'string' 
        ? JSON.parse(schemeData.content) 
        : schemeData.content
      
      if (typeof schemeValues !== 'object' || schemeValues === null) {
        message.warn('方案的字段内容格式不正确，无法自动填充。')
        return false
      }
      
      // 清理模板内容
      sanitizeTextNodesInTiptapJSON(templateDoc)
      
      // 合并模板内容与字段值
      const filledContent = mergeTemplateWithFieldValues(templateDoc, schemeValues)
      
      // 清理合并后的内容
      sanitizeTextNodesInTiptapJSON(filledContent)
      
      editorContent.value = filledContent
      formState.content = filledContent
      
      console.log('方案字段填充完成')
      message.success('方案字段已自动填充到模板中')
      return true
    } catch (error) {
      console.error('填充方案字段时出错:', error)
      message.error('填充方案字段时出错: ' + error.message)
      return false
    } finally {
      showEditor()
    }
  }
  
  /**
   * 从编辑器内容中提取字段值
   * @returns {Object} 字段值对象
   */
  const extractCurrentFieldValues = () => {
    return extractFieldValuesFromTiptapJSON(editorContent.value)
  }
  
  /**
   * 滚动到指定字段
   * @param {string} fieldKey 字段键
   */
  const scrollToField = (fieldKey) => {
    if (editorRef.value) {
      editorRef.value.scrollToField(fieldKey)
    }
  }
  
  /**
   * 高亮指定字段
   * @param {string} fieldKey 字段键
   * @param {boolean} highlight 是否高亮
   */
  const highlightField = (fieldKey, highlight) => {
    if (editorRef.value) {
      editorRef.value.highlightField(fieldKey, highlight)
    }
  }
  
  return {
    // 状态
    editorRef,
    editorVisible,
    editorContent,
    selectedTemplateFull,
    currentTemplateContent,
    editorPlaceholderText,
    
    // 方法
    showEditor,
    hideEditor,
    resetEditorState,
    loadTemplateContent,
    handleTemplateChangeWithConfirm,
    tryFillSchemeFields,
    extractCurrentFieldValues,
    scrollToField,
    highlightField
  }
}
