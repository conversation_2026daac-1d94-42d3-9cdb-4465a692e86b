<template>
  <div v-if="editor && !isFillMode" class="editor-toolbar">
    <a-space class="p-[5px]" wrap>
      <!-- 插入字段的下拉菜单 -->
      <a-dropdown v-if="internalAvailableFields && internalAvailableFields.length > 0">
        <template #overlay>
          <a-menu @click="handleInsertFieldFromPool">
            <a-menu-item v-for="field in internalAvailableFields" :key="field.field_key" :data-field-object="field">{{ field.field_name }} (来自字段池)</a-menu-item>
          </a-menu>
        </template>
        <a-button size="small">
          字段池
          <DownOutlined />
        </a-button>
      </a-dropdown>

      <!-- 添加自定义模板内字段 -->
      <a-tooltip title="添加自定义模板字段">
        <a-button size="small" @click="showAddCustomFieldModal">
          <template #icon><PlusCircleOutlined /></template>
          自定义字段
        </a-button>
      </a-tooltip>

      <a-divider type="vertical" />

      <!-- 字体选择 -->
      <a-tooltip title="字体">
        <a-dropdown trigger="hover">
          <template #overlay>
            <a-menu @click="handleFontFamilyMenuClick">
              <a-menu-item v-for="font in fontFamilyOptions" :key="font.value" :data-font-value="font.value">
                {{ font.label }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button size="small" style="width: 120px; text-align: left">
            {{ currentFontFamily }}
            <DownOutlined style="float: right; margin-top: 2px" />
          </a-button>
        </a-dropdown>
      </a-tooltip>

      <!-- 字号选择 -->
      <a-tooltip title="字号">
        <a-dropdown trigger="hover">
          <template #overlay>
            <a-menu @click="handleFontSizeMenuClick">
              <a-menu-item v-for="size in fontSizeOptions" :key="size.value" :data-size-value="size.value">
                {{ size.label }}
              </a-menu-item>
            </a-menu>
          </template>
          <a-button size="small" style="width: 80px; text-align: left">
            {{ currentFontSize }}
            <DownOutlined style="float: right; margin-top: 2px" />
          </a-button>
        </a-dropdown>
      </a-tooltip>

      <!-- 文字颜色 -->
      <a-tooltip title="文字颜色">
        <a-dropdown>
          <template #overlay>
            <div class="color-picker-panel">
              <div class="color-grid">
                <div v-for="color in colorOptions" :key="color" class="color-item" :style="{ backgroundColor: color }" @click="handleColorChange(color)"></div>
              </div>
              <a-divider style="margin: 8px 0" />
              <a-button size="small" @click="handleColorChange(null)" block>清除颜色</a-button>
            </div>
          </template>
          <a-button size="small">
            <template #icon><FontColorsOutlined /></template>
            <span class="color-indicator" :style="{ backgroundColor: currentColor || '#000' }"></span>
          </a-button>
        </a-dropdown>
      </a-tooltip>

      <a-divider type="vertical" />

      <!-- 首行缩进 -->
      <a-tooltip :title="currentTextIndent ? '取消首行缩进' : '首行缩进2字符'">
        <a-button @click="handleFirstLineIndent" size="small" :type="currentTextIndent ? 'primary' : 'default'">
          <template #icon><MenuFoldOutlined /></template>
          缩进
        </a-button>
      </a-tooltip>

      <!-- 行间距 -->
      <a-tooltip title="行间距">
        <a-dropdown>
          <template #overlay>
            <a-menu @click="handleLineHeightChange">
              <a-menu-item key="1">单倍行距</a-menu-item>
              <a-menu-item key="1.15">1.15倍行距</a-menu-item>
              <a-menu-item key="1.5">1.5倍行距</a-menu-item>
              <a-menu-item key="2">2倍行距</a-menu-item>
              <a-menu-item key="2.5">2.5倍行距</a-menu-item>
              <a-menu-item key="3">3倍行距</a-menu-item>
            </a-menu>
          </template>
          <a-button size="small">
            <template #icon><LineHeightOutlined /></template>
            行距
          </a-button>
        </a-dropdown>
      </a-tooltip>

      <a-divider type="vertical" />

      <a-tooltip title="加粗 (Ctrl+B)">
        <a-button
          size="small"
          :type="editor.isActive('bold') ? 'primary' : 'default'"
          @click="editor.chain().focus().toggleBold().run()"
          :disabled="!editor.can().chain().focus().toggleBold().run()"
        >
          <template #icon><BoldOutlined /></template>
        </a-button>
      </a-tooltip>

      <a-tooltip title="斜体 (Ctrl+I)">
        <a-button
          size="small"
          :type="editor.isActive('italic') ? 'primary' : 'default'"
          @click="editor.chain().focus().toggleItalic().run()"
          :disabled="!editor.can().chain().focus().toggleItalic().run()"
        >
          <template #icon><ItalicOutlined /></template>
        </a-button>
      </a-tooltip>

      <a-tooltip title="下划线 (Ctrl+U)">
        <a-button
          size="small"
          :type="editor.isActive('underline') ? 'primary' : 'default'"
          @click="editor.chain().focus().toggleUnderline().run()"
          :disabled="!editor.can().chain().focus().toggleUnderline().run()"
        >
          <template #icon><UnderlineOutlined /></template>
        </a-button>
      </a-tooltip>

      <a-tooltip title="删除线">
        <a-button
          size="small"
          :type="editor.isActive('strike') ? 'primary' : 'default'"
          @click="editor.chain().focus().toggleStrike().run()"
          :disabled="!editor.can().chain().focus().toggleStrike().run()"
        >
          <template #icon><StrikethroughOutlined /></template>
        </a-button>
      </a-tooltip>

      <a-divider type="vertical" />

      <a-tooltip title="一级标题">
        <a-button size="small" :type="editor.isActive('heading', { level: 1 }) ? 'primary' : 'default'" @click="editor.chain().focus().toggleHeading({ level: 1 }).run()">
          H1
        </a-button>
      </a-tooltip>
      <a-tooltip title="二级标题">
        <a-button size="small" :type="editor.isActive('heading', { level: 2 }) ? 'primary' : 'default'" @click="editor.chain().focus().toggleHeading({ level: 2 }).run()">
          H2
        </a-button>
      </a-tooltip>
      <a-tooltip title="三级标题">
        <a-button size="small" :type="editor.isActive('heading', { level: 3 }) ? 'primary' : 'default'" @click="editor.chain().focus().toggleHeading({ level: 3 }).run()">
          H3
        </a-button>
      </a-tooltip>
      <a-tooltip title="段落">
        <a-button size="small" :type="editor.isActive('paragraph') ? 'primary' : 'default'" @click="editor.chain().focus().setParagraph().run()">P</a-button>
      </a-tooltip>

      <a-divider type="vertical" />

      <a-tooltip title="左对齐">
        <a-button
          size="small"
          :type="editor.isActive({ textAlign: 'left' }) ? 'primary' : 'default'"
          @click="editor.chain().focus().setTextAlign('left').run()"
          :disabled="!editor.can().setTextAlign('left')"
        >
          <template #icon><AlignLeftOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="居中对齐">
        <a-button
          size="small"
          :type="editor.isActive({ textAlign: 'center' }) ? 'primary' : 'default'"
          @click="editor.chain().focus().setTextAlign('center').run()"
          :disabled="!editor.can().setTextAlign('center')"
        >
          <template #icon><AlignCenterOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="右对齐">
        <a-button
          size="small"
          :type="editor.isActive({ textAlign: 'right' }) ? 'primary' : 'default'"
          @click="editor.chain().focus().setTextAlign('right').run()"
          :disabled="!editor.can().setTextAlign('right')"
        >
          <template #icon><AlignRightOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="两端对齐">
        <a-button
          size="small"
          :type="editor.isActive({ textAlign: 'justify' }) ? 'primary' : 'default'"
          @click="editor.chain().focus().setTextAlign('justify').run()"
          :disabled="!editor.can().setTextAlign('justify')"
        >
          <template #icon><MenuUnfoldOutlined /></template>
        </a-button>
      </a-tooltip>

      <a-divider type="vertical" />

      <a-tooltip title="无序列表">
        <a-button size="small" :type="editor.isActive('bulletList') ? 'primary' : 'default'" @click="editor.chain().focus().toggleBulletList().run()">
          <template #icon><UnorderedListOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="有序列表">
        <a-button size="small" :type="editor.isActive('orderedList') ? 'primary' : 'default'" @click="editor.chain().focus().toggleOrderedList().run()">
          <template #icon><OrderedListOutlined /></template>
        </a-button>
      </a-tooltip>

      <a-divider type="vertical" />

      <a-tooltip title="水平分割线">
        <a-button size="small" @click="editor.chain().focus().setHorizontalRule().run()">
          <template #icon><MinusOutlined /></template>
        </a-button>
      </a-tooltip>

      <a-divider type="vertical" />

      <a-tooltip title="撤销 (Ctrl+Z)">
        <a-button size="small" @click="editor.chain().focus().undo().run()" :disabled="!editor.can().chain().focus().undo().run()">
          <template #icon><UndoOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="重做 (Ctrl+Shift+Z)">
        <a-button size="small" @click="editor.chain().focus().redo().run()" :disabled="!editor.can().chain().focus().redo().run()">
          <template #icon><RedoOutlined /></template>
        </a-button>
      </a-tooltip>

      <a-divider type="vertical" />

      <!-- 分页符、目录、图片按钮 -->
      <a-tooltip title="插入分页符">
        <a-button size="small" @click="insertPageBreak">
          <template #icon><FileTextOutlined /></template>
          分页符
        </a-button>
      </a-tooltip>

      <a-tooltip title="插入目录">
        <a-button size="small" @click="showTableOfContentsModal">
          <template #icon><UnorderedListOutlined /></template>
          目录
        </a-button>
      </a-tooltip>

      <a-tooltip title="插入图片">
        <a-button size="small" @click="showImageSelectModal">
          <template #icon><PictureOutlined /></template>
          图片
        </a-button>
      </a-tooltip>

      <a-divider type="vertical" />

      <!-- 表格操作按钮 -->
      <a-tooltip title="插入表格">
        <a-button size="small" @click="editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()">
          <template #icon><TableOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="在后方添加列">
        <a-button size="small" @click="editor.chain().focus().addColumnAfter().run()" :disabled="!editor.can().addColumnAfter()">
          <template #icon><BorderRightOutlined /></template>
          <!-- 暂用，或可考虑更形象的图标/文字 -->
        </a-button>
      </a-tooltip>
      <a-tooltip title="在前方添加列">
        <a-button size="small" @click="editor.chain().focus().addColumnBefore().run()" :disabled="!editor.can().addColumnBefore()">
          <template #icon><BorderLeftOutlined /></template>
          <!-- 暂用 -->
        </a-button>
      </a-tooltip>
      <a-tooltip title="删除列">
        <a-button size="small" @click="editor.chain().focus().deleteColumn().run()" :disabled="!editor.can().deleteColumn()">
          <template #icon><DeleteColumnOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="在下方添加行">
        <a-button size="small" @click="editor.chain().focus().addRowAfter().run()" :disabled="!editor.can().addRowAfter()">
          <template #icon><BorderBottomOutlined /></template>
          <!-- 暂用 -->
        </a-button>
      </a-tooltip>
      <a-tooltip title="在上方添加行">
        <a-button size="small" @click="editor.chain().focus().addRowBefore().run()" :disabled="!editor.can().addRowBefore()">
          <template #icon><BorderTopOutlined /></template>
          <!-- 暂用 -->
        </a-button>
      </a-tooltip>
      <a-tooltip title="删除行">
        <a-button size="small" @click="editor.chain().focus().deleteRow().run()" :disabled="!editor.can().deleteRow()">
          <template #icon><DeleteRowOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="删除表格">
        <a-button size="small" @click="editor.chain().focus().deleteTable().run()" :disabled="!editor.can().deleteTable()">
          <template #icon><DeleteOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="合并单元格">
        <a-button size="small" @click="editor.chain().focus().mergeCells().run()" :disabled="!editor.can().mergeCells()">
          <template #icon><MergeCellsOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="拆分单元格">
        <a-button size="small" @click="editor.chain().focus().splitCell().run()" :disabled="!editor.can().splitCell()">
          <template #icon><SplitCellsOutlined /></template>
        </a-button>
      </a-tooltip>
      <a-tooltip title="切换为表头行">
        <a-button size="small" @click="editor.chain().focus().toggleHeaderRow().run()" :disabled="!editor.can().toggleHeaderRow()">
          <template #icon><BorderHorizontalOutlined /></template>
          <!-- 暂用 -->
        </a-button>
      </a-tooltip>
      <a-tooltip title="切换为表头列">
        <a-button size="small" @click="editor.chain().focus().toggleHeaderColumn().run()" :disabled="!editor.can().toggleHeaderColumn()">
          <template #icon><BorderVerticleOutlined /></template>
          <!-- 暂用 -->
        </a-button>
      </a-tooltip>
    </a-space>

    <!-- 添加自定义字段的 Modal -->
    <a-modal
      title="添加自定义模板字段"
      v-model:visible="customFieldModalVisible"
      @ok="handleAddCustomField"
      @cancel="customFieldModalVisible = false"
      ok-text="确认添加"
      cancel-text="取消"
    >
      <a-form layout="vertical">
        <a-form-item label="字段显示名称" required :validate-status="customFieldNameError ? 'error' : ''" :help="customFieldNameError">
          <a-input v-model:value="customFieldName" placeholder="例如：项目名称、公司名称、联系人等" />
        </a-form-item>
        <a-form-item label="说明">
          <div class="field-description">
            <p>• 字段将自动生成唯一标识</p>
            <p>• 默认情况下字段不可编辑，用于模板结构</p>
            <p>• 在填充模式下可以编辑字段内容</p>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 目录设置 Modal -->
    <a-modal
      title="插入目录"
      v-model:visible="tocModalVisible"
      @ok="handleInsertTableOfContents"
      @cancel="tocModalVisible = false"
      ok-text="插入目录"
      cancel-text="取消"
      width="500px"
    >
      <a-form layout="vertical">
        <a-form-item label="目录标题">
          <a-input v-model:value="tocTitle" placeholder="目录" />
        </a-form-item>
        <a-form-item label="包含的标题级别">
          <a-checkbox-group v-model:value="tocLevels">
            <a-checkbox value="1">一级标题 (H1)</a-checkbox>
            <a-checkbox value="2">二级标题 (H2)</a-checkbox>
            <a-checkbox value="3">三级标题 (H3)</a-checkbox>
            <a-checkbox value="4">四级标题 (H4)</a-checkbox>
            <a-checkbox value="5">五级标题 (H5)</a-checkbox>
            <a-checkbox value="6">六级标题 (H6)</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 图片选择 Modal -->
    <a-modal
      title="插入图片"
      v-model:visible="imageModalVisible"
      @ok="handleInsertImage"
      @cancel="imageModalVisible = false"
      ok-text="插入图片"
      cancel-text="取消"
      width="600px"
    >
      <a-form layout="vertical">
        <a-form-item label="选择图片" required>
          <div class="image-gallery">
            <div v-for="image in availableImages" :key="image.name" class="image-item" :class="{ selected: selectedImage === image.src }" @click="selectImage(image.src)">
              <img :src="image.src" :alt="image.name" />
              <div class="image-name">{{ image.name }}</div>
            </div>
          </div>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed, watch, inject } from 'vue'
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  StrikethroughOutlined,
  UnorderedListOutlined,
  OrderedListOutlined,
  CodeOutlined,
  UndoOutlined,
  RedoOutlined,
  BlockOutlined,
  MinusOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  MenuUnfoldOutlined,
  TableOutlined,
  BorderRightOutlined,
  BorderLeftOutlined,
  DeleteColumnOutlined,
  BorderBottomOutlined,
  BorderTopOutlined,
  DeleteRowOutlined,
  DeleteOutlined,
  MergeCellsOutlined,
  SplitCellsOutlined,
  BorderHorizontalOutlined,
  BorderVerticleOutlined,
  DownOutlined,
  PlusCircleOutlined,
  FileTextOutlined,
  PictureOutlined,
  FontColorsOutlined,
  MenuFoldOutlined,
  LineHeightOutlined
} from '@ant-design/icons-vue'
import { Modal, message } from 'ant-design-vue'
import api from '@/api'

// 静态导入图片资源
import logoImg from '@/assets/TiptapEditor/Toolbar/logo.png'
import sample1Img from '@/assets/TiptapEditor/Toolbar/sample1.png'

const props = defineProps({
  editor: {
    type: Object,
    required: true
  },
  insertFieldFn: {
    type: Function,
    required: true
  }
})

// 注入填充模式状态
const fillMode = inject('fillMode', false)
const isFillMode = computed(() => fillMode.value)

// 内部字段池数据
const internalAvailableFields = ref([])

// 获取字段池数据
const fetchAvailableFields = async () => {
  try {
    const response = await api.templateField.list()
    if (response.success && response.data) {
      internalAvailableFields.value = (response.data?.list || []).map(f => ({
        field_key: f.field_key,
        field_name: f.field_name,
        field_type: f.field_type
      }))
      console.log('EditorToolbar: 字段池获取成功', internalAvailableFields.value)
    } else {
      console.error('EditorToolbar: 获取字段池失败', response.message || '未知错误')
      message.error('获取字段池失败: ' + (response.message || '未知错误'))
    }
  } catch (error) {
    console.error('EditorToolbar: 加载可用字段错误', error)
    message.error('加载可用字段错误: ' + error.message)
  }
}

// Modal 相关状态
const customFieldModalVisible = ref(false)
const customFieldName = ref('')
const customFieldNameError = ref('')

// 目录 Modal 状态
const tocModalVisible = ref(false)
const tocTitle = ref('目录')
const tocLevels = ref(['1', '2', '3'])

// 图片 Modal 状态
const imageModalVisible = ref(false)
const selectedImage = ref('')

// 可用图片列表
const availableImages = ref([])

// 字体、字号、颜色相关状态
const currentFontFamily = ref('宋体') // 默认宋体
const currentFontSize = ref('五号') // 默认五号
const currentColor = ref('')
const currentTextIndent = ref(false) // 是否有首行缩进
const currentLineHeight = ref('1') // 当前行间距

// Word常用字体选项
const fontFamilyOptions = ref([
  { label: '宋体', value: '宋体' },
  { label: '黑体', value: '黑体' },
  { label: '楷体', value: '楷体' },
  { label: '仿宋', value: '仿宋' },
  { label: '微软雅黑', value: 'Microsoft YaHei' },
  { label: 'Arial', value: 'Arial' },
  { label: 'Times New Roman', value: 'Times New Roman' },
  { label: 'Calibri', value: 'Calibri' },
  { label: 'Verdana', value: 'Verdana' },
  { label: 'Georgia', value: 'Georgia' }
])

// Word常用字号选项
const fontSizeOptions = ref([
  { label: '初号', value: '42pt' },
  { label: '小初', value: '36pt' },
  { label: '一号', value: '26pt' },
  { label: '小一', value: '24pt' },
  { label: '二号', value: '22pt' },
  { label: '小二', value: '18pt' },
  { label: '三号', value: '16pt' },
  { label: '小三', value: '15pt' },
  { label: '四号', value: '14pt' },
  { label: '小四', value: '12pt' },
  { label: '五号', value: '10.5pt' },
  { label: '小五', value: '9pt' },
  { label: '六号', value: '7.5pt' },
  { label: '小六', value: '6.5pt' },
  { label: '七号', value: '5.5pt' }
])

// 常用颜色选项
const colorOptions = ref([
  '#000000',
  '#333333',
  '#666666',
  '#999999',
  '#CCCCCC',
  '#FF0000',
  '#FF6600',
  '#FFCC00',
  '#FFFF00',
  '#99FF00',
  '#00FF00',
  '#00FFCC',
  '#00CCFF',
  '#0099FF',
  '#0066FF',
  '#0000FF',
  '#6600FF',
  '#9900FF',
  '#CC00FF',
  '#FF0099',
  '#FF3366',
  '#FF6699',
  '#FF99CC',
  '#FFCCFF',
  '#CCCCFF',
  '#99CCFF',
  '#66CCFF',
  '#33CCFF',
  '#00CCFF',
  '#CCFFFF',
  '#CCFFCC',
  '#99FFCC',
  '#66FFCC',
  '#33FFCC',
  '#00FFCC',
  '#FFFFCC',
  '#FFFF99',
  '#FFFF66',
  '#FFFF33',
  '#FFFF00'
])

const showAddCustomFieldModal = () => {
  customFieldName.value = ''
  customFieldNameError.value = ''
  customFieldModalVisible.value = true
}

// 生成唯一的字段key
const generateUniqueFieldKey = baseName => {
  // 将中文名称转换为拼音首字母或使用时间戳
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 8)
  return `field_${timestamp}_${randomStr}`
}

// 遍历 Tiptap JSON 内容，检查 field_key 是否已存在
const isFieldKeyUniqueInContent = (contentNodes, keyToCheck) => {
  if (!Array.isArray(contentNodes)) {
    return true
  }
  for (const node of contentNodes) {
    if (node.type === 'customField' && node.attrs && node.attrs.field_key === keyToCheck) {
      return false // 找到了重复的 key
    }
    // 递归检查子节点 (如果节点有 content 属性)
    if (node.content && !isFieldKeyUniqueInContent(node.content, keyToCheck)) {
      return false
    }
  }
  return true
}

const handleAddCustomField = () => {
  customFieldNameError.value = ''

  if (!customFieldName.value.trim()) {
    customFieldNameError.value = '字段显示名称不能为空。'
    return
  }

  // 自动生成唯一的字段key
  const fieldKey = generateUniqueFieldKey(customFieldName.value.trim())

  props.insertFieldFn({
    field_key: fieldKey,
    field_name: customFieldName.value.trim(),
    field_type: 'template_custom', // 标记为模板内自定义字段
    isFieldValueEditable: false // 默认不可编辑，只有填充模式下才可编辑
  })

  customFieldModalVisible.value = false
  message.success('自定义字段添加成功')
}

const handleInsertFieldFromPool = ({ item, key }) => {
  const field = item?.originItemSpec?.props?.dataFieldObject || item?.item?.props?.dataFieldObject

  if (field && typeof field === 'object') {
    // 检查从字段池选择的字段 key 是否已在模板中 (虽然不太可能，但作为安全措施)
    const currentEditorJSON = props.editor.getJSON()
    if (!isFieldKeyUniqueInContent(currentEditorJSON.content, field.field_key)) {
      message.warn(`字段 "${field.field_name}" (key: ${field.field_key}) 已通过自定义方式存在于模板中。仍会插入，但请注意潜在冲突。`)
    }

    props.insertFieldFn({
      field_key: field.field_key,
      field_name: field.field_name,
      field_type: field.field_type || 'pool_custom', // 标记为来自字段池
      isFieldValueEditable: true
    })
  } else {
    console.warn('Could not retrieve field object from menu item', item, key)
    if (typeof key === 'string') {
      const fallbackField = internalAvailableFields.value.find(f => f.field_key === key)
      if (fallbackField) {
        const currentEditorJSON = props.editor.getJSON()
        if (!isFieldKeyUniqueInContent(currentEditorJSON.content, fallbackField.field_key)) {
          message.warn(`字段 "${fallbackField.field_name}" (key: ${fallbackField.field_key}) 已通过自定义方式存在于模板中。仍会插入，但请注意潜在冲突。`)
        }
        props.insertFieldFn({
          field_key: fallbackField.field_key,
          field_name: fallbackField.field_name,
          field_type: fallbackField.field_type || 'pool_custom',
          isFieldValueEditable: true
        })
        return
      }
    }
    console.error('Failed to insert field from pool: details not found.')
    message.error('从字段池插入字段失败。')
  }
}

// 初始化可用图片
const initAvailableImages = () => {
  // 使用已经导入的图片资源
  availableImages.value = [
    {
      name: 'Logo图片',
      src: logoImg,
      fileName: 'logo.png'
    },
    {
      name: '示例图片1',
      src: sample1Img,
      fileName: 'sample1.png'
    }
    // 如果需要添加更多图片，在文件顶部添加对应的import语句，然后在这里添加对象
  ]

  console.log('EditorToolbar: 加载可用图片', availableImages.value)
}

// 插入分页符
const insertPageBreak = () => {
  if (props.editor) {
    props.editor.chain().focus().insertPageBreak().run()
    message.success('分页符插入成功')
  }
}

// 显示目录配置 Modal
const showTableOfContentsModal = () => {
  tocTitle.value = '目录'
  tocLevels.value = ['1', '2', '3']
  tocModalVisible.value = true
}

// 插入目录
const handleInsertTableOfContents = () => {
  if (props.editor) {
    props.editor
      .chain()
      .focus()
      .insertTableOfContents({
        title: tocTitle.value,
        levels: tocLevels.value.join(',')
      })
      .run()
    tocModalVisible.value = false
    message.success('目录插入成功')
  }
}

// 显示图片选择 Modal
const showImageSelectModal = () => {
  selectedImage.value = ''
  imageModalVisible.value = true
}

// 选择图片
const selectImage = src => {
  selectedImage.value = src
}

// 插入图片
const handleInsertImage = () => {
  if (!selectedImage.value) {
    message.error('请选择要插入的图片')
    return
  }

  if (props.editor) {
    const imageAttrs = {
      src: selectedImage.value
    }

    props.editor.chain().focus().insertImage(imageAttrs).run()
    imageModalVisible.value = false
    message.success('图片插入成功')
  }
}

// 监听编辑器选择变化，更新工具栏状态
const updateToolbarState = () => {
  if (!props.editor) return

  // 获取当前选择的文本样式
  const { from, to } = props.editor.state.selection

  // 获取字体
  const fontFamily = props.editor.getAttributes('textStyle').fontFamily
  currentFontFamily.value = fontFamily || '宋体'

  // 获取字号
  const fontSize = props.editor.getAttributes('textStyle').fontSize
  if (fontSize) {
    // 根据pt值找到对应的中文字号
    const sizeOption = fontSizeOptions.value.find(option => option.value === fontSize)
    currentFontSize.value = sizeOption ? sizeOption.label : fontSize
  } else {
    currentFontSize.value = '五号'
  }

  // 获取颜色
  const color = props.editor.getAttributes('textStyle').color
  currentColor.value = color || ''

  // 获取缩进状态
  const paragraphAttrs = props.editor.getAttributes('paragraph')
  const headingAttrs = props.editor.getAttributes('heading')
  const textIndent = paragraphAttrs.textIndent || headingAttrs.textIndent
  currentTextIndent.value = textIndent === '2em'

  // 获取行间距
  const lineHeight = paragraphAttrs.lineHeight || headingAttrs.lineHeight
  currentLineHeight.value = lineHeight || '1'
}

// 监听编辑器变化
watch(
  () => props.editor,
  (newEditor, oldEditor) => {
    // 移除旧编辑器的监听器
    if (oldEditor) {
      oldEditor.off('selectionUpdate', updateToolbarState)
      oldEditor.off('transaction', updateToolbarState)
    }

    // 添加新编辑器的监听器
    if (newEditor) {
      newEditor.on('selectionUpdate', updateToolbarState)
      newEditor.on('transaction', updateToolbarState)
      updateToolbarState()
    }
  },
  { immediate: true }
)

// 组件挂载时初始化状态
onMounted(() => {
  fetchAvailableFields()
  initAvailableImages()
})

// 组件卸载时清理事件监听器
onBeforeUnmount(() => {
  if (props.editor) {
    props.editor.off('selectionUpdate', updateToolbarState)
    props.editor.off('transaction', updateToolbarState)
  }
})

// 字体菜单点击处理函数
const handleFontFamilyMenuClick = ({ item, key }) => {
  const fontValue = item?.originItemSpec?.props?.dataFontValue || key
  if (fontValue && props.editor) {
    props.editor.chain().focus().setFontFamily(fontValue).run()
    currentFontFamily.value = fontValue
  }
}

// 字号菜单点击处理函数
const handleFontSizeMenuClick = ({ item, key }) => {
  const sizeValue = item?.originItemSpec?.props?.dataSizeValue || key
  if (sizeValue && props.editor) {
    props.editor.chain().focus().setFontSize(sizeValue).run()
    // 更新显示的字号标签
    const sizeOption = fontSizeOptions.value.find(option => option.value === sizeValue)
    currentFontSize.value = sizeOption ? sizeOption.label : sizeValue
  }
}

// 字体处理函数（保留作为备用）
const handleFontFamilyChange = fontFamily => {
  if (props.editor) {
    if (fontFamily) {
      props.editor.chain().focus().setFontFamily(fontFamily).run()
      currentFontFamily.value = fontFamily
    } else {
      props.editor.chain().focus().unsetFontFamily().run()
      currentFontFamily.value = '宋体'
    }
  }
}

// 字号处理函数（保留作为备用）
const handleFontSizeChange = fontSize => {
  if (props.editor) {
    if (fontSize) {
      props.editor.chain().focus().setFontSize(fontSize).run()
      // 更新显示的字号标签
      const sizeOption = fontSizeOptions.value.find(option => option.value === fontSize)
      currentFontSize.value = sizeOption ? sizeOption.label : fontSize
    } else {
      props.editor.chain().focus().unsetFontSize().run()
      currentFontSize.value = '五号'
    }
  }
}

// 颜色处理函数
const handleColorChange = color => {
  if (props.editor) {
    if (color) {
      props.editor.chain().focus().setColor(color).run()
      currentColor.value = color
    } else {
      props.editor.chain().focus().unsetColor().run()
      currentColor.value = ''
    }
  }
}

// 首行缩进处理函数 - 支持切换
const handleFirstLineIndent = () => {
  if (props.editor) {
    if (currentTextIndent.value) {
      // 当前有缩进，取消缩进
      props.editor.chain().focus().unsetTextIndent().run()
      currentTextIndent.value = false
      message.success('已取消首行缩进')
    } else {
      // 当前无缩进，设置缩进
      props.editor.chain().focus().setFirstLineIndent().run()
      currentTextIndent.value = true
      message.success('已设置首行缩进2字符')
    }
  }
}

// 行间距处理函数
const handleLineHeightChange = ({ key }) => {
  if (props.editor) {
    const lineHeight = key
    props.editor.chain().focus().setLineHeight(lineHeight).run()
    currentLineHeight.value = lineHeight
    message.success(`已设置${lineHeight}倍行距`)
  }
}
</script>

<style lang="less" scoped>
.editor-toolbar {
  padding: 8px;

  .ant-divider-vertical {
    margin: 0 12px;
  }

  .ant-btn:not(.ant-btn-icon-only) {
    padding: 0 10px;
  }
}

// 图片选择器样式
.image-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.image-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border: 2px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  background-color: white;

  &:hover {
    border-color: #40a9ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  }

  &.selected {
    border-color: #1890ff;
    background-color: #e6f7ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  }

  img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    margin-bottom: 6px;
  }

  .image-name {
    font-size: 12px;
    color: #666;
    text-align: center;
    word-break: break-all;
    line-height: 1.2;
  }
}

// 颜色选择器样式
.color-picker-panel {
  padding: 12px;
  width: 240px;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 6px;
  margin-bottom: 8px;
}

.color-item {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #d9d9d9;
  transition: all 0.2s;

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

// 颜色指示器样式
.color-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-left: 4px;
  border: 1px solid #d9d9d9;
  vertical-align: middle;
}

// 字段描述样式
.field-description {
  padding: 12px;
  background-color: #f6f8fa;
  border-radius: 4px;
  border-left: 3px solid #1890ff;

  p {
    margin: 4px 0;
    color: #555;
    font-size: 12px;
    line-height: 1.4;

    &:first-child {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
