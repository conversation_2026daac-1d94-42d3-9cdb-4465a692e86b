import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import { Plugin, PluginKey } from '@tiptap/pm/state'
// 修改路径：指向新的 extensions 目录下的 CustomFieldNodeView.vue
import CustomFieldNodeView from './CustomFieldNodeView.vue'

/**
 * 自定义字段扩展 CustomFieldExtension
 * 该扩展用于在 Tiptap 编辑器中表示可复用的字段。
 * 字段具有以下特点：
 *  - 包含 field_key, field_name, field_type
 *  - 字段的"值"作为节点的 ProseMirror content (类型为 text* | hardBreak*)
 *  - 在非填充模式下：字段显示为模板占位符，不可编辑
 *  - 在填充模式下：字段内容可编辑，用于填入实际值
 *  - 字段作为一个整体单元，但其内容在填充模式下可编辑
 *  - 支持软换行：粘贴内容中的换行和按回车键都转换为软换行（hardBreak）
 */
export const CustomFieldExtension = Node.create({
  name: 'customField', // 节点的唯一名称
  group: 'inline', // 节点组，inline 表示它可以像文本一样在段落中流动
  inline: true, // 明确这是一个内联节点
  atom: false, // 允许光标进入并编辑其内容 (通过 NodeViewContent)
  selectable: true, // 允许节点被选中
  content: '(text | hardBreak)*', // 定义节点的内容模型为文本节点和软换行节点

  addAttributes() {
    return {
      field_key: {
        default: null,
        parseHTML: element => element.getAttribute('data-field-key'),
        renderHTML: attributes => ({
          'data-field-key': attributes.field_key
        })
      },
      field_name: {
        default: '未命名字段',
        parseHTML: element => element.getAttribute('data-field-name'),
        renderHTML: attributes => ({
          'data-field-name': attributes.field_name
        })
      },
      field_type: {
        default: 'custom', // 'custom', 'template_custom', 'pool_custom' 等
        parseHTML: element => element.getAttribute('data-field-type'),
        renderHTML: attributes => ({
          'data-field-type': attributes.field_type
        })
      },
      // 字段值是否在当前模式下可编辑（主要用于填充模式控制）
      isFieldValueEditable: {
        default: true, // 默认可编辑，具体由NodeView根据填充模式动态控制
        parseHTML: element => element.getAttribute('data-is-field-value-editable') === 'true',
        renderHTML: attributes => ({
          'data-is-field-value-editable': attributes.isFieldValueEditable
        })
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-custom-field]'
        // getContent: (domNode) => { ... } // 如果需要更复杂的从DOM提取内容逻辑
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['span', mergeAttributes(HTMLAttributes, { 'data-custom-field': '' })]
  },

  addNodeView() {
    return VueNodeViewRenderer(CustomFieldNodeView)
  },

  addCommands() {
    return {
      insertCustomField:
        attributes =>
        ({ commands }) => {
          const node = {
            type: this.name,
            attrs: {
              field_key: attributes.field_key,
              field_name: attributes.field_name || '未命名字段',
              field_type: attributes.field_type || 'custom',
              isFieldValueEditable: attributes.isFieldValueEditable !== false // 默认为true
            }
            // 初始插入时，可以不带content，让NodeView的placeholder显示
            // 或者插入一个空文本节点
          }
          // 插入字段节点后，在其后插入一个空格，方便用户继续输入普通文本
          return commands.insertContent([node, { type: 'text', text: ' ' }])
        }
    }
  },

  addKeyboardShortcuts() {
    return {
      // 在自定义字段内按回车键时插入软换行而不是创建新段落
      Enter: ({ editor }) => {
        const { state } = editor
        const { selection } = state
        const { from, to } = selection

        // 检查当前光标是否在自定义字段内
        const resolvedFrom = state.doc.resolve(from)
        let parentNode = null
        let depth = resolvedFrom.depth

        // 向上遍历节点树，查找自定义字段节点
        while (depth > 0) {
          const node = resolvedFrom.node(depth)
          if (node.type.name === 'customField') {
            parentNode = node
            break
          }
          depth--
        }

        // 如果在自定义字段内，插入软换行
        if (parentNode) {
          editor.commands.setHardBreak()
          return true
        }

        // 否则使用默认行为
        return false
      }
    }
  },

  addProseMirrorPlugins() {
    return [
      // 处理粘贴事件的插件 - 确保自定义字段内的换行转换为软换行
      new Plugin({
        key: new PluginKey('customFieldPasteHandler'),
        props: {
          handlePaste: (view, event, slice) => {
            const { state } = view
            const { selection } = state
            const { from } = selection

            // 检查当前光标是否在自定义字段内
            const resolvedFrom = state.doc.resolve(from)
            let isInCustomField = false
            let depth = resolvedFrom.depth

            // 向上遍历节点树，查找自定义字段节点
            while (depth > 0) {
              const node = resolvedFrom.node(depth)
              if (node.type.name === 'customField') {
                isInCustomField = true
                break
              }
              depth--
            }

            // 如果在自定义字段内，处理粘贴内容
            if (isInCustomField) {
              // 获取粘贴的纯文本内容
              const text = event.clipboardData?.getData('text/plain') || ''

              if (text) {
                // 阻止默认粘贴行为
                event.preventDefault()

                // 将文本内容中的换行符转换为软换行
                // 使用简单的文本替换方式，直接插入转换后的文本
                const lines = text.split(/\r?\n/)

                let tr = state.tr

                // 删除当前选择的内容（如果有）
                if (!selection.empty) {
                  tr = tr.deleteSelection()
                }

                // 逐行插入内容
                lines.forEach((line, index) => {
                  // 插入文本内容
                  if (line.length > 0) {
                    tr = tr.insertText(line)
                  }

                  // 如果不是最后一行，插入软换行
                  if (index < lines.length - 1) {
                    const hardBreakNode = state.schema.nodes.hardBreak.create()
                    tr = tr.insert(tr.selection.from, hardBreakNode)
                  }
                })

                view.dispatch(tr)

                return true // 已处理粘贴事件
              }
            }

            // 不在自定义字段内，使用默认粘贴行为
            return false
          }
        }
      })
    ]
  }
})

export default CustomFieldExtension
