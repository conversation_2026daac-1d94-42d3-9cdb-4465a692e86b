<template>
  <transition name="tooltip-fade" appear>
    <div v-if="visible" class="risk-tooltip" :style="tooltipStyle">
      <div class="risk-tooltip-header">
        <span class="risk-level-badge" :class="`risk-level-${riskData?.risk_level}`">
          {{ formatRiskLevel(riskData?.risk_level) }}
        </span>
        <span class="field-name">{{ riskData?.field_name }}</span>
      </div>

      <div class="risk-tooltip-content">
        <div class="risk-reason">
          <strong>风险原因：</strong>
          <p>{{ riskData?.reason }}</p>
        </div>

        <div class="risk-suggestion" v-if="riskData?.suggestion">
          <strong>改进建议：</strong>
          <p>{{ riskData?.suggestion }}</p>
        </div>

        <div class="risk-score">
          <span>风险评分：</span>
          <span class="score">{{ riskData?.risk_score }}</span>
        </div>
      </div>

      <div class="risk-tooltip-arrow" :class="arrowPosition"></div>
    </div>
  </transition>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'

// Props定义
const props = defineProps({
  visible: { type: Boolean, default: false },
  riskData: { type: Object, default: null },
  targetElement: { type: Object, default: null },
  position: { type: Object, default: () => ({ x: 0, y: 0 }) }
})

// 响应式数据
const tooltipStyle = ref({})
const arrowPosition = ref('bottom')

// 格式化风险等级
const formatRiskLevel = level => {
  const levelMap = { 高: '高风险', 中: '中风险', 低: '低风险' }
  return levelMap[level] || level
}

// 计算提示框位置
const calculatePosition = () => {
  if (!props.targetElement || !props.visible) return

  nextTick(() => {
    const target = props.targetElement
    const targetRect = target.getBoundingClientRect()
    const tooltipElement = document.querySelector('.risk-tooltip')

    if (!tooltipElement) return

    const tooltipRect = tooltipElement.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight

    let left = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2
    let top = targetRect.top - tooltipRect.height - 10

    // 调整水平位置
    if (left < 10) {
      left = 10
    } else if (left + tooltipRect.width > viewportWidth - 10) {
      left = viewportWidth - tooltipRect.width - 10
    }

    // 调整垂直位置
    if (top < 10) {
      // 如果上方空间不够，显示在下方
      top = targetRect.bottom + 10
      arrowPosition.value = 'top'
    } else {
      arrowPosition.value = 'bottom'
    }

    tooltipStyle.value = {
      position: 'fixed',
      left: `${left}px`,
      top: `${top}px`,
      zIndex: 1000
    }
  })
}

// 监听显示状态和目标元素变化
watch([() => props.visible, () => props.targetElement], calculatePosition, { immediate: true })

// 暴露给父组件的方法
defineExpose({
  calculatePosition
})
</script>

<style scoped>
.risk-tooltip {
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  position: fixed;
  z-index: 1000;
  transition: all 0.2s ease-in-out;
}

.risk-tooltip-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.risk-level-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.risk-level-badge.risk-level-高 {
  background-color: #ff4d4f;
}

.risk-level-badge.risk-level-中 {
  background-color: #ffa500;
}

.risk-level-badge.risk-level-低 {
  background-color: #fadb14;
  color: #333;
}

.field-name {
  font-weight: 500;
  color: #333;
}

.risk-tooltip-content {
  color: #666;
}

.risk-reason,
.risk-suggestion {
  margin-bottom: 8px;
}

.risk-reason strong,
.risk-suggestion strong {
  color: #333;
  display: block;
  margin-bottom: 4px;
}

.risk-reason p,
.risk-suggestion p {
  margin: 0;
  white-space: pre-wrap;
}

.risk-score {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

.score {
  font-weight: 500;
  color: #ff4d4f;
}

.risk-tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.risk-tooltip-arrow.bottom {
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px 6px 0 6px;
  border-color: white transparent transparent transparent;
}

.risk-tooltip-arrow.top {
  top: -6px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 6px 6px 6px;
  border-color: transparent transparent white transparent;
}

.risk-tooltip-arrow.bottom::before {
  content: '';
  position: absolute;
  top: -7px;
  left: -6px;
  border-width: 6px 6px 0 6px;
  border-style: solid;
  border-color: #d9d9d9 transparent transparent transparent;
}

.risk-tooltip-arrow.top::before {
  content: '';
  position: absolute;
  top: 1px;
  left: -6px;
  border-width: 0 6px 6px 6px;
  border-style: solid;
  border-color: transparent transparent #d9d9d9 transparent;
}

/* 过渡动画效果 */
.tooltip-fade-enter-active {
  transition: all 0.2s ease-out;
}

.tooltip-fade-leave-active {
  transition: all 0.15s ease-in;
}

.tooltip-fade-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.tooltip-fade-leave-to {
  opacity: 0;
  transform: translateY(-5px) scale(0.98);
}

.tooltip-fade-enter-to,
.tooltip-fade-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}
</style>
