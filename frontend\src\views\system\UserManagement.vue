<template>
  <div>
    <a-card title="用户管理">
      <a-button type="primary" @click="showCreateModal" style="margin-bottom: 16px">
        <template #icon><PlusOutlined /></template>
        添加用户
      </a-button>

      <a-table :columns="columns" :dataSource="users" :loading="loading" rowKey="id" :pagination="pagination" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 'active' ? 'green' : 'red'">
              {{ record.status === 'active' ? '激活' : '禁用' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="showEditModal(record)">编辑</a-button>
              <a-popconfirm title="确定要删除此用户吗?" ok-text="确定" cancel-text="取消" @confirm="handleDeleteUser(record.id)" :disabled="record.username === 'admin'">
                <a-button type="link" danger :disabled="record.username === 'admin' || record.username == store.state.auth.user.username">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新建/编辑用户 Modal -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalMode === 'create' ? '添加新用户' : '编辑用户'"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="modalLoading"
      destroyOnClose
    >
      <a-form ref="formRef" :model="formState" layout="vertical" :rules="rules">
        <a-form-item label="用户名" name="username">
          <a-input v-model:value="formState.username" :disabled="modalMode === 'edit'" />
        </a-form-item>
        <a-form-item label="昵称" name="nickname">
          <a-input v-model:value="formState.nickname" />
        </a-form-item>
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="formState.email" />
        </a-form-item>
        <a-form-item label="密码" name="password" v-if="modalMode === 'create'">
          <a-input-password v-model:value="formState.password" placeholder="创建时必填" />
        </a-form-item>
        <!-- <a-form-item label="原始密码" name="newPassword" v-if="modalMode === 'edit'">
          <a-input-password v-model:value="formState.password" disabled placeholder="" />
        </a-form-item> -->
        <a-form-item label="新密码" name="newPassword" v-if="modalMode === 'edit'">
          <a-input-password v-model:value="formState.newPassword" placeholder="留空则不修改密码" />
        </a-form-item>
        <a-form-item label="角色" name="role_id">
          <!-- mode="multiple" -->
          <a-select v-model:value="formState.role_id" placeholder="请选择角色">
            <a-select-option v-for="role in roles" :key="role.id" :value="role.id">{{ role.name }} ({{ role.description }})</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-select v-model:value="formState.status">
            <a-select-option value="active">激活</a-select-option>
            <a-select-option value="inactive">禁用</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import api from '@/api'
import dayjs from 'dayjs' // 用于日期格式化
import store from '@/store'

const users = ref([])
const roles = ref([])
const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const modalMode = ref('create') // 'create' or 'edit'
const currentEditUserId = ref(null)

const formRef = ref()
const formState = reactive({
  username: '',
  nickname: '',
  email: '',
  password: '',
  newPassword: '', // 用于编辑时
  role_id: undefined,
  status: 'active'
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100']
})

const rules = {
  username: [{ required: true, message: '请输入用户名' }],
  email: [{ type: 'email', message: '请输入有效的邮箱地址' }],
  password: [{ required: true, message: '请输入密码' }], // 创建时必填
  role_id: [{ required: true, message: '请选择角色' }],
  status: [{ required: true, message: '请选择状态' }]
}

const columns = [
  { title: '用户名', dataIndex: 'username', key: 'username' },
  { title: '昵称', dataIndex: 'nickname', key: 'nickname' },
  { title: '邮箱', dataIndex: 'email', key: 'email' },
  { title: '角色', dataIndex: 'role_name', key: 'role_name' }, // User service 返回了 role_name
  { title: '状态', dataIndex: 'status', key: 'status' },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at' },
  { title: '操作', key: 'action', width: 150 }
]

const formatDate = dateString => {
  return dateString ? dayjs(dateString).format('YYYY-MM-DD HH:mm:ss') : '-'
}

const fetchUsers = async (pg = pagination) => {
  loading.value = true
  try {
    const params = {
      page: pg.current,
      pageSize: pg.pageSize
    }
    const response = await api.user.list(params)
    users.value = response.list
    pagination.total = response.total
  } catch (error) {
    message.error('获取用户列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const fetchRoles = async () => {
  try {
    const response = await api.role.list()
    roles.value = response.list || response.data
  } catch (error) {
    message.error('获取角色列表失败: ' + error.message)
  }
}

const handleTableChange = (pg, filters, sorter) => {
  pagination.current = pg.current
  pagination.pageSize = pg.pageSize
  fetchUsers(pg)
  // TODO: 添加排序和过滤逻辑 (如果后端支持)
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formState.username = ''
  formState.nickname = ''
  formState.email = ''
  formState.password = ''
  formState.newPassword = ''
  formState.role_id = undefined
  formState.status = 'active'
  currentEditUserId.value = null
}

const showCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  modalVisible.value = true
}

const showEditModal = record => {
  modalMode.value = 'edit'
  resetForm() // 先重置
  currentEditUserId.value = record.id
  // 填充表单
  formState.username = record.username
  formState.nickname = record.nickname
  formState.email = record.email
  formState.role_id = record.role_id
  formState.status = record.status
  // formState.password = record.password;

  // 密码字段留空，除非用户输入新密码
  modalVisible.value = true
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true

    // formState.role_id = formState.role_id.map(roleId => roleId.toString());

    const dataToSubmit = {
      nickname: formState.nickname,
      email: formState.email,
      role_id: formState.role_id,
      // role_id: JSON.stringify(formState.role_id || []),
      status: formState.status
    }

    // console.log("formState.role_id",dataToSubmit)
    // return

    if (modalMode.value === 'create') {
      dataToSubmit.username = formState.username
      dataToSubmit.password = formState.password // 创建时密码是必需的
      // 使用新的HTTP API对象结构进行用户注册
      let result = await api.user.register(dataToSubmit)
      if (result.success) {
        message.success('用户添加成功')
      }
    } else {
      if (formState.newPassword) {
        dataToSubmit.password = formState.newPassword // 如果输入了新密码
      }
      dataToSubmit.id = currentEditUserId.value
      // 使用新的HTTP API对象结构进行用户更新
      let result = await api.user.update(dataToSubmit)
      if (result.success) {
        message.success('用户信息更新成功')
      }
    }
    modalVisible.value = false
    fetchUsers() // 重新加载列表
  } catch (errorInfo) {
    if (errorInfo.message) {
      // API 调用错误
      message.error('操作失败: ' + errorInfo.message)
    } else {
      // 表单校验错误
      console.log('表单校验失败:', errorInfo)
      message.error('请检查表单输入项')
    }
  } finally {
    modalLoading.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
}

const handleDeleteUser = async userId => {
  try {
    // 使用新的HTTP API对象结构进行用户删除
    let result = await api.user.delete(userId)
    if (result.success) {
      message.success('用户删除成功')
      fetchUsers() // 重新加载列表
    } else {
      message.error(result.message || '删除失败')
    }
  } catch (error) {
    message.error('删除用户失败: ' + error.message)
  }
}

onMounted(() => {
  fetchUsers()
  fetchRoles()
})
</script>

<style scoped>
/* 可根据需要添加样式 */
</style>
