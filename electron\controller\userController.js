'use strict'

const UserService = require('../service/userService')

/**
 * 用户管理控制器
 * @class
 */
class UserController {
  constructor(ctx) {
    this.userService = new UserService()
  }

  /**
   * 用户注册
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async register(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body
    const { username, password, nickname, email, role_id } = params

    if (!username || !password) {
      return { success: false, message: '用户名和密码不能为空' }
    }

    try {
      const result = await this.userService.register({
        username,
        password,
        nickname,
        email,
        role_id
      })
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 用户登录
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async login(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body
    const { username, password } = params

    if (!username || !password) {
      return { success: false, message: '用户名和密码不能为空' }
    }

    try {
      const result = await this.userService.login(username, password)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取用户列表
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async list(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {}

    try {
      const result = await this.userService.list(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取单个用户信息
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async get(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的用户ID' }
    }

    try {
      const result = await this.userService.getById(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 创建用户
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async create(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body
    const { username, password, nickname, email, role_id } = params

    if (!username || !password) {
      return { success: false, message: '用户名和密码不能为空' }
    }

    try {
      const result = await this.userService.create({
        username,
        password,
        nickname,
        email,
        role_id
      })
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 更新用户信息
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async update(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    const { id, ...updateData } = params
    const userId = parseInt(id)

    if (isNaN(userId)) {
      return { success: false, message: '无效的用户ID' }
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return { success: false, message: '没有提供需要更新的数据' }
    }

    try {
      const result = await this.userService.update(userId, updateData)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 删除用户
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async delete(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的用户ID' }
    }

    try {
      const result = await this.userService.delete(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
}

UserController.toString = () => '[class UserController]'

module.exports = UserController
