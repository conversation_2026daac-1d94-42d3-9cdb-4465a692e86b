<template>
  <div>
    <!-- class="page-container" -->
    <a-card title="招标文件模板">
      <!-- <a-page-header title="招标文件模板" /> -->
      <!-- <a-card :bordered="false"> -->
        <!-- 搜索条件 -->

        <div class="flex justify-between mb-[16px]">
          <a-form layout="inline" :model="queryParams" class="gap-y-[10px]">
            <a-form-item label="模板名称">
              <a-input v-model:value="queryParams.name" placeholder="请输入模板名称" @pressEnter="handleSearch" />
            </a-form-item>
            <a-form-item label="文件类型">
              <a-input v-model:value="queryParams.file_type" placeholder="请输入文件类型" @pressEnter="handleSearch" />
            </a-form-item>
            <!-- TODO: 后续可以添加父模板选择器进行筛选 -->
            <a-form-item>
              <a-button type="primary" @click="handleSearch">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button style="margin-left: 8px" @click="resetQuery">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
            </a-form-item>
          </a-form>

          <!-- 操作按钮 -->
          <a-button type="primary" @click="handleCreate">
            <template #icon><PlusOutlined /></template>
            新建模板
          </a-button>
        </div>
        <!-- 树形表格 -->
        <a-table
          row-key="id"
          :columns="columns"
          :data-source="filteredTreeData"
          :pagination="false"
          :loading="loading"
          :default-expand-all-rows="false"
          :expand-row-by-click="false"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <a-space size="small">
                <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
                <a-popconfirm title="确定删除此模板吗？" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record.id)">
                  <a-button type="link" size="small" danger>删除</a-button>
                </a-popconfirm>
                <!-- TODO: 后续可以添加查看子模板等操作 -->
              </a-space>
            </template>
            <template v-else-if="column.dataIndex === 'parent_id'">
              {{ record.parent_template_name || (record.parent_id ? record.parent_id : '-') }}
              <!-- 优先显示父模板名称，如果只有ID则显示ID，都没有则显示 '-' -->
            </template>
          </template>
        </a-table>
      <!-- </a-card> -->
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue'
// 使用统一的HTTP API
import api from '@/api'

const router = useRouter()

// 查询参数
const queryParams = reactive({
  page: 1,
  pageSize: 10,
  name: '',
  file_type: '',
  parent_id: null // 用于按父模板ID筛选，null 表示顶级或不筛选
})

const templateTreeData = ref([])
const loading = ref(false)
const filteredTreeData = ref([])

// 表格列定义
const columns = [
  { title: '模板名称', dataIndex: 'name', width: 240 },
  { title: '文件类型', dataIndex: 'file_type', width: 120 },
  { title: '备注', dataIndex: 'remark' },
  { title: '创建时间', dataIndex: 'created_at', width: 180 },
  { title: '更新时间', dataIndex: 'updated_at', width: 180 },
  { title: '操作', dataIndex: 'action', width: 150, fixed: 'right' }
]

// 获取模板树数据
const fetchTemplateTree = async () => {
  loading.value = true
  try {
    // 使用新的HTTP API对象结构
    const response = await api.biddingDocumentTemplate.getTree()
    if (response.success && response.data) {
      templateTreeData.value = response.data
      applyFilter() // 应用筛选
    } else {
      message.error(response.message || '获取模板树失败')
      templateTreeData.value = []
    }
  } catch (error) {
    console.error('获取模板树错误:', error)
    message.error('获取模板树时发生错误: ' + error.message)
    templateTreeData.value = []
  } finally {
    loading.value = false
  }
}

// 递归筛选树形数据
const filterTreeData = (data, nameFilter, typeFilter) => {
  return data
    .filter(item => {
      // 检查当前节点是否匹配
      const nameMatch = !nameFilter || item.name.toLowerCase().includes(nameFilter.toLowerCase())
      const typeMatch = !typeFilter || (item.file_type && item.file_type.toLowerCase().includes(typeFilter.toLowerCase()))

      // 递归筛选子节点
      const filteredChildren = item.children ? filterTreeData(item.children, nameFilter, typeFilter) : []

      // 如果当前节点匹配或有匹配的子节点，则保留
      if (nameMatch && typeMatch) {
        return { ...item, children: filteredChildren }
      } else if (filteredChildren.length > 0) {
        return { ...item, children: filteredChildren }
      }

      return false
    })
    .filter(Boolean)
}

// 应用筛选
const applyFilter = () => {
  const nameFilter = queryParams.name.trim()
  const typeFilter = queryParams.file_type.trim()

  if (!nameFilter && !typeFilter) {
    filteredTreeData.value = templateTreeData.value
  } else {
    filteredTreeData.value = filterTreeData(templateTreeData.value, nameFilter, typeFilter)
  }
}

// 搜索操作
const handleSearch = () => {
  applyFilter()
}

// 重置查询条件
const resetQuery = () => {
  queryParams.name = ''
  queryParams.file_type = ''
  queryParams.parent_id = null
  applyFilter()
}

// ---- 新建/编辑操作 ----
const handleCreate = () => {
  router.push({ name: 'BiddingDocumentTemplateNew' })
}

const handleEdit = record => {
  router.push({ name: 'BiddingDocumentTemplateEdit', params: { id: record.id } })
}

// ---- 新建/编辑操作结束 ----

// 删除操作
const handleDelete = async id => {
  try {
    // 使用新的HTTP API对象结构
    const response = await api.biddingDocumentTemplate.delete({ id })
    if (response.success) {
      message.success('模板删除成功')
      fetchTemplateTree() // 重新加载树数据
    } else {
      message.error(response.message || '删除模板失败')
    }
  } catch (error) {
    console.error('删除模板错误:', error)
    message.error('删除模板时发生错误: ' + error.message)
  }
}

// 组件挂载后加载数据
onMounted(() => {
  fetchTemplateTree()
})
</script>

<style lang="less" scoped>
</style>
