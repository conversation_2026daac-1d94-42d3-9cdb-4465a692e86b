<template>
  <div class="ai-chat-sidebar" :class="{ collapsed: collapsed }">
    <!-- 折叠触发器 - 始终可见的tab -->
    <div class="ai-chat-trigger" @click="toggleChat">
      <div class="trigger-content">
        <MessageOutlined v-if="collapsed" />
        <CloseOutlined v-else />
        <span class="trigger-text">AI助手</span>
      </div>
    </div>

    <!-- 聊天面板主体 -->
    <div class="ai-chat-panel">
      <!-- 头部工具栏 -->
      <div class="chat-header">
        <div class="header-left">
          <RobotOutlined class="ai-icon" />
          <span class="title">AI助手</span>
        </div>
        <div class="header-right">
          <a-tooltip title="新对话">
            <PlusOutlined class="header-btn" @click="handleCreateNewSession" />
          </a-tooltip>
          <a-tooltip title="会话列表">
            <UnorderedListOutlined class="header-btn" @click="showSessionList" />
          </a-tooltip>
        </div>
      </div>

      <!-- 当前会话信息 -->
      <div v-if="currentSession" class="current-session">
        <span class="session-title" @click="editSessionTitle">
          {{ currentSession.title }}
        </span>
        <EditOutlined class="edit-icon" @click="editSessionTitle" />
      </div>

      <!-- 聊天内容组件 -->
      <ChatContent
        :current-session="currentSession"
        :messages="messages"
        v-model:input-message="inputMessage"
        :sending="sending"
        :streaming-message="streamingMessage"
        :auto-scroll="autoScroll"
        @send-message="handleSendMessage"
        @cancel-message="handleCancelMessage"
      />
    </div>

    <!-- 会话列表模态框 -->
    <SessionListModal
      v-model:visible="sessionListVisible"
      :sessions="sessions"
      :current-session-id="currentSession?.id"
      @switch-session="handleSwitchSession"
      @edit-session="handleEditSession"
      @delete-session="handleDeleteSession"
      @create-session="handleCreateNewSession"
      @refresh="loadSessions"
    />

    <!-- 编辑会话标题模态框 -->
    <EditSessionModal v-model:visible="editTitleVisible" :session="editingSession" @save="handleSaveSessionTitle" />
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue'
import { MessageOutlined, CloseOutlined, RobotOutlined, PlusOutlined, UnorderedListOutlined, EditOutlined } from '@ant-design/icons-vue'

// 导入子组件
import ChatContent from './ChatContent.vue'
import SessionListModal from './SessionListModal.vue'
import EditSessionModal from './EditSessionModal.vue'

// 导入组合式函数
import { useChatService } from './composables/useChatService.js'

// 使用聊天服务
const {
  // 状态
  currentSession,
  messages,
  sessions,
  sending,
  streamingMessage,
  autoScroll,
  inputMessage,

  // 方法
  createNewSession,
  loadSessions,
  switchSession,
  sendMessage,
  cancelMessage,
  updateSessionTitle,
  deleteSession,
  initChatService
} = useChatService()

// 本地响应式数据
const collapsed = ref(true) // 默认折叠状态

// 模态框相关
const sessionListVisible = ref(false)
const editTitleVisible = ref(false)
const editingSession = ref(null)

// 滚动到底部函数（组件内部实现）
const scrollToBottom = () => {
  if (autoScroll.value) {
    nextTick(() => {
      const bubbleList = document.querySelector('.ant-bubble-list')
      if (bubbleList) {
        bubbleList.scrollTop = bubbleList.scrollHeight
      }
    })
  }
}

// 方法定义
const toggleChat = () => {
  collapsed.value = !collapsed.value
  if (!collapsed.value && !currentSession.value && sessions.value.length > 0) {
    // 打开时如果没有当前会话，选择最近的会话
    handleSwitchSession(sessions.value[0])
  }
}

const handleCreateNewSession = async () => {
  const newSession = await createNewSession()
  if (newSession) {
    sessionListVisible.value = false
    collapsed.value = false
  }
}

const showSessionList = () => {
  sessionListVisible.value = true
  loadSessions()
}

const handleSwitchSession = async session => {
  await switchSession(session)
  sessionListVisible.value = false
}

const handleSendMessage = async () => {
  await sendMessage()
}

const editSessionTitle = () => {
  if (!currentSession.value) return
  editingSession.value = currentSession.value
  editTitleVisible.value = true
}

const handleEditSession = session => {
  editingSession.value = session
  editTitleVisible.value = true
}

const handleSaveSessionTitle = async ({ session, title }) => {
  const success = await updateSessionTitle(session.id, title)
  if (success) {
    editTitleVisible.value = false
    editingSession.value = null
  }
}

const handleDeleteSession = async session => {
  await deleteSession(session)
}

const handleCancelMessage = () => {
  cancelMessage()
}

// 生命周期
onMounted(async () => {
  await initChatService()
})

// 监听消息变化，自动滚动到底部
watch(
  () => messages.length,
  () => {
    scrollToBottom()
  }
)
</script>

<style lang="less" scoped>
.ai-chat-sidebar {
  position: relative;
  height: 100vh;
  display: flex;
  background: white;
  border-left: 1px solid #e8e8e8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  z-index: 10;
  transition: width 0.3s ease;

  // 展开状态宽度
  // width: 420px;
  width: 545px;

  // 折叠状态宽度（只显示trigger）
  &.collapsed {
    width: 50px;
  }

  .ai-chat-trigger {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 120px;
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: -2px 0 8px rgba(24, 144, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px 0 0 8px;
    z-index: 15;

    &:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
      transform: translateY(-50%) translateX(-3px);
      box-shadow: -3px 0 12px rgba(24, 144, 255, 0.3);
    }

    .trigger-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      .trigger-text {
        font-size: 12px;
        font-weight: 500;
        writing-mode: vertical-rl;
        text-orientation: mixed;
        white-space: nowrap;
        letter-spacing: 1px;
      }

      .anticon {
        font-size: 16px;
      }
    }
  }

  .ai-chat-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: 50px;
    background: white;
    transition: opacity 0.3s ease, visibility 0.3s ease;

    .collapsed & {
      opacity: 0;
      visibility: hidden;
      pointer-events: none;
    }

    .chat-header {
      height: 50px;
      padding: 0 25px 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
      background: linear-gradient(90deg, #fafafa 0%, #f5f5f5 100%);
      flex-shrink: 0;

      .header-left {
        display: flex;
        align-items: center;
        gap: 8px;

        .ai-icon {
          color: #1677ff;
          font-size: 18px;
        }

        .title {
          font-weight: 600;
          color: #262626;
          font-size: 16px;
        }
      }

      .header-right {
        display: flex;
        gap: 12px;

        .header-btn {
          color: #8c8c8c;
          cursor: pointer;
          padding: 6px;
          border-radius: 6px;
          transition: all 0.2s;
          font-size: 14px;

          &:hover {
            color: #1890ff;
            background: #f0f7ff;
          }
        }
      }
    }

    // 当前会话信息
    .current-session {
      height: 40px;
      padding: 0 20px;
      background: #f8f9fa;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      align-items: center;
      gap: 8px;
      flex-shrink: 0;

      .session-title {
        flex: 1;
        font-size: 14px;
        color: #595959;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background 0.2s;
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &:hover {
          background: #e6f7ff;
        }
      }

      .edit-icon {
        color: #8c8c8c;
        cursor: pointer;
        font-size: 12px;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-chat-sidebar {
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1000;
    width: 100vw !important;

    &.collapsed {
      width: 50px !important;
    }

    .ai-chat-trigger {
      left: auto;
      right: 0;
      border-radius: 0 0 0 8px;
    }

    .ai-chat-panel {
      margin-left: 0;
      margin-right: 50px;
    }
  }
}
</style>
