import Router from '../router/index';
import store from '../store/index.js';
import AppSiderVue from '@/layouts/AppSider.vue';
import NotFound from "@/views/NotFound.vue";

const whiteList = ['/login','/register']

Router.beforeEach((to, from, next) => {
  if (!store.state.auth.token) {
    if (whiteList.indexOf(to.path) !== -1) {
      // 白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) 
    }
  } else {
    if (to.path == '/login') {
      next({ path: '/' })
    }else{ 
        if(!store.state.auth.isDynamicRoutesAdded){
            store.dispatch("auth/GetInfo").then(res=>{
                 store.dispatch('auth/GenerateRoutes').then(accessRoutes => {
                    let redirarr = ["/tender-scheme","/bidding-document","/system"];//招标方案 招标文件 系统管理
                    function getFirstMatchedPath(a, redirarr) {
                      let matchedPaths = a.filter(item => redirarr.includes(item.path)).map(item => item.path);
      
                      if (matchedPaths.length === redirarr.length) {
                        return "/tender-scheme";  
                      }
                      if (matchedPaths.includes("/bidding-document") && matchedPaths.includes("/tender-scheme")) {
                        return "/tender-scheme";
                      }
                      return matchedPaths.length > 0 ? matchedPaths[0] : null;
                    }
                    // 确保路由顺序
                    let redirect = getFirstMatchedPath(accessRoutes,redirarr)

                    const mainRoute = {
                        path: '/',
                        name: 'routers',
                        component: AppSiderVue,
                        // redirect: '/tender-scheme',
                        redirect: redirect,
                        children: accessRoutes
                    };

                    Router.addRoute(mainRoute);
                    Router.addRoute({
                        path: '/:pathMatch(.*)*',
                        name: 'NotFound',
                        component: NotFound
                    });
                    console.log("用户路由",Router.getRoutes())
                    console.log("用户跳转",redirect)
                    next({ path: redirect, replace: true }); // 确保使用新的重定向路径
                })
            })
        }else{
          next()
        }
    } 
  }
});


