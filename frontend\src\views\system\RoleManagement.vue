<template>
  <div>
    <a-card title="角色管理">
      <a-button type="primary" @click="showCreateModal" style="margin-bottom: 16px">
        <template #icon><PlusOutlined /></template>
        添加角色
      </a-button>

      <a-table :columns="columns" :dataSource="roles" :loading="loading" rowKey="id" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'permissions'">
            <div v-if="record.permissions && record.permissions.length > 0">
              <!-- <a-tag v-for="perm in record.permissions" :key="perm" color="blue" style="margin-bottom: 4px;">
                {{ getPermissionDisplayName(perm) }}
              </a-tag> -->
              <a-tag style="margin-bottom: 4px" :color="'green'" v-for="item in getPermissionDisplayName(record.permissions)">
                {{ item }}
              </a-tag>
            </div>
            <span v-else>-</span>
          </template>
          <template v-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" :disabled="isSystemRole(record.name)" @click="showEditModal(record)">编辑</a-button>
              <a-popconfirm title="确定要删除此角色吗?" ok-text="确定" cancel-text="取消" @confirm="handleDeleteRole(record.id)" :disabled="isSystemRole(record.name)">
                <a-button type="link" danger :disabled="isSystemRole(record.name)">删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新建/编辑角色 Modal -->
    <a-modal
      v-model:visible="modalVisible"
      :title="modalMode === 'create' ? '添加新角色' : '编辑角色'"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="modalLoading"
      destroyOnClose
    >
      <a-form ref="formRef" :model="formState" layout="vertical" :rules="rules">
        <a-form-item label="角色名称 (英文)" name="name">
          <a-input v-model:value="formState.name" :disabled="modalMode === 'edit'" placeholder="如：admin, editor" />
        </a-form-item>
        <a-form-item label="角色描述" name="description">
          <a-input v-model:value="formState.description" placeholder="如：系统管理员, 内容编辑员" />
        </a-form-item>
        <a-form-item label="权限" name="permissions">
          <!-- <a-checkbox-group v-model:value="formState.permissions" style="width: 100%;">
            <a-row :gutter="[8, 8]">
              <a-col v-for="option in permissionOptions" :key="option.value" :span="12">
                <a-checkbox :value="option.value">{{ option.label }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group> -->

          <!--  :checkedKeys="formState.permissions"
            :defaultCheckedKeys="formState.permissions"
            checkStrictly  -->
          <a-tree
            checkable
            @check="handleCheck"
            :treeData="permissionOptions"
            v-model:value="formState.permissions"
            :checkedKeys="formState.permissions"
            :fieldNames="{
              key: 'value',
              title: 'label',
              children: 'children'
            }"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import api from '@/api' // 引入封装的 API 调用
import dayjs from 'dayjs' // 用于日期格式化

const roles = ref([])
const loading = ref(false)
const modalVisible = ref(false)
const modalLoading = ref(false)
const modalMode = ref('create') // 'create' or 'edit'
const currentEditRoleId = ref(null)

const formRef = ref()
const formState = reactive({
  name: '',
  description: '',
  permissions: [] // 存储权限的数组
})

// { label: '查看仪表盘', value: 'view_dashboard' },
// { label: '管理用户', value: 'manage_users' },
// { label: '管理角色', value: 'manage_roles' },
// { label: '管理模板', value: 'manage_templates' },
// { label: '管理方案', value: 'manage_solutions' },
// { label: '管理文件', value: 'manage_documents' },
// { label: '管理字典', value: 'manage_dictionaries' },
// { label: '菜单管理', value: 'manage_menus'},
// 预设的权限列表
const permissionOptions = ref([
  {
    label: '招标方案',
    value: 'TenderSchemeModule',
    children: [
      { label: '模板管理', value: 'TenderSchemeTemplateList' },
      { label: '方案制作', value: 'TenderSchemeProjectList' }
    ]
  },
  {
    label: '招标文件',
    value: 'BiddingDocumentModule',
    children: [
      { label: '模板管理', value: 'BiddingDocumentTemplateList' },
      { label: '文件管理', value: 'BiddingDocumentFileList' }
    ]
  },
  {
    label: '系统管理',
    value: 'SystemManagement',
    children: [
      { label: '用户管理', value: 'SystemUsers' },
      { label: '角色管理', value: 'SystemRoles' },
      { label: '模板字段管理', value: 'SystemTemplateFieldList' }
    ]
  }
])

const handleCheck = (checkedKeys, e) => {
  formState.permissions = checkedKeys
  // 获取当前节点和子节点的 value
  const checked = e.checked ? e.node.value : null

  // 处理选中父节点时，所有子节点选中的情况
  if (e.node.children && e.node.children.length > 0) {
    const childValues = e.node.children.map(child => child.value)
    if (e.checked) {
      // 当父节点被选中时，选中所有子节点
      formState.permissions = [...new Set([...formState.permissions, ...childValues])]
    } else {
      // 当父节点取消选中时，移除所有子节点
      formState.permissions = formState.permissions.filter(value => !childValues.includes(value))
    }
  }

  // 处理子节点全部选中后，父节点自动选中的情况
  permissionOptions.value.forEach(option => {
    if (option.children) {
      const allChildrenChecked = option.children.every(child => checkedKeys.checked.includes(child.value))
      if (allChildrenChecked) {
        formState.permissions.push(option.value)
      } else {
        formState.permissions = formState.permissions.filter(value => value !== option.value)
      }
    }
  })
}

// const getPermissionDisplayName = (permValue) => {
//   if (typeof permValue === 'string') {
//     try {
//       permValue = JSON.parse(permValue);
//     } catch (error) {
//       console.error('JSON解析失败', error);
//       return [];
//     }
//   }

//   if (!Array.isArray(permValue)) {
//     console.error('permValue必须是数组或可解析的JSON数组字符串');
//     return [];
//   }

//   // 顶级权限
//   const excludedTopLevelValues = ['TenderSchemeModule', 'BiddingDocumentModule', 'SystemManagement'];

//   // 递归查找
//   const findPermissionLabel = (value) => {
//     for (let option of permissionOptions.value) {
//       // if (option.value === value) {
//       //   return option.label;
//       // }
//       if (option.value === value && !excludedTopLevelValues.includes(value)) {
//         return option.label;
//       }
//       if (option.children) {
//         for (let child of option.children) {
//           if (child.value === value) {
//             return `${child.label}`;
//           }
//         }
//       }
//     }
//     return value; // 如果找不到，返回原始值
//   };

//   // 返回每个权限项的label
//   // return permValue.map(findPermissionLabel);
//   return permValue
//     .map(findPermissionLabel)
//     .filter(label => !excludedTopLevelValues.includes(label));
// };

const getPermissionDisplayName = permValue => {
  if (typeof permValue === 'string') {
    try {
      permValue = JSON.parse(permValue)
    } catch (error) {
      console.error('JSON解析失败', error)
      return []
    }
  }

  if (!Array.isArray(permValue)) {
    console.error('permValue必须是数组或可解析的JSON数组字符串')
    return []
  }

  if (Array.isArray(permValue) && permValue[0] == 'all') {
    permValue = [
      'TenderSchemeTemplateList',
      'TenderSchemeProjectList',
      'BiddingDocumentTemplateList',
      'BiddingDocumentFileList',
      'SystemUsers',
      'SystemRoles',
      'SystemTemplateFieldList'
    ]
  }

  // if (Array.isArray(permValue) && permValue.length == 0) {
  //   permValue = []
  // }

  // 顶级权限
  const excludedTopLevelValues = ['TenderSchemeModule', 'BiddingDocumentModule', 'SystemManagement']

  // 递归查找
  const findPermissionLabel = value => {
    for (let option of permissionOptions.value) {
      // 顶级权限且需要排除，跳过该权限
      if (option.value === value && excludedTopLevelValues.includes(value)) {
        return ''
      }

      if (option.value === value && !excludedTopLevelValues.includes(value)) {
        return option.label
      }

      // 处理子权限
      if (option.children) {
        for (let child of option.children) {
          if (child.value === value) {
            return `${option.label}-${child.label}`
          }
        }
      }
    }
    return value // 如果找不到，返回原始值
  }

  return permValue.map(findPermissionLabel).filter(label => label !== '')
  // .join(",")
}

const rules = {
  name: [
    { required: true, message: '请输入角色名称 (英文)' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '角色名称只能包含字母、数字和下划线' }
  ],
  description: [{ required: true, message: '请输入角色描述' }]
}

const columns = [
  { title: '角色名称 (标识)', dataIndex: 'name', key: 'name' },
  { title: '描述', dataIndex: 'description', key: 'description' },
  { title: '权限', dataIndex: 'permissions', key: 'permissions', width: '30%' },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at' },
  { title: '操作', key: 'action', width: 150 }
]

const isSystemRole = roleName => {
  // return ['superadmin', 'ordinary_user'].includes(roleName);
  return ['superadmin'].includes(roleName)
}

const formatDate = dateString => {
  return dateString ? dayjs(dateString).format('YYYY-MM-DD HH:mm:ss') : '-'
}

const fetchRoles = async () => {
  loading.value = true
  try {
    // 使用新的HTTP API对象结构
    const response = await api.role.list()
    roles.value = response.data || response
  } catch (error) {
    message.error('获取角色列表失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  formState.name = ''
  formState.description = ''
  formState.permissions = []
  currentEditRoleId.value = null
}

const showCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  modalVisible.value = true
}

const showEditModal = record => {
  modalMode.value = 'edit'
  resetForm()
  currentEditRoleId.value = record.id

  formState.name = record.name
  formState.description = record.description
  // permissions 已经是数组了，因为 fetchRoles 中做过转换

  formState.permissions = record.permissions && !Array.isArray(record.permissions) ? [...JSON.parse(record.permissions)] : []

  modalVisible.value = true
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true

    // 权限需要转换为 JSON 字符串再提交
    const dataToSubmit = {
      name: formState.name,
      description: formState.description,
      permissions: JSON.stringify(formState.permissions || []) // 确保是数组，然后转字符串
    }

    if (modalMode.value === 'create') {
      // 使用新的HTTP API对象结构进行角色创建
      let result = await api.role.create(dataToSubmit)
      if (result.status == 409) {
        message.error('角色名称已存在')
      } else if (result.status == 201) {
        message.success('角色添加成功')
      }
    } else {
      dataToSubmit.id = currentEditRoleId.value
      // 使用新的HTTP API对象结构进行角色更新
      let result = await api.role.update(dataToSubmit)
      message.success(result.message)
    }
    modalVisible.value = false
    fetchRoles() // 重新加载列表
  } catch (errorInfo) {
    if (errorInfo && errorInfo.message) {
      // API 调用错误
      message.error('操作失败: ' + errorInfo.message)
    } else if (errorInfo && errorInfo.errorFields) {
      // 表单校验错误
      console.log('表单校验失败:', errorInfo)
      message.error('请检查表单输入项')
    } else {
      message.error('操作失败，未知错误')
      console.error('未知错误:', errorInfo)
    }
  } finally {
    modalLoading.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
}

const handleDeleteRole = async roleId => {
  const roleToDelete = roles.value.find(r => r.id === roleId)
  if (roleToDelete && isSystemRole(roleToDelete.name)) {
    message.warn('系统内置角色不能删除')
    return
  }
  try {
    // 使用新的HTTP API对象结构进行角色删除
    let result = await api.role.delete(roleId)
    if (result.status == 200) {
      message.success('角色删除成功')
    } else if (result.status == 404) {
      message.error('角色未找到或删除失败')
    }
    fetchRoles() // 重新加载列表
  } catch (error) {
    message.error('删除角色失败: ' + error.message)
  }
}

onMounted(() => {
  fetchRoles()
})
</script>

<style scoped>
/* 可根据需要添加样式 */
.ant-checkbox-group {
  width: 100%;
}
.ant-checkbox-group .ant-row .ant-col {
  margin-bottom: 8px; /* 给checkbox之间增加一些垂直间距 */
}
</style>
