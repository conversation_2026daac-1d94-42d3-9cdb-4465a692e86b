'use strict'

const RiskAssessmentService = require('../service/riskAssessmentService')

/**
 * 风险评估控制器
 * 处理风险评估相关的HTTP请求
 */
class RiskAssessmentController {
  constructor(ctx) {
    this.riskAssessmentService = new RiskAssessmentService()
  }
  /**
   * 启动风险评估
   * POST /api/risk-assessment/start
   */
  async startAssessment(args, ctx) {
    try {
      const { document_type, document_id } = args

      // 从请求头获取用户ID
      const userId = ctx && ctx.headers ? parseInt(ctx.headers['x-user-id']) : null

      if (!userId) return { success: false, message: '用户未登录' }
      if (!document_type || !document_id) return { success: false, message: '文档类型和文档ID不能为空' }
      if (!['tender_scheme', 'bidding_document'].includes(document_type)) return { success: false, message: '文档类型不正确' }

      const taskId = await this.riskAssessmentService.startAssessment(document_type, document_id, userId)

      return { success: true, message: '风险评估已启动', data: { taskId } }
    } catch (error) {
      console.error('启动风险评估失败:', error)
      return { success: false, message: error.message || '启动风险评估失败' }
    }
  }

  /**
   * 获取风险评估信息
   * GET /api/risk-assessment/:risk_assessment_id
   */
  async getAssessmentInfo(args, ctx) {
    try {
      const { risk_assessment_id } = args

      // 从请求头获取用户ID
      const userId = ctx && ctx.headers ? parseInt(ctx.headers['x-user-id']) : null

      if (!userId) return { success: false, message: '用户未登录' }
      if (!risk_assessment_id) return { success: false, message: '风险评估ID不能为空' }

      const assessment = this.riskAssessmentService.getAssessmentInfo(risk_assessment_id)
      if (!assessment) {
        return { success: false, message: '风险评估结果不存在' }
      }

      // 解析AI结果
      let aiResult = null
      if (assessment.ai_result) {
        try {
          aiResult = JSON.parse(assessment.ai_result)
        } catch (e) {
          console.error('AI结果解析失败:', e)
        }
      }

      return { success: true, data: { ...assessment, ai_result: aiResult } }
    } catch (error) {
      console.error('获取风险评估信息失败:', error)
      return { success: false, message: error.message || '获取风险评估信息失败' }
    }
  }

  /**
   * 获取用户的评估任务列表
   * GET /api/risk-assessment/user-tasks
   */
  async getUserTasks(args, ctx) {
    try {
      // 从请求头获取用户ID
      const userId = ctx && ctx.headers ? parseInt(ctx.headers['x-user-id']) : null

      if (!userId) return { success: false, message: '用户未登录' }

      const options = {}

      // 提取查询参数
      if (args.status) options.status = args.status
      if (args.document_type) options.documentType = args.document_type
      if (args.limit) options.limit = parseInt(args.limit)

      const tasks = this.riskAssessmentService.getUserTasks(userId, options)

      // 解析AI结果
      const processedTasks = tasks.map(task => {
        let aiResult = null
        if (task.ai_result) {
          try {
            aiResult = JSON.parse(task.ai_result)
          } catch (e) {
            console.error('AI结果解析失败:', e)
          }
        }
        return { ...task, ai_result: aiResult }
      })

      return { success: true, data: processedTasks }
    } catch (error) {
      console.error('获取用户任务列表失败:', error)
      return { success: false, message: error.message || '获取用户任务列表失败' }
    }
  }

  /**
   * 取消评估任务
   * POST /api/risk-assessment/cancel/:taskId
   */
  async cancelTask(args, ctx) {
    try {
      const { taskId } = args

      // 从请求头获取用户ID
      const userId = ctx && ctx.headers ? parseInt(ctx.headers['x-user-id']) : null

      if (!userId) return { success: false, message: '用户未登录' }
      if (!taskId) return { success: false, message: '任务ID不能为空' }

      await this.riskAssessmentService.cancelTask(taskId, userId)

      return { success: true, message: '评估任务已取消' }
    } catch (error) {
      console.error('取消评估任务失败:', error)
      return { success: false, message: error.message || '取消评估任务失败' }
    }
  }
}

RiskAssessmentController.toString = () => '[class RiskAssessmentController]'

module.exports = RiskAssessmentController
