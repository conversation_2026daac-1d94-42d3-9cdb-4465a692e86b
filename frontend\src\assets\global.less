* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100vh;
  overflow-x: hidden;
}

.ant-page-header{
  // border: 1px solid rgb(241, 239, 239);
  // margin-left: -10px;
  height: 45px;
  line-height: 45px;
  padding: 0;
  margin-bottom: 5px;
}

// a-card字体
.ant-card-head-title{
  font-size: 20px;
}
/* 滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 4px;
}
::-webkit-scrollbar-button {
  width: 10px;
  height: 0;
}
::-webkit-scrollbar-track {
  background: 0 0;
}
::-webkit-scrollbar-thumb {
  background: #e6f4ff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: #4096ff;
}
::-webkit-scrollbar-thumb:active {
  background-color: #4096ff;
}
