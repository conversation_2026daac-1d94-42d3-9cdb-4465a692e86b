// TiptapEditor 组件样式文件
// 此文件包含所有编辑器相关的样式定义

// 编辑器外层容器样式
.tiptap-editor-wrapper {
  width: 100%;
  background-color: #f5f5f5; // 灰色背景，模拟办公环境
  padding: 0;
  min-height: 100vh;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

// 吸顶工具栏容器样式
.sticky-toolbar-container {
  position: sticky;
  top: -16px;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000; // 确保在其他内容之上
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px; // 与下方内容的间距
  transition: all 0.3s ease; // 添加过渡动画
  backdrop-filter: blur(10px); // 毛玻璃效果（现代浏览器支持）
  -webkit-backdrop-filter: blur(10px); // Safari支持
  border-radius: 8px;

  // 鼠标悬停效果
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  // 工具栏内部样式
  :deep(.editor-toolbar) {
    max-width: 100%;
    box-sizing: border-box;
    position: relative;

    // 添加吸顶指示器
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40px;
      height: 3px;
      background: linear-gradient(90deg, #1890ff, #52c41a);
      border-radius: 0 0 3px 3px;
      opacity: 0.8;
    }

    // 工具栏按钮优化
    .toolbar-button,
    button {
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.tiptap-editor-container {
  // A4纸张尺寸设置：21.0cm x 29.7cm
  width: 21cm;
  min-height: 29.7cm;
  max-width: 21cm;
  margin: 0 auto; // 水平居中显示
  border: 1px solid #d9d9d9;
  border-radius: 8px; // 增加圆角，现在工具栏不在容器内
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); // 添加阴影效果，模拟纸张
  overflow: hidden; // 确保内容不会溢出
}

.editor-wrapper {
  // A4纸张页边距设置：上下2.54cm，左右3.18cm
  padding: 2.54cm 3.18cm;
  transition: border-color 0.3s, box-shadow 0.3s;
  min-height: inherit;
  position: relative;
  box-sizing: border-box; // 确保padding计算在内

  // 实际内容区域：宽度14.64cm(21.0-3.18*2)，高度24.62cm(29.7-2.54*2)

  &.is-focused {
    border-color: #40a9ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &.is-disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
    .ProseMirror {
      cursor: not-allowed;
      :deep(.custom-field-node-view .field-value[contenteditable='false']) {
        color: #555;
        background-color: #eee;
        cursor: default;
        &:empty::before {
          content: '[不可编辑]';
          color: #999;
          cursor: default;
        }
      }
    }
  }

  // 填充模式样式
  &.is-fill-mode {
    border-color: #52c41a;
    background-color: #f6ffed;

    &::before {
      content: '填充模式';
      position: absolute;
      top: -12px;
      right: 8px;
      background: #52c41a;
      color: white;
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 4px;
      z-index: 1;
    }

    &.is-focused {
      border-color: #52c41a;
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
    }
  }

  :deep(.ProseMirror) {
    outline: none;
    // 确保内容区域的最小高度符合A4纸张要求（A4高度减去上下页边距）
    min-height: 24.62cm; // 29.7cm - 2.54cm * 2
    caret-color: currentColor;

    p.is-editor-empty:first-child::before {
      content: attr(data-placeholder);
      float: left;
      color: #adb5bd;
      pointer-events: none;
      height: 0;
    }

    // 填充模式下的特殊样式
    .is-fill-mode & {
      // 非自定义字段内容在填充模式下的视觉提示
      p:not(:has(.custom-field-node-view)),
      h1:not(:has(.custom-field-node-view)),
      h2:not(:has(.custom-field-node-view)),
      h3:not(:has(.custom-field-node-view)),
      h4:not(:has(.custom-field-node-view)),
      h5:not(:has(.custom-field-node-view)),
      h6:not(:has(.custom-field-node-view)),
      li:not(:has(.custom-field-node-view)),
      table:not(:has(.custom-field-node-view)),
      blockquote:not(:has(.custom-field-node-view)) {
        position: relative;
        opacity: 0.6;
        user-select: none;
        pointer-events: none; // 完全禁用鼠标交互
        background-color: rgba(0, 0, 0, 0.02);
        border-radius: 2px;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: repeating-linear-gradient(45deg, transparent, transparent 2px, rgba(255, 0, 0, 0.1) 2px, rgba(255, 0, 0, 0.1) 4px);
          pointer-events: none;
          z-index: 1;
        }

        &::after {
          content: '此内容在填充模式下不可编辑';
          position: absolute;
          right: 4px;
          top: 50%;
          transform: translateY(-50%);
          color: #999;
          font-size: 10px;
          opacity: 0.7;
          background: rgba(255, 255, 255, 0.8);
          padding: 2px 4px;
          border-radius: 2px;
          z-index: 2;
          white-space: nowrap;
        }
      }

      // 包含自定义字段的元素保持正常
      p:has(.custom-field-node-view),
      h1:has(.custom-field-node-view),
      h2:has(.custom-field-node-view),
      h3:has(.custom-field-node-view),
      h4:has(.custom-field-node-view),
      h5:has(.custom-field-node-view),
      h6:has(.custom-field-node-view),
      li:has(.custom-field-node-view) {
        opacity: 1;
        user-select: auto;
        pointer-events: auto;
        background-color: transparent;

        &::before,
        &::after {
          display: none;
        }
      }
    }

    // 只读节点样式（支持所有一级节点）
    [data-is-readonly='true'] {
      background-color: #f5f5f5;
      border-left: 3px solid #1890ff;
      padding: 8px 12px;
      margin: 4px 0;
      border-radius: 4px;
      position: relative;
      cursor: default;

      &::before {
        content: '继承内容（只读）';
        text-indent: 0;
        position: absolute;
        top: -8px;
        right: 8px;
        background: #1890ff;
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 2px;
        z-index: 1;
      }

      &:hover {
        background-color: #f0f0f0;
      }

      // 特殊处理表格的只读样式
      &.ProseMirror table {
        border: 2px solid #1890ff;
        background-color: rgba(24, 144, 255, 0.05);
      }

      // 特殊处理列表的只读样式
      &.ProseMirror ul,
      &.ProseMirror ol {
        background-color: rgba(24, 144, 255, 0.05);
        border-radius: 4px;
        padding: 8px 16px;
      }

      // 特殊处理分页符的只读样式
      &[data-page-break] {
        border-color: #1890ff;
        background-color: rgba(24, 144, 255, 0.1);
      }

      // 目录的只读样式由 TableOfContentsNodeView.vue 组件自己处理

      // 特殊处理水平线的只读样式
      &.ProseMirror hr {
        border-top-color: #1890ff;
        background-color: rgba(24, 144, 255, 0.1);
        padding: 4px 0;
        margin: 8px 0;
      }
    }

    // 标题样式 - 参考DOCX默认样式
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      color: #333;
      margin: 0.5em 0;
      line-height: 1.3;
      font-weight: bold;
    }

    h1 {
      font-size: 16pt; // 三号字
      margin-top: 12pt;
      margin-bottom: 6pt;
    }

    h2 {
      font-size: 15pt; // 小三号字
      margin-top: 10pt;
      margin-bottom: 6pt;
    }

    h3 {
      font-size: 14pt; // 四号字
      margin-top: 10pt;
      margin-bottom: 6pt;
    }

    h4 {
      font-size: 12pt; // 小四号字
      margin-top: 8pt;
      margin-bottom: 4pt;
    }

    h5 {
      font-size: 10.5pt; // 五号字
      margin-top: 8pt;
      margin-bottom: 4pt;
    }

    h6 {
      font-size: 9pt; // 小五号字
      margin-top: 6pt;
      margin-bottom: 3pt;
      font-weight: normal;
      font-style: italic;
    }

    // 段落样式
    p {
      margin: 6pt 0;
      line-height: 1.15; // DOCX默认行间距
      font-size: 10.5pt; // 五号字
      font-family: '宋体';
      color: #000;
    }

    // 列表样式 - 参考DOCX默认样式
    ul,
    ol {
      margin: 6pt 0;
      padding-left: 18pt; // DOCX默认缩进
      line-height: 1.15;
    }

    ul li {
      margin: 3pt 0;
      list-style-type: disc;
    }

    ol li {
      margin: 3pt 0;
      list-style-type: decimal;
    }

    // 嵌套列表样式
    ul ul li {
      list-style-type: circle;
    }

    ul ul ul li {
      list-style-type: square;
    }

    ol ol li {
      list-style-type: lower-alpha;
    }

    ol ol ol li {
      list-style-type: lower-roman;
    }

    // 引用块样式
    blockquote {
      margin: 6pt 0 6pt 18pt;
      padding-left: 12pt;
      border-left: 3pt solid #ddd;
      color: #666;
      font-style: italic;
      background-color: #f9f9f9;
      padding-top: 6pt;
      padding-bottom: 6pt;
      padding-right: 6pt;
    }

    // 代码块样式
    pre {
      background-color: #f5f5f5;
      border: 1pt solid #ddd;
      border-radius: 3pt;
      padding: 8pt;
      margin: 6pt 0;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 9pt; // 小五号字
      line-height: 1.2;
      overflow-x: auto;
    }

    // 行内代码样式
    code {
      background-color: #f0f0f0;
      border: 1pt solid #ddd;
      border-radius: 2pt;
      padding: 1pt 3pt;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 0.9em;
      color: #d73a49;
    }

    // 分割线样式
    hr {
      border: none;
      border-top: 1pt solid #ddd;
      margin: 12pt 0;
      height: 0;
    }

    // 强调文本样式
    strong,
    b {
      font-weight: bold;
    }

    em,
    i {
      font-style: italic;
    }

    // 下划线样式
    u {
      text-decoration: underline;
    }

    // 删除线样式
    s,
    del {
      text-decoration: line-through;
    }

    // 高亮样式（使用 mark 标签）
    mark {
      padding: 0.125em 0;
      border-radius: 0.25em;
      box-decoration-break: clone;
      -webkit-box-decoration-break: clone;

      // 不同颜色的高亮
      &[data-color='#FFFF00'] {
        background-color: #ffff00;
      } // 黄色
      &[data-color='#FFE600'] {
        background-color: #ffe600;
      }
      &[data-color='#FFCC00'] {
        background-color: #ffcc00;
      }
      &[data-color='#FFB300'] {
        background-color: #ffb300;
      }
      &[data-color='#FF9900'] {
        background-color: #ff9900;
      } // 橙色
      &[data-color='#CCFF00'] {
        background-color: #ccff00;
      }
      &[data-color='#99FF00'] {
        background-color: #99ff00;
      }
      &[data-color='#66FF00'] {
        background-color: #66ff00;
      } // 绿色
      &[data-color='#00FFCC'] {
        background-color: #00ffcc;
      }
      &[data-color='#00CCFF'] {
        background-color: #00ccff;
      }
      &[data-color='#0099FF'] {
        background-color: #0099ff;
      } // 蓝色
      &[data-color='#CC99FF'] {
        background-color: #cc99ff;
      }
      &[data-color='#FF99CC'] {
        background-color: #ff99cc;
      }
      &[data-color='#FF6699'] {
        background-color: #ff6699;
      } // 紫粉色
      &[data-color='#FFD1DC'] {
        background-color: #ffd1dc;
      }

      // 默认高亮颜色
      &:not([data-color]) {
        background-color: #ffff00; // 默认黄色
      }
    }

    // 自定义字段样式优化
    .custom-field-node-view {
      .field-value {
        font-size: inherit; // 继承父元素字号
        line-height: inherit; // 继承父元素行高

        &:empty::before {
          font-size: inherit;
          line-height: inherit;
        }
      }
    }

    // 分页符样式
    [data-page-break] {
      page-break-before: always;
      border-top: 2px dashed #ccc;
      margin: 16px 0;
      padding: 8px 0;
      text-align: center;
      color: #666;
      font-size: 12px;
      background-color: #f9f9f9;
      cursor: pointer;

      &:hover {
        background-color: #f0f0f0;
        border-color: #999;
      }
    }

    // 目录样式
    [data-table-of-contents] {
      border: 2px dashed #1890ff;
      margin: 16px 0;
      padding: 16px;
      background-color: #f6f8fa;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        background-color: #e6f7ff;
        border-color: #40a9ff;
      }
    }

    // 图片样式增强 - 内联显示
    .image-node-view {
      display: inline-block;
      vertical-align: middle;
      margin: 0 2px;

      img {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        vertical-align: middle;

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }
      }

      &.is-selected img {
        box-shadow: 0 0 0 2px #1890ff;
      }
    }

    // 链接样式
    a {
      color: #1890ff;
      text-decoration: underline;
      cursor: pointer;

      &:hover {
        color: #40a9ff;
        text-decoration: none;
      }

      &:visited {
        color: #722ed1;
      }
    }

    // 图片样式
    img {
      max-width: 100%;
      height: auto;
      border-radius: 4px;
      // margin: 6pt 0;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    // 文本对齐样式
    .has-text-align-left {
      text-align: left;
    }

    .has-text-align-center {
      text-align: center;
    }

    .has-text-align-right {
      text-align: right;
    }

    .has-text-align-justify {
      text-align: justify;
    }

    // 临时高亮样式
    .temp-highlight {
      background-color: rgba(24, 144, 255, 0.3) !important;
      border: 2px solid #1890ff !important;
      border-radius: 4px;
      box-shadow: 0 0 8px rgba(24, 144, 255, 0.4) !important;
      transition: all 0.3s ease;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin: 1em 0;
      border: 1px solid #dadce0;

      th,
      td {
        border: 1px solid #dadce0;
        padding: 0.5em 0.75em;
        vertical-align: top;
        min-width: 1em;
        box-sizing: border-box;
        position: relative;
      }

      th {
        font-weight: bold;
        text-align: left;
        background-color: #f8f9fa;
      }

      .ProseMirror-tableWrapper {
        margin: 1em 0;
        overflow-x: auto;
      }

      .column-resize-handle {
        position: absolute;
        right: -2px;
        top: 0;
        bottom: 0;
        width: 4px;
        background: #adf;
        pointer-events: auto;
        cursor: col-resize;
        z-index: 10;
      }

      .selectedCell:after {
        z-index: 2;
        position: absolute;
        content: '';
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background: rgba(173, 216, 230, 0.4);
        pointer-events: none;
      }
    }
  }
}
