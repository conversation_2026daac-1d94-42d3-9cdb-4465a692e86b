/**
 * API 统一入口文件
 * 基于ee-core框架的自动路由机制：/controller/controllerName/methodName 或 /controllerName/methodName
 */

// 导入HTTP工具方法
import { httpRequest, get, post, put, del, upload, setApiBaseUrl } from './utils'

/**
 * API配置定义
 * ee-core自动路由格式：controller/{controllerName}/{methodName}
 */
const apiConfig = {
  // 用户管理模块
  user: {
    methods: {
      register: { method: 'POST', url: '/controller/userController/register' },
      login: { method: 'POST', url: '/controller/userController/login' },
      list: { method: 'GET', url: '/controller/userController/list' },
      getById: { method: 'GET', url: '/controller/userController/get' },
      create: { method: 'POST', url: '/controller/userController/create' },
      update: { method: 'PUT', url: '/controller/userController/update' },
      delete: { method: 'DELETE', url: '/controller/userController/delete' }
    }
  },

  // 角色管理模块
  role: {
    methods: {
      list: { method: 'GET', url: '/controller/roleController/list' },
      getById: { method: 'GET', url: '/controller/roleController/get' },
      create: { method: 'POST', url: '/controller/roleController/create' },
      update: { method: 'PUT', url: '/controller/roleController/update' },
      delete: { method: 'DELETE', url: '/controller/roleController/delete' }
    }
  },

  // 模板字段管理模块
  templateField: {
    methods: {
      list: { method: 'GET', url: '/controller/templateFieldController/list' },
      getById: { method: 'GET', url: '/controller/templateFieldController/get' },
      create: { method: 'POST', url: '/controller/templateFieldController/create' },
      update: { method: 'PUT', url: '/controller/templateFieldController/update' },
      delete: { method: 'DELETE', url: '/controller/templateFieldController/delete' }
    }
  },

  // 招标方案模板管理模块
  tenderSchemeTemplate: {
    methods: {
      list: { method: 'GET', url: '/controller/tenderSchemeTemplateController/list' },
      getById: { method: 'GET', url: '/controller/tenderSchemeTemplateController/get' },
      create: { method: 'POST', url: '/controller/tenderSchemeTemplateController/create' },
      update: { method: 'PUT', url: '/controller/tenderSchemeTemplateController/update' },
      delete: { method: 'DELETE', url: '/controller/tenderSchemeTemplateController/delete' }
    }
  },

  // 招标方案管理模块
  tenderScheme: {
    methods: {
      list: { method: 'GET', url: '/controller/tenderSchemeController/list' },
      getById: { method: 'GET', url: '/controller/tenderSchemeController/get' },
      create: { method: 'POST', url: '/controller/tenderSchemeController/create' },
      update: { method: 'PUT', url: '/controller/tenderSchemeController/update' },
      delete: { method: 'DELETE', url: '/controller/tenderSchemeController/delete' },
      getHistory: { method: 'GET', url: '/controller/tenderSchemeController/getHistory' }
    }
  },

  // 招标文件模板管理模块
  biddingDocumentTemplate: {
    methods: {
      list: { method: 'GET', url: '/controller/biddingDocumentTemplateController/list' },
      getById: { method: 'GET', url: '/controller/biddingDocumentTemplateController/getById' },
      getTree: { method: 'GET', url: '/controller/biddingDocumentTemplateController/getTree' },
      create: { method: 'POST', url: '/controller/biddingDocumentTemplateController/create' },
      update: { method: 'PUT', url: '/controller/biddingDocumentTemplateController/update' },
      delete: { method: 'DELETE', url: '/controller/biddingDocumentTemplateController/delete' }
    }
  },

  // 招标文件管理模块
  biddingDocument: {
    methods: {
      list: { method: 'GET', url: '/controller/biddingDocumentController/list' },
      getById: { method: 'GET', url: '/controller/biddingDocumentController/getById' },
      create: { method: 'POST', url: '/controller/biddingDocumentController/create' },
      update: { method: 'PUT', url: '/controller/biddingDocumentController/update' },
      delete: { method: 'DELETE', url: '/controller/biddingDocumentController/delete' },
      getHistory: { method: 'GET', url: '/controller/biddingDocumentController/getHistory' }
    }
  },

  // AI聊天模块
  aiChat: {
    methods: {
      // 会话管理
      createSession: { method: 'POST', url: '/controller/aiChatController/createSession' },
      getSessionList: { method: 'GET', url: '/controller/aiChatController/getSessionList' },
      updateSession: { method: 'PUT', url: '/controller/aiChatController/updateSession' },
      deleteSession: { method: 'DELETE', url: '/controller/aiChatController/deleteSession' },

      // 消息管理
      getMessages: { method: 'GET', url: '/controller/aiChatController/getMessages' },

      // 流式聊天（通过fetch直接调用，不使用统一API）
      streamChat: { method: 'POST', url: '/controller/aiChatController/streamChat' }
    }
  },

  // 风险评估模块
  riskAssessment: {
    methods: {
      // 启动风险评估
      startAssessment: { method: 'POST', url: '/controller/riskAssessmentController/startAssessment' },
      // 获取评估信息
      getAssessmentInfo: { method: 'GET', url: '/controller/riskAssessmentController/getAssessmentInfo' },
      // 获取用户任务列表
      getUserTasks: { method: 'GET', url: '/controller/riskAssessmentController/getUserTasks' },
      // 取消评估任务
      cancelTask: { method: 'POST', url: '/controller/riskAssessmentController/cancelTask' }
    }
  },

  // 系统操作模块
  os: {
    methods: {
      // 读取本地文件
      readLocalFile: { method: 'POST', url: '/controller/os/readLocalFile' },
      // 消息提示
      messageShow: { method: 'POST', url: '/controller/os/messageShow' },
      // 选择文件夹
      selectFolder: { method: 'POST', url: '/controller/os/selectFolder' },
      // 选择图片
      selectPic: { method: 'POST', url: '/controller/os/selectPic' }
    }
  }
}

/**
 * 自动生成API方法
 * 根据配置自动创建对应的API调用函数
 */
function createApiMethod(config) {
  return async function (params = {}) {
    const { method, url } = config

    try {
      // 处理路径参数（如 :id）
      let finalUrl = url
      const pathParams = {}
      const queryOrBodyParams = { ...params }

      // 如果URL包含 :id 等路径参数，进行替换
      if (url.includes('/:id') || url.includes(':id')) {
        if (params.id !== undefined) {
          finalUrl = url.replace(':id', params.id).replace('/:id', `/${params.id}`)
          pathParams.id = params.id
          delete queryOrBodyParams.id
        } else {
          console.warn('[API] URL需要id参数但未提供:', url)
        }
      }

      console.log('[createApiMethod] 发送请求:', { method, url: finalUrl, params: queryOrBodyParams })

      let response
      switch (method.toUpperCase()) {
        case 'GET':
          response = await get(finalUrl, queryOrBodyParams)
          break
        case 'POST':
          response = await post(finalUrl, queryOrBodyParams)
          break
        case 'PUT':
          response = await put(finalUrl, queryOrBodyParams)
          break
        case 'DELETE':
          response = await del(finalUrl, queryOrBodyParams)
          break
        default:
          throw new Error(`不支持的HTTP方法: ${method}`)
      }

      console.log('[createApiMethod] 响应结果:', response)
      return response
    } catch (error) {
      console.error('[createApiMethod] 请求失败:', error)
      throw error
    }
  }
}

/**
 * 根据配置生成API对象
 */
const api = {}
Object.keys(apiConfig).forEach(moduleName => {
  api[moduleName] = {}
  const moduleConfig = apiConfig[moduleName]

  Object.keys(moduleConfig.methods).forEach(methodName => {
    const methodConfig = moduleConfig.methods[methodName]
    api[moduleName][methodName] = createApiMethod(methodConfig)
  })
})

// 导出API对象和工具方法
export default api
export { get, post, put, del, upload, httpRequest, setApiBaseUrl }

/**
 * 使用示例：
 *
 * // 基本用法
 * import api from '@/api'
 *
 * // 用户登录
 * const loginResult = await api.user.login({ username: 'admin', password: '123456' })
 *
 * // 获取用户列表
 * const userList = await api.user.list({ page: 1, pageSize: 10 })
 *
 * // 获取用户详情
 * const userDetail = await api.user.getById({ id: 1 })
 *
 * // 更新用户
 * const updateResult = await api.user.update({ id: 1, nickname: '新昵称' })
 *
 * // 删除用户
 * const deleteResult = await api.user.delete({ id: 1 })
 *
 * 添加新接口非常简单，只需在 apiConfig 中添加配置：
 * methods: {
 *   newMethod: { method: 'POST', url: '/api/module/new-endpoint' }
 * }
 */
