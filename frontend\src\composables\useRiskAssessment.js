/**
 * 风险评估组合式函数
 * 提供风险评估相关的状态管理、WebSocket监听、API调用等功能
 */

import { ref, nextTick, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import api from '@/api'
import webSocketService from '@/utils/webSocketService'

/**
 * 风险评估组合式函数
 * @param {Object} options 配置选项
 * @param {string} options.documentType 文档类型 ('bidding_document' | 'tender_scheme')
 * @param {Ref} options.documentId 文档ID的响应式引用
 * @param {boolean} options.isEditMode 是否为编辑模式
 * @param {Ref} options.editorRef 编辑器引用
 * @returns {Object} 风险评估相关的状态和方法
 */
export function useRiskAssessment(options) {
  const { documentType, documentId, isEditMode, editorRef } = options
  
  // 风险评估状态
  const riskAssessmentData = ref(null)
  const riskAssessmentLoading = ref(false)
  const currentRiskAssessmentId = ref(null)
  
  /**
   * 启动风险评估
   */
  const handleStartRiskAssessment = async () => {
    if (!isEditMode || !documentId.value) {
      message.warning('请在编辑现有文档时进行风险评估')
      return
    }
    
    try {
      riskAssessmentLoading.value = true
      
      // 清空当前风险评估数据，因为新的评估正在进行
      riskAssessmentData.value = null
      currentRiskAssessmentId.value = null
      
      const response = await api.riskAssessment.startAssessment({ 
        document_type: documentType, 
        document_id: documentId.value 
      })
      
      if (response?.success) {
        message.success('风险评估已启动，评估完成后将自动显示结果')
        // 注意：这里不需要设置 riskAssessmentLoading.value = false，
        // 保持loading状态直到评估完成或失败
      } else {
        message.error(response?.message || '启动风险评估失败')
        riskAssessmentLoading.value = false
      }
    } catch (error) {
      console.error('启动风险评估失败:', error)
      message.error('启动风险评估时发生错误: ' + (error.message || '未知错误'))
      riskAssessmentLoading.value = false
    }
  }
  
  /**
   * 刷新风险评估数据
   */
  const handleRefreshRiskAssessment = async () => {
    await loadRiskAssessmentData()
  }
  
  /**
   * 加载风险评估数据
   */
  const loadRiskAssessmentData = async () => {
    if (!isEditMode || !currentRiskAssessmentId.value) {
      riskAssessmentData.value = null
      return
    }
    
    try {
      riskAssessmentLoading.value = true
      
      // 直接使用风险评估ID获取评估信息
      const response = await api.riskAssessment.getAssessmentInfo({ 
        risk_assessment_id: currentRiskAssessmentId.value 
      })
      
      if (response?.success && response.data) {
        const assessment = response.data
        
        // 检查评估状态是否为完成
        if (['low', 'medium', 'high'].includes(assessment.status) && assessment.ai_result) {
          // 解析AI结果（如果是字符串格式）
          let aiResult = assessment.ai_result
          if (typeof aiResult === 'string') {
            try {
              aiResult = JSON.parse(aiResult)
            } catch (e) {
              console.error('解析AI评估结果失败:', e)
              riskAssessmentData.value = null
              return
            }
          }
          
          riskAssessmentData.value = aiResult
          
          // 更新编辑器的风险高亮
          if (editorRef.value && aiResult?.field_risks) {
            nextTick(() => {
              editorRef.value.updateRiskHighlights(aiResult.field_risks)
            })
          }
        } else {
          // 评估未完成或无结果
          riskAssessmentData.value = null
        }
      } else {
        riskAssessmentData.value = null
      }
    } catch (error) {
      console.error('加载风险评估数据失败:', error)
      riskAssessmentData.value = null
    } finally {
      riskAssessmentLoading.value = false
    }
  }
  
  /**
   * 滚动到指定字段
   * @param {Object} risk 风险对象
   */
  const handleScrollToField = (risk) => {
    if (editorRef.value) {
      editorRef.value.scrollToField(risk.field_key)
    }
  }
  
  /**
   * 高亮指定字段
   * @param {Object} risk 风险对象
   * @param {boolean} highlight 是否高亮
   */
  const handleHighlightField = (risk, highlight) => {
    if (editorRef.value) {
      editorRef.value.highlightField(risk.field_key, highlight)
    }
  }
  
  /**
   * 设置风险评估ID
   * @param {string|number} assessmentId 风险评估ID
   */
  const setRiskAssessmentId = (assessmentId) => {
    currentRiskAssessmentId.value = assessmentId
  }
  
  /**
   * 设置WebSocket事件监听器
   */
  const setupRiskAssessmentListeners = () => {
    const handleRiskAssessmentCompleted = (data) => {
      const { documentType: msgDocType, documentId: msgDocId, taskId } = data
      if (msgDocType === documentType && msgDocId === Number(documentId.value)) {
        message.success('风险评估已完成！')
        
        // 更新当前文档的风险评估ID
        currentRiskAssessmentId.value = taskId
        
        // 结束loading状态
        riskAssessmentLoading.value = false
        
        // 重新加载风险评估数据
        loadRiskAssessmentData()
      }
    }
    
    const handleRiskAssessmentFailed = (data) => {
      const { documentType: msgDocType, documentId: msgDocId } = data
      if (msgDocType === documentType && msgDocId === Number(documentId.value)) {
        message.error('风险评估失败：' + data.error)
        riskAssessmentLoading.value = false
      }
    }
    
    webSocketService.on('risk_assessment_completed', handleRiskAssessmentCompleted)
    webSocketService.on('risk_assessment_failed', handleRiskAssessmentFailed)
    
    // 返回清理函数
    return () => {
      webSocketService.off('risk_assessment_completed', handleRiskAssessmentCompleted)
      webSocketService.off('risk_assessment_failed', handleRiskAssessmentFailed)
    }
  }
  
  // 组件挂载时设置监听器
  let cleanupListeners = null
  onMounted(() => {
    cleanupListeners = setupRiskAssessmentListeners()
  })
  
  // 组件卸载时清理监听器
  onUnmounted(() => {
    if (cleanupListeners) {
      cleanupListeners()
    }
  })
  
  return {
    // 状态
    riskAssessmentData,
    riskAssessmentLoading,
    currentRiskAssessmentId,
    
    // 方法
    handleStartRiskAssessment,
    handleRefreshRiskAssessment,
    loadRiskAssessmentData,
    handleScrollToField,
    handleHighlightField,
    setRiskAssessmentId,
    setupRiskAssessmentListeners
  }
}
