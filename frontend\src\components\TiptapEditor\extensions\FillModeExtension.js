import { Extension } from '@tiptap/core'
import { Plugin, PluginKey, TextSelection } from '@tiptap/pm/state'

export const fillModePluginKey = new PluginKey('fillModeProtection')

/**
 * 填充模式扩展 FillModeExtension - 简化版本
 * 该扩展用于控制填充模式下的编辑权限：
 * - 填充模式下：只有自定义字段内容可编辑，光标不能移动到字段外
 * - 非填充模式下：正常编辑，所有内容可编辑
 */
export const FillModeExtension = Extension.create({
  name: 'fillModeExtension',

  // 添加选项 该扩展程序的默认设置
  addOptions() {
    return {
      // 获取编辑器实例的函数
      getEditorInstance: () => null,
      // 检查是否为填充模式的函数
      isFillModeActive: () => false
    }
  },

  addProseMirrorPlugins() {
    // 通过this.options访问选项
    const getEditorInstanceFn = this.options.getEditorInstance
    const isFillModeActiveFn = this.options.isFillModeActive

    return [
      new Plugin({
        key: fillModePluginKey,
        props: {
          /**
           * 处理键盘输入事件
           */
          handleKeyDown(view, event) {
            // 如果不在填充模式，允许默认行为
            if (!isFillModeActiveFn()) {
              return false
            }

            console.log('[FillMode-KeyDown]', {
              key: event.key,
              selectionFrom: view.state.selection.from,
              selectionTo: view.state.selection.to
            })

            const { state } = view
            const { selection } = state
            const { $from, $to } = selection

            // 检查当前选择是否在字段内
            const fromInField = isPositionInCustomField($from)
            const toInField = isPositionInCustomField($to)

            // 处理 Backspace 和 Delete 键
            if (event.key === 'Backspace' || event.key === 'Delete') {
              console.log('[FillMode-Delete]', {
                key: event.key,
                fromInField,
                toInField,
                selectionEmpty: selection.empty
              })

              // 如果当前不在字段内，阻止删除，重定向到最近的字段
              if (!fromInField || !toInField) {
                event.preventDefault()
                const closestFieldPos = findClosestCustomField(state, $from.pos)
                if (closestFieldPos !== -1) {
                  const newSelection = TextSelection.create(state.doc, closestFieldPos)
                  view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
                }
                return true
              }

              // 边界检查
              const fieldBounds = getCustomFieldBounds(state, $from)
              if (fieldBounds && selection.empty) {
                if (event.key === 'Backspace' && $from.pos <= fieldBounds.start) {
                  console.log('[FillMode-Delete] 阻止在字段开始位置Backspace')
                  event.preventDefault()
                  return true
                }
                // if (event.key === 'Delete' && $from.pos >= fieldBounds.end) {
                //   console.log('[FillMode-Delete] 阻止在字段结束位置Delete');
                //   event.preventDefault();
                //   return true;
                // }
              }
            }

            // 处理方向键导航
            if (event.key === 'ArrowLeft' || event.key === 'ArrowRight' || event.key === 'ArrowUp' || event.key === 'ArrowDown') {
              // 如果当前光标不在字段内，阻止移动并重定位到最近的字段
              if (!fromInField || !toInField) {
                event.preventDefault()
                const closestFieldPos = findClosestCustomField(state, $from.pos)
                if (closestFieldPos !== -1) {
                  const newSelection = TextSelection.create(state.doc, closestFieldPos)
                  view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
                }
                return true
              }

              // 边界检查
              const fieldBounds = getCustomFieldBounds(state, $from)
              if (fieldBounds) {
                if (event.key === 'ArrowLeft' && $from.pos <= fieldBounds.start) {
                  event.preventDefault()
                  return true
                }
                if (event.key === 'ArrowRight' && $from.pos >= fieldBounds.end) {
                  event.preventDefault()
                  return true
                }
              }
            }

            // 处理 Tab 键 - 在字段间切换
            if (event.key === 'Tab') {
              event.preventDefault()

              // 获取所有字段位置
              const fieldPositions = []
              state.doc.descendants((node, nodePos) => {
                if (node.type.name === 'customField') {
                  const fieldStart = nodePos + 1
                  fieldPositions.push(fieldStart)
                }
              })

              if (fieldPositions.length === 0) {
                return true
              }

              // 按位置排序
              fieldPositions.sort((a, b) => a - b)

              // 找到当前字段的索引
              let currentIndex = -1
              for (let i = 0; i < fieldPositions.length; i++) {
                if ($from.pos >= fieldPositions[i]) {
                  const nextPos = i < fieldPositions.length - 1 ? fieldPositions[i + 1] : Infinity
                  if ($from.pos < nextPos) {
                    currentIndex = i
                    break
                  }
                }
              }

              // 切换到下一个或上一个字段
              let targetIndex
              if (event.shiftKey) {
                // Shift+Tab：上一个字段
                targetIndex = currentIndex > 0 ? currentIndex - 1 : fieldPositions.length - 1
              } else {
                // Tab：下一个字段
                targetIndex = currentIndex < fieldPositions.length - 1 ? currentIndex + 1 : 0
              }

              const targetPos = fieldPositions[targetIndex]
              const newSelection = TextSelection.create(state.doc, targetPos)
              view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
              return true
            }

            // 处理 Home/End 键
            if (event.key === 'Home' || event.key === 'End') {
              // 如果当前不在字段内，重定向到最近的字段
              if (!fromInField || !toInField) {
                event.preventDefault()
                const closestFieldPos = findClosestCustomField(state, $from.pos)
                if (closestFieldPos !== -1) {
                  const newSelection = TextSelection.create(state.doc, closestFieldPos)
                  view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
                }
                return true
              }

              // 限制 Home/End 在当前字段内
              event.preventDefault()
              const fieldBounds = getCustomFieldBounds(state, $from)
              if (fieldBounds) {
                const targetPos = event.key === 'Home' ? fieldBounds.start : fieldBounds.end
                const newSelection = TextSelection.create(state.doc, targetPos)
                view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
              }
              return true
            }

            // 处理 Ctrl+A (全选)
            if (event.key === 'a' && (event.ctrlKey || event.metaKey)) {
              event.preventDefault()
              // 只选择当前字段内的内容
              if (fromInField && toInField && $from.parent === $to.parent) {
                const fieldBounds = getCustomFieldBounds(state, $from)
                if (fieldBounds) {
                  const newSelection = TextSelection.create(state.doc, fieldBounds.start, fieldBounds.end)
                  view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
                }
              }
              return true
            }

            // 对于其他键盘输入，检查当前位置是否在字段内
            if (event.key.length === 1) {
              if (!fromInField || !toInField) {
                event.preventDefault()
                // 重定位到最近的字段
                const closestFieldPos = findClosestCustomField(state, $from.pos)
                if (closestFieldPos !== -1) {
                  const newSelection = TextSelection.create(state.doc, closestFieldPos)
                  view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
                }
                return true
              }
            }

            return false
          },

          /**
           * 处理鼠标点击事件
           */
          handleClick(view, pos, event) {
            // 如果不在填充模式，允许默认行为
            if (!isFillModeActiveFn()) {
              return false
            }

            const { state } = view
            const $resolvedPos = state.doc.resolve(pos)

            // 如果点击直接在customField的内容区域内，允许默认行为
            if ($resolvedPos.parent.type.name === 'customField') {
              return false
            }

            // 点击在字段外，尝试将光标移动到最近的customField
            const closestFieldPos = findClosestCustomField(state, pos)

            if (closestFieldPos !== -1) {
              const newSelection = TextSelection.create(state.doc, closestFieldPos)
              if (!state.selection.eq(newSelection)) {
                view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
              }
              return true // Event handled
            }

            // 如果没有找到自定义字段，阻止点击
            return true
          }
        }
      })
    ]

    /**
     * 检查位置是否在customField内
     */
    function isPositionInCustomField($pos) {
      return $pos && $pos.parent && $pos.parent.type.name === 'customField'
    }

    /**
     * 检查节点是否包含customField
     */
    function containsCustomField(node) {
      if (!node) return false
      if (node.type.name === 'customField') return true
      if (node.childCount > 0) {
        for (let i = 0; i < node.childCount; i++) {
          if (containsCustomField(node.child(i))) return true
        }
      }
      return false
    }

    /**
     * 获取自定义字段的边界位置
     */
    function getCustomFieldBounds(state, $pos) {
      if (!isPositionInCustomField($pos)) {
        return null
      }

      // 向上查找customField节点
      for (let depth = 0; depth <= $pos.depth; depth++) {
        const node = $pos.node(depth)
        if (node.type.name === 'customField') {
          const start = $pos.start(depth)
          const end = $pos.end(depth)
          return { start, end }
        }
      }

      return null
    }

    /**
     * 找到最近的customField位置
     */
    function findClosestCustomField(state, pos) {
      const fieldPositions = []

      // 收集所有字段的位置信息
      state.doc.descendants((node, nodePos) => {
        if (node.type.name === 'customField') {
          const fieldStart = nodePos + 1 // 字段内容开始位置
          const fieldEnd = nodePos + 1 + node.content.size // 字段内容结束位置
          fieldPositions.push({
            start: fieldStart,
            end: fieldEnd,
            nodePos: nodePos
          })
        }
      })

      if (fieldPositions.length === 0) {
        return -1 // 没有字段
      }

      // 找到最近的字段位置
      let closestFieldPos = -1
      let minDistance = Infinity

      for (const field of fieldPositions) {
        let distance
        let targetPos

        if (pos < field.start) {
          // 点击位置在字段前面，选择字段开始位置
          distance = field.start - pos
          targetPos = field.start
        } else if (pos > field.end) {
          // 点击位置在字段后面，选择字段结束位置
          distance = pos - field.end
          targetPos = field.end
        } else {
          // 点击位置在字段内部
          distance = 0
          targetPos = Math.min(pos, field.end)
        }

        if (distance < minDistance) {
          minDistance = distance
          closestFieldPos = targetPos
        }
      }

      return Math.max(0, Math.min(closestFieldPos, state.doc.content.size))
    }
  }
})
