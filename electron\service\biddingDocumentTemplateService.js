'use strict'

// const BaseService = require('./baseService');
const { sqlitedbService } = require('./database/sqlitedb')
const crypto = require('crypto')

/**
 * 招标文件模板服务
 * 实现模板分级管理、内容继承、段落内容独立存储
 * @class
 */
class BiddingDocumentTemplateService {
  // 移除 extends BaseService
  constructor(ctx) {
    // super(); // 移除
    this.ctx = ctx
    this.db = sqlitedbService.db
    this.tableName = sqlitedbService.biddingDocumentTemplatesTableName
    this.contentsTableName = sqlitedbService.templateContentsTableName
  }

  // 添加辅助错误处理方法
  errorResponse(message, statusCode = 400) {
    console.error(`Service Error: ${message}`)
    return { success: false, message, statusCode }
  }

  dbErrorResponse() {
    return {
      success: false,
      message: '数据库操作失败，请检查数据库连接或稍后再试。',
      statusCode: 500
    }
  }

  /**
   * 保存段落内容到内容表
   * @param {Array} contentArray - Tiptap JSON content 数组
   * @param {number} templateId - 所属模板ID
   * @returns {Array} 段落引用数组 [{"paragraphId":1,"templateId":2}]
   */
  saveContentParagraphs(contentArray, templateId) {
    if (!Array.isArray(contentArray)) {
      throw new Error('Content must be an array')
    }
    console.log('[saveContentParagraphs] 保存段落，模板ID:', templateId, '段落数量:', contentArray.length)

    const paragraphRefs = []
    const usedDbParagraphIds = new Set() // 用于跟踪已使用的dbParagraphId

    // 批量操作数据收集
    const updatesData = [] // 需要更新的段落数据
    const insertsData = [] // 需要插入的段落数据
    const preserveRefs = [] // 需要保持原有引用的段落

    // 第一阶段：分析和分类所有段落
    for (let i = 0; i < contentArray.length; i++) {
      const paragraph = contentArray[i]

      // 检查段落的templateId属性
      const paragraphTemplateId = paragraph.attrs?.templateId
      let dbParagraphId = paragraph.attrs?.dbParagraphId

      // 检查是否有重复的dbParagraphId
      if (dbParagraphId && usedDbParagraphIds.has(dbParagraphId)) {
        console.log(`[saveContentParagraphs] 检测到重复的dbParagraphId: ${dbParagraphId}，将作为新段落插入`)
        dbParagraphId = null // 重置为null，作为新段落处理
      }

      // 只对以下情况进行保存：
      // 1. templateId 没有值（新插入的段落）
      // 2. templateId 等于当前templateId（更新当前模板的段落）
      // 注意：确保类型一致的比较
      const shouldSave = !paragraphTemplateId || paragraphTemplateId == templateId // 使用 == 而不是 === 来处理类型转换

      if (shouldSave) {
        const contentData = JSON.stringify(paragraph)

        if (dbParagraphId) {
          // 收集需要更新的段落数据
          updatesData.push({ id: dbParagraphId, contentData: contentData, originalIndex: i })
          usedDbParagraphIds.add(dbParagraphId)
        } else {
          // 收集需要插入的段落数据
          insertsData.push({ contentData: contentData, originalIndex: i })
        }
      } else {
        // 对于其他模板的段落，保持原有引用
        if (dbParagraphId && paragraphTemplateId) {
          usedDbParagraphIds.add(dbParagraphId)
          preserveRefs.push({ paragraphId: dbParagraphId, templateId: paragraphTemplateId, originalIndex: i })
        }
      }
    }

    console.log(`[saveContentParagraphs] 操作统计 - 更新: ${updatesData.length}, 插入: ${insertsData.length}, 保持: ${preserveRefs.length}`)

    // 使用事务确保所有操作的原子性
    const batchTransaction = this.db.transaction(() => {
      // 第二阶段：批量更新操作（使用CASE WHEN语法）
      if (updatesData.length > 0) {
        console.log(`[saveContentParagraphs] 批量更新 ${updatesData.length} 个段落`)

        // 统一使用CASE WHEN批量语法
        const ids = updatesData.map(item => item.id)
        const idsPlaceholder = ids.map(() => '?').join(',')

        // 构建CASE WHEN语句
        const caseWhenContent = updatesData.map(() => 'WHEN id = ? THEN ?').join(' ')

        const batchUpdateSql = `
          UPDATE ${this.contentsTableName} 
          SET content_data = CASE ${caseWhenContent} END,
              updated_at = datetime('now','localtime')
          WHERE id IN (${idsPlaceholder})
        `

        // 构建参数数组：[id1, content1, id2, content2, ...] + [id1, id2, ...]
        const caseParams = []
        updatesData.forEach(item => {
          caseParams.push(item.id, item.contentData)
        })
        const allParams = [...caseParams, ...ids]

        this.db.prepare(batchUpdateSql).run(...allParams)
        console.log(`[saveContentParagraphs] 批量更新完成：1条SQL语句更新${updatesData.length}个段落`)
      }

      // 第三阶段：批量插入操作（使用多VALUES语法）
      if (insertsData.length > 0) {
        console.log(`[saveContentParagraphs] 批量插入 ${insertsData.length} 个段落`)

        // 统一使用批量VALUES语法
        const valuesPlaceholder = insertsData.map(() => '(?)').join(',')
        const batchInsertSql = `INSERT INTO ${this.contentsTableName} (content_data) VALUES ${valuesPlaceholder}`
        const contentParams = insertsData.map(item => item.contentData)

        const result = this.db.prepare(batchInsertSql).run(...contentParams)
        console.log(`[saveContentParagraphs] 批量插入完成：1条SQL语句插入${insertsData.length}个段落`)

        // 计算生成的ID（SQLite自增ID是连续的）
        const firstInsertId = result.lastInsertRowid - insertsData.length + 1
        insertsData.forEach((item, index) => {
          item.generatedId = firstInsertId + index
        })
      }
    })

    // 执行批量事务
    batchTransaction()

    // 第四阶段：按原始顺序构建段落引用数组
    const tempRefs = [] // 临时存储所有引用，包含原始索引信息

    // 添加更新的段落引用
    updatesData.forEach(item => {
      tempRefs.push({ paragraphId: item.id, templateId: templateId, originalIndex: item.originalIndex })
    })

    // 添加插入的段落引用
    insertsData.forEach(item => {
      tempRefs.push({ paragraphId: item.generatedId, templateId: templateId, originalIndex: item.originalIndex })
    })

    // 添加保持的段落引用
    preserveRefs.forEach(item => {
      tempRefs.push({ paragraphId: item.paragraphId, templateId: item.templateId, originalIndex: item.originalIndex })
    })

    // 按原始索引排序，确保引用顺序与输入顺序一致
    tempRefs.sort((a, b) => a.originalIndex - b.originalIndex)

    // 构建最终的段落引用数组（移除originalIndex字段）
    tempRefs.forEach(ref => {
      paragraphRefs.push({ paragraphId: ref.paragraphId, templateId: ref.templateId })
    })

    console.log(`[saveContentParagraphs] 批量保存完成，生成 ${paragraphRefs.length} 个段落引用`)

    return paragraphRefs
  }

  /**
   * 批量删除段落记录
   * 优化版本：使用批量删除替代N次单独删除，提升性能
   * @param {Array} paragraphIds - 要删除的段落ID数组
   * @returns {number} 删除的记录数
   */
  deleteParagraphs(paragraphIds) {
    if (!Array.isArray(paragraphIds) || paragraphIds.length === 0) {
      return 0
    }

    // 过滤掉无效的ID，确保都是有效的数字
    const validIds = paragraphIds.filter(id => id != null && !isNaN(Number(id)))

    // 没有有效的段落ID需要删除
    if (validIds.length === 0) return 0

    console.log(`[deleteParagraphs] 批量删除 ${validIds.length} 个段落`)

    // 构建批量删除的SQL，使用IN语句一次性删除所有段落
    const placeholders = validIds.map(() => '?').join(',')
    const batchDeleteSql = `DELETE FROM ${this.contentsTableName} WHERE id IN (${placeholders})`

    // 执行批量删除
    const deleteStmt = this.db.prepare(batchDeleteSql)
    const result = deleteStmt.run(...validIds)

    const deletedCount = result.changes
    console.log(`[deleteParagraphs] 批量删除成功，共删除 ${deletedCount} 个段落`)

    // 如果删除的记录数少于预期，说明有些ID不存在
    if (deletedCount < validIds.length) console.warn(`[deleteParagraphs] 预期删除 ${validIds.length} 个段落，实际删除 ${deletedCount} 个，部分ID可能不存在`)

    return deletedCount
  }

  /**
   * 根据段落引用数组获取完整内容
   * 优化版本：使用批量查询替代N+1查询问题，提升性能
   * @param {Array} paragraphRefs - 段落引用数组 [{"paragraphId":1,"templateId":2}]
   * @returns {Array} Tiptap JSON content 数组
   */
  getContentByRefs(paragraphRefs) {
    if (!Array.isArray(paragraphRefs) || paragraphRefs.length === 0) {
      return []
    }

    // 提取所有段落ID，去重避免重复查询
    const paragraphIds = [...new Set(paragraphRefs.map(ref => ref.paragraphId))]

    if (paragraphIds.length === 0) return []

    console.log(`[getContentByRefs] 批量查询 ${paragraphIds.length} 个段落内容`)

    // 构建批量查询的SQL，使用IN语句一次性获取所有段落内容
    const placeholders = paragraphIds.map(() => '?').join(',')
    const batchQuerySql = `
      SELECT id, content_data 
      FROM ${this.contentsTableName} 
      WHERE id IN (${placeholders})
    `

    // 执行批量查询
    const contentRows = this.db.prepare(batchQuerySql).all(...paragraphIds)

    // 创建ID到content_data的映射，便于快速查找
    const contentMap = new Map()
    contentRows.forEach(row => contentMap.set(row.id, row.content_data))

    console.log(`[getContentByRefs] 成功获取 ${contentRows.length}/${paragraphIds.length} 个段落内容`)

    // 按照原始的paragraphRefs顺序构建结果数组
    const contents = []
    for (const ref of paragraphRefs) {
      const contentData = contentMap.get(ref.paragraphId)

      if (contentData) {
        const parsedContent = JSON.parse(contentData)

        // 确保段落有attrs对象
        if (!parsedContent.attrs) parsedContent.attrs = {}

        // 设置段落的数据库ID和所属模板ID
        parsedContent.attrs.dbParagraphId = ref.paragraphId
        parsedContent.attrs.templateId = ref.templateId

        contents.push(parsedContent)
      } else {
        console.warn(`[getContentByRefs] 段落内容不存在 (ID: ${ref.paragraphId})`)
      }
    }

    console.log(`[getContentByRefs] 最终返回 ${contents.length} 个有效段落内容`)
    return contents
  }

  /**
   * 获取模板的实际段落引用（经过层级对比后的结果）
   * 以父模板的段落引用为基准，将当前模板新增的段落插入到相邻的父模板段落之间，保持上下文位置
   * @param {number} templateId - 模板ID
   * @returns {Array} 实际的段落引用数组
   */
  getActualParagraphRefs(templateId) {
    const template = this.db.prepare(`SELECT parent_id, paragraph_refs FROM ${this.tableName} WHERE id = ?`).get(templateId)

    if (!template) return []

    let actualRefs = []

    // 如果有父模板，先获取父模板的实际段落引用
    if (template.parent_id) actualRefs = this.getActualParagraphRefs(template.parent_id)

    // 获取当前模板的段落引用
    let currentRefs = []
    if (template.paragraph_refs) currentRefs = JSON.parse(template.paragraph_refs)

    // 如果没有父模板，直接返回当前模板的引用
    if (actualRefs.length === 0) return currentRefs

    // 使用新的算法计算结果
    const result = this.computeResultRefs({ actualRefs, currentRefs, templateId })
    console.log(`[getActualParagraphRefs] 模板 ${templateId} 最终段落数量: ${result.length}`)
    return result
  }

  /**
   * 计算实际的段落引用结果
   * 以 actualRefs 为基准，将 currentRefs 中 templateId === 当前 templateId 的段落
   * 插入其在 currentRefs 中相邻的 actualRefs 段落之间，保持上下文位置
   * @param {Object} params - { actualRefs, currentRefs, templateId }
   * @returns {Array} 计算后的段落引用数组
   */
  computeResultRefs({ actualRefs, currentRefs, templateId }) {
    // 生成段落引用的唯一键
    const makeKey = ref => `${ref.paragraphId}-${ref.templateId}`

    // 创建 actualRefs 的键集合和索引映射
    const actualKeys = new Set(actualRefs.map(makeKey))
    const actualIndexMap = new Map(actualRefs.map((ref, idx) => [makeKey(ref), idx]))

    // 克隆基础数组
    const result = [...actualRefs]
    const inserts = []

    // 筛选出当前模板新增的段落（使用 == 而不是 === 来处理类型转换）
    const currentSelfRefs = currentRefs.filter(r => r.templateId == templateId)
    console.log(`[computeResultRefs] 模板 ${templateId} 新增段落数量: ${currentSelfRefs.length}`)

    // 为每个当前模板的段落找到插入位置
    for (const ref of currentSelfRefs) {
      const idxInCurrent = currentRefs.findIndex(r => makeKey(r) === makeKey(ref))

      // 找它之前的第一个 actualRef
      let beforeKey = null
      for (let i = idxInCurrent - 1; i >= 0; i--) {
        const key = makeKey(currentRefs[i])
        if (actualKeys.has(key)) {
          beforeKey = key
          break
        }
      }

      // 找它之后的第一个 actualRef
      let afterKey = null
      for (let i = idxInCurrent + 1; i < currentRefs.length; i++) {
        const key = makeKey(currentRefs[i])
        if (actualKeys.has(key)) {
          afterKey = key
          break
        }
      }

      // 确定插入位置
      let insertIndex
      if (beforeKey && actualIndexMap.has(beforeKey)) {
        insertIndex = actualIndexMap.get(beforeKey) + 1
      } else if (afterKey && actualIndexMap.has(afterKey)) {
        insertIndex = actualIndexMap.get(afterKey)
      } else {
        insertIndex = result.length
      }

      // 累计插入，避免因索引偏移出错
      inserts.push({ index: insertIndex, ref })
    }

    // 按插入位置排序，然后依次插入段落
    inserts.sort((a, b) => a.index - b.index)
    let offset = 0
    for (const { index, ref } of inserts) {
      result.splice(index + offset, 0, ref)
      // 更新 actualIndexMap 以反映新的索引位置
      for (const [key, oldIndex] of actualIndexMap.entries()) {
        if (oldIndex >= index + offset) {
          actualIndexMap.set(key, oldIndex + 1)
        }
      }
      offset++
    }

    return result
  }

  /**
   * 创建招标文件模板
   * @param {object} data - { name, parent_id?, file_type?, remark?, content? }
   * @returns {Promise<object>} { success, data?, message?, statusCode? }
   */
  async create(data) {
    if (!this.db) {
      return this.dbErrorResponse()
    }

    try {
      const { name, parent_id, file_type, remark, content } = data

      if (!name) {
        return this.errorResponse('模板名称不能为空')
      }

      // 验证父模板存在性
      if (parent_id) {
        const parentTemplate = this.db
          .prepare(
            `SELECT id 
             FROM ${this.tableName} 
             WHERE id = ?`
          )
          .get(parent_id)

        if (!parentTemplate) {
          return this.errorResponse('指定的父模板不存在', 404)
        }
      }

      // 先插入模板记录以获取模板ID
      const insertStmt = this.db.prepare(`INSERT INTO ${this.tableName} 
                                          (name, parent_id, file_type, remark, paragraph_refs) 
                                          VALUES (?, ?, ?, ?, ?)`)

      const result = insertStmt.run(name, parent_id || null, file_type || null, remark || null, '[]')
      const templateId = result.lastInsertRowid

      // 处理内容
      let paragraphRefs = []
      if (content) {
        let contentObj = content

        // 如果content是字符串，先解析为对象
        if (typeof content === 'string') contentObj = JSON.parse(content)

        // 检查解析后的内容格式
        if (contentObj && contentObj.content && Array.isArray(contentObj.content)) {
          paragraphRefs = this.saveContentParagraphs(contentObj.content, templateId)

          // 更新模板的paragraph_refs
          this.db.prepare(`UPDATE ${this.tableName} SET paragraph_refs = ? WHERE id = ?`).run(JSON.stringify(paragraphRefs), templateId)
        }
      }

      const newTemplate = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE id = ?`).get(templateId)

      return { success: true, data: newTemplate, message: '招标文件模板创建成功' }
    } catch (error) {
      console.error('[BiddingDocumentTemplateService.create] Error:', error)
      return this.errorResponse(error.message || '服务内部错误', 500)
    }
  }

  /**
   * 根据ID获取招标文件模板详情（包含完整内容）
   * @param {number} id - 模板ID
   * @returns {Promise<object>} { success, data?, message?, statusCode? }
   */
  async getById(id) {
    if (!this.db) return this.dbErrorResponse()

    try {
      const template = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE id = ?`).get(id)
      if (!template) return this.errorResponse('未找到指定的招标文件模板', 404)

      // 获取完整的继承内容
      const actualParagraphRefs = this.getActualParagraphRefs(id)
      const fullContent = this.getContentByRefs(actualParagraphRefs)

      // 构建完整的 Tiptap JSON 结构
      const templateData = template
      templateData.content = { type: 'doc', content: fullContent }

      // 返回实际的段落引用数组，让前端自己判断只读逻辑
      templateData.actual_paragraph_refs = actualParagraphRefs

      return { success: true, data: templateData }
    } catch (error) {
      console.error('[BiddingDocumentTemplateService.getById] Error:', error)
      return this.errorResponse(error.message || '服务内部错误', 500)
    }
  }

  /**
   * 更新招标文件模板
   * @param {number} id - 模板ID
   * @param {object} data - 更新的数据 { name?, file_type?, remark?, content? }
   * @returns {Promise<object>} { success, data?, message?, statusCode? }
   */
  async update(id, data) {
    if (!this.db) return this.dbErrorResponse()

    try {
      const template = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE id = ?`).get(id)

      if (!template) return this.errorResponse('未找到要更新的招标文件模板', 404)

      const { name, file_type, remark, content } = data
      const updateFields = []
      const updateParams = {}

      if (name !== undefined) {
        updateFields.push('name = @name')
        updateParams.name = name
      }

      if (file_type !== undefined) {
        updateFields.push('file_type = @file_type')
        updateParams.file_type = file_type
      }

      if (remark !== undefined) {
        updateFields.push('remark = @remark')
        updateParams.remark = remark
      }

      // 处理内容更新
      if (content) {
        let contentObj = content

        // 如果content是字符串，先解析为对象
        if (typeof content === 'string') contentObj = JSON.parse(content)

        // 检查解析后的内容格式
        if (contentObj && contentObj.content && Array.isArray(contentObj.content)) {
          // 获取当前模板现有的段落引用（只包含当前模板的段落）
          let oldCurrentTemplateParagraphs = []
          if (template.paragraph_refs) {
            const currentParagraphRefs = JSON.parse(template.paragraph_refs)
            // 筛选出属于当前模板的段落（使用 == 而不是 === 来处理类型转换）
            oldCurrentTemplateParagraphs = currentParagraphRefs.filter(ref => ref.templateId == id)
          }

          // 保存新的段落内容，获取新的段落引用
          const newParagraphRefs = this.saveContentParagraphs(contentObj.content, id)

          // 获取新的当前模板段落引用（只包含当前模板的段落）
          const newCurrentTemplateParagraphs = newParagraphRefs.filter(ref => ref.templateId == id)

          // 找出被删除的段落：在旧引用中存在但在新引用中不存在的段落
          const oldParagraphIds = new Set(oldCurrentTemplateParagraphs.map(ref => ref.paragraphId))
          const newParagraphIds = new Set(newCurrentTemplateParagraphs.map(ref => ref.paragraphId))

          const deletedParagraphIds = [...oldParagraphIds].filter(id => !newParagraphIds.has(id))

          // 删除不再使用的段落记录
          if (deletedParagraphIds.length > 0) {
            console.log(`[BiddingDocumentTemplateService.update] 准备删除段落ID: ${deletedParagraphIds.join(', ')}`)
            const deletedCount = this.deleteParagraphs(deletedParagraphIds)
            console.log(`[BiddingDocumentTemplateService.update] 成功删除 ${deletedCount} 个段落`)
          }

          updateFields.push('paragraph_refs = @paragraph_refs')
          updateParams.paragraph_refs = JSON.stringify(newParagraphRefs)
        }
      }

      if (updateFields.length === 0) return this.errorResponse('没有提供需要更新的数据')

      // 添加更新时间
      updateFields.push("updated_at = datetime('now', 'localtime')")
      updateParams.id = id

      const updateSql = `
        UPDATE ${this.tableName} 
        SET ${updateFields.join(', ')} 
        WHERE id = @id
      `

      this.db.prepare(updateSql).run(updateParams)

      // 获取更新后的数据
      const updatedTemplate = await this.getById(id)
      if (updatedTemplate.success) {
        return { success: true, data: updatedTemplate.data, message: '招标文件模板更新成功' }
      } else {
        return updatedTemplate
      }
    } catch (error) {
      console.error('[BiddingDocumentTemplateService.update] Error:', error)
      return this.errorResponse(error.message || '服务内部错误', 500)
    }
  }

  /**
   * 删除招标文件模板
   * @param {number} id - 模板ID
   * @returns {Promise<object>} { success, message?, statusCode? }
   */
  async delete(id) {
    if (!this.db) return this.dbErrorResponse()

    try {
      // 检查是否有子模板
      const childrenCount = this.db.prepare(`SELECT COUNT(*) as count FROM ${this.tableName} WHERE parent_id = ?`).get(id).count

      if (childrenCount > 0) return this.errorResponse('该模板下存在子模板，请先删除子模板', 400)

      // 检查是否有招标文件在使用此模板
      const documentsCount = this.db.prepare(`SELECT COUNT(*) as count FROM ${sqlitedbService.biddingDocumentsTableName} WHERE template_id = ?`).get(id).count

      if (documentsCount > 0) return this.errorResponse('该模板正在被招标文件使用，无法删除', 400)

      // 获取要删除的模板信息，准备清理相关段落
      const template = this.db.prepare(`SELECT paragraph_refs FROM ${this.tableName} WHERE id = ?`).get(id)

      if (template && template.paragraph_refs) {
        const paragraphRefs = JSON.parse(template.paragraph_refs)
        // 筛选出属于当前模板的段落（使用 == 而不是 === 来处理类型转换）
        const currentTemplateParagraphs = paragraphRefs.filter(ref => ref.templateId == id)

        if (currentTemplateParagraphs.length > 0) {
          const paragraphIds = currentTemplateParagraphs.map(ref => ref.paragraphId)
          console.log(`[BiddingDocumentTemplateService.delete] 模板删除时清理段落ID: ${paragraphIds.join(', ')}`)
          const deletedCount = this.deleteParagraphs(paragraphIds)
          console.log(`[BiddingDocumentTemplateService.delete] 清理了 ${deletedCount} 个段落`)
        }
      }

      // 删除模板记录
      const result = this.db.prepare(`DELETE FROM ${this.tableName} WHERE id = ?`).run(id)

      if (result.changes === 0) return this.errorResponse('未找到要删除的招标文件模板', 404)

      return { success: true, message: '招标文件模板删除成功' }
    } catch (error) {
      console.error('[BiddingDocumentTemplateService.delete] Error:', error)
      return this.errorResponse(error.message || '服务内部错误', 500)
    }
  }

  /**
   * 获取招标文件模板树结构
   * @param {number | null} parentId - 父ID，null表示获取顶级模板
   * @return {object} - { success: boolean, data: Array, message?: string }
   */
  getTree(parentId = null) {
    if (!this.db) return this.errorResponse('数据库未连接')

    try {
      let templates
      if (parentId === null) {
        const sql = `SELECT * FROM ${this.tableName} WHERE parent_id IS NULL ORDER BY name`
        templates = this.db.prepare(sql).all()
      } else {
        const sql = `SELECT * FROM ${this.tableName} WHERE parent_id = ? ORDER BY name`
        templates = this.db.prepare(sql).all(parentId)
      }

      const results = []
      for (const template of templates) {
        const childrenResponse = this.getTree(template.id)
        if (childrenResponse.success) {
          const tempOjb = { ...template, children: childrenResponse.data }
          tempOjb.children.length === 0 && delete tempOjb.children
          results.push(tempOjb)
        } else {
          console.error(`Failed to fetch children for template ID ${template.id}: ${childrenResponse.message}`)
          return this.errorResponse(`获取ID为 ${template.id} 的子模板失败: ${childrenResponse.message}`)
        }
      }

      return { success: true, data: results }
    } catch (error) {
      console.error('Error fetching template tree:', error)
      return this.errorResponse(`获取模板树失败: ${error.message}`)
    }
  }
}

module.exports = BiddingDocumentTemplateService
