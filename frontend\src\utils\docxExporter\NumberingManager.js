import * as docx from 'docx'

/**
 * NumberingManager 类用于管理 DOCX 文档中编号列表的定义。
 * 它可以确保每个有序列表都有一个独立的编号序列。
 *
 * 使用示例：
 * const manager = new NumberingManager()
 * const listRef = manager.createNumberingDefinition()
 * const configs = manager.getConfigurations()
 */
export class NumberingManager {
  constructor() {
    this.nextId = 1
    this.numberingConfigurations = [] // 存储传递给 Document 构造函数的配置
  }

  /**
   * 为新的有序列表创建一个编号定义。
   * @returns {string} 编号配置的引用字符串，用于 Paragraph 的 numbering 属性。
   */
  createNumberingDefinition() {
    const id = this.nextId++
    const reference = `generated-list-style-${id}`

    this.numberingConfigurations.push({
      reference: reference,
      levels: [
        {
          level: 0,
          format: docx.NumberFormat.DECIMAL, // 例如：1, 2, 3
          text: '%1.', // 例如："1."
          alignment: docx.AlignmentType.LEFT,
          style: {
            paragraph: {
              indent: {
                left: docx.convertInchesToTwip(0.5),
                hanging: docx.convertInchesToTwip(0.25)
              }
            }
          }
        }
        // 如果支持嵌套列表且需要不同样式，可以在此定义更多级别
        // 例如 level 1: format: NumberFormat.LOWER_LETTER, text: "%2)" for "a)", "b)"
      ]
    })

    return reference // 此引用字符串在 Paragraph 的 numbering 属性中使用
  }

  /**
   * 获取所有已创建的编号配置。
   * @returns {Array<object>} Document 构造函数中 numberings 属性所需的配置数组。
   */
  getConfigurations() {
    return this.numberingConfigurations
  }
}
