-- ========================================
-- 数据库迁移脚本：从 v1.0 到 v2.0
-- 主要变更：风险评估模块重构，字段结构调整
-- 创建时间：2025-01-07
-- ========================================

PRAGMA foreign_keys = OFF;

-- ========================================
-- 第一步：备份现有数据到临时表
-- ========================================

-- 备份旧的风险评估相关数据
CREATE TABLE temp_risk_assessment_tasks_backup AS SELECT * FROM risk_assessment_tasks;
CREATE TABLE temp_risk_assessment_results_backup AS SELECT * FROM risk_assessment_results;

-- 备份主表数据（包含旧的风险状态字段）
CREATE TABLE temp_tender_schemes_backup AS SELECT * FROM tender_schemes;
CREATE TABLE temp_bidding_documents_backup AS SELECT * FROM bidding_documents;
CREATE TABLE temp_tender_scheme_history_backup AS SELECT * FROM tender_scheme_history;
CREATE TABLE temp_bidding_document_history_backup AS SELECT * FROM bidding_document_history;

-- 备份模板段落内容表
CREATE TABLE temp_bidding_document_template_contents_backup AS SELECT * FROM bidding_document_template_contents;

-- ========================================
-- 第二步：删除旧表结构
-- ========================================

-- 删除旧的风险评估表
DROP TABLE IF EXISTS risk_assessment_tasks;
DROP TABLE IF EXISTS risk_assessment_results;

-- 删除主表（稍后重建）
DROP TABLE IF EXISTS tender_schemes;
DROP TABLE IF EXISTS bidding_documents;
DROP TABLE IF EXISTS tender_scheme_history;
DROP TABLE IF EXISTS bidding_document_history;

-- 删除模板段落内容表（需要重建结构）
DROP TABLE IF EXISTS bidding_document_template_contents;

-- ========================================
-- 第三步：创建新的表结构
-- ========================================

-- 创建新的风险评估表（统一设计）
CREATE TABLE risk_assessment (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  document_type TEXT NOT NULL,
  user_id INTEGER NOT NULL,
  status TEXT DEFAULT 'pending',
  started_at DATETIME,
  completed_at DATETIME,
  content_sent TEXT,
  ai_result TEXT,
  tokens_used INTEGER DEFAULT 0,
  error_message TEXT,
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (user_id) REFERENCES app_users(id) ON DELETE CASCADE
);

-- 创建新的招标方案表
CREATE TABLE tender_schemes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  template_id INTEGER,
  remark TEXT,
  content TEXT,
  version_number INTEGER DEFAULT 1,
  update_remark TEXT DEFAULT '',
  updated_by INTEGER,
  share_status TEXT DEFAULT 'private',
  share_users TEXT,
  created_by INTEGER,
  risk_assessment_id INTEGER,
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (template_id) REFERENCES tender_scheme_templates(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES app_users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES app_users(id) ON DELETE SET NULL,
  FOREIGN KEY (risk_assessment_id) REFERENCES risk_assessment(id) ON DELETE SET NULL
);

-- 创建新的招标文件表
CREATE TABLE bidding_documents (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,
  template_id INTEGER,
  scheme_id INTEGER,
  remark TEXT,
  content TEXT,
  version_number INTEGER DEFAULT 1,
  update_remark TEXT DEFAULT '',
  updated_by INTEGER,
  share_status TEXT DEFAULT 'private',
  share_users TEXT,
  created_by INTEGER,
  risk_assessment_id INTEGER,
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (template_id) REFERENCES bidding_document_templates(id) ON DELETE SET NULL,
  FOREIGN KEY (scheme_id) REFERENCES tender_schemes(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES app_users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES app_users(id) ON DELETE SET NULL,
  FOREIGN KEY (risk_assessment_id) REFERENCES risk_assessment(id) ON DELETE SET NULL
);

-- 创建新的招标方案历史版本表
CREATE TABLE tender_scheme_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  scheme_id INTEGER NOT NULL,
  content TEXT NOT NULL,
  version_number INTEGER NOT NULL,
  update_remark TEXT DEFAULT '',
  updated_by INTEGER,
  risk_assessment_id INTEGER,
  created_at DATETIME,
  FOREIGN KEY (scheme_id) REFERENCES tender_schemes(id) ON DELETE CASCADE,
  FOREIGN KEY (updated_by) REFERENCES app_users(id) ON DELETE SET NULL
);

-- 创建新的招标文件历史版本表
CREATE TABLE bidding_document_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  document_id INTEGER NOT NULL,
  content TEXT NOT NULL,
  version_number INTEGER NOT NULL,
  update_remark TEXT DEFAULT '',
  updated_by INTEGER,
  risk_assessment_id INTEGER,
  created_at DATETIME,
  FOREIGN KEY (document_id) REFERENCES bidding_documents(id) ON DELETE CASCADE,
  FOREIGN KEY (updated_by) REFERENCES app_users(id) ON DELETE SET NULL
);

-- 重建模板段落内容表（新增template_id字段）
CREATE TABLE bidding_document_template_contents (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  template_id INTEGER NOT NULL,
  content_data TEXT NOT NULL,
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (template_id) REFERENCES bidding_document_templates(id) ON DELETE CASCADE
);

-- ========================================
-- 第四步：数据迁移和转换
-- ========================================

-- 1. 迁移风险评估数据：将旧的任务+结果模式合并为新的统一模式
INSERT INTO risk_assessment (
  document_type, user_id, status, started_at, completed_at, 
  content_sent, ai_result, tokens_used, error_message, created_at, updated_at
)
SELECT 
  t.document_type,
  t.user_id,
  CASE 
    WHEN t.status = 'failed' THEN 'failed'
    WHEN t.status = 'cancelled' THEN 'cancelled'
    WHEN t.status = 'pending' THEN 'pending'
    WHEN t.status = 'processing' THEN 'processing'
    WHEN t.status = 'completed' THEN
      CASE 
        WHEN EXISTS(SELECT 1 FROM temp_risk_assessment_results_backup r WHERE r.task_id = t.id AND r.risk_level = 'high') THEN 'high'
        WHEN EXISTS(SELECT 1 FROM temp_risk_assessment_results_backup r WHERE r.task_id = t.id AND r.risk_level = 'medium') THEN 'medium'
        WHEN EXISTS(SELECT 1 FROM temp_risk_assessment_results_backup r WHERE r.task_id = t.id) THEN 'low'
        ELSE 'low'
      END
    ELSE 'pending'
  END as status,
  t.started_at,
  t.completed_at,
  '',
  COALESCE((
    SELECT json_group_array(
      json_object(
        'field_key', r.field_key,
        'field_index', r.field_index,
        'field_content', r.field_content,
        'risk_level', r.risk_level,
        'risk_reason', r.risk_reason,
        'risk_score', r.risk_score
      )
    ) 
    FROM temp_risk_assessment_results_backup r 
    WHERE r.task_id = t.id
  ), '[]') as ai_result,
  0,
  t.error_message,
  t.created_at,
  t.updated_at
FROM temp_risk_assessment_tasks_backup t;

-- 2. 迁移招标方案数据
INSERT INTO tender_schemes (
  id, name, template_id, remark, content, version_number, 
  update_remark, updated_by, share_status, share_users, 
  created_by, risk_assessment_id, created_at, updated_at
)
SELECT 
  t.id, t.name, t.template_id, t.remark, t.content, t.version_number,
  t.update_remark, t.updated_by, t.share_status, t.share_users,
  t.created_by,
  CASE 
    WHEN t.risk_status != 'none' AND t.last_risk_assessment_at IS NOT NULL THEN
      (SELECT ra.id FROM risk_assessment ra 
       WHERE ra.document_type = 'tender_scheme' 
       AND ra.user_id = COALESCE(t.created_by, 1)
       AND datetime(ra.created_at) <= datetime(t.last_risk_assessment_at)
       ORDER BY ra.created_at DESC LIMIT 1)
    ELSE NULL
  END as risk_assessment_id,
  t.created_at, t.updated_at
FROM temp_tender_schemes_backup t;

-- 3. 迁移招标文件数据
INSERT INTO bidding_documents (
  id, name, template_id, scheme_id, remark, content, version_number,
  update_remark, updated_by, share_status, share_users,
  created_by, risk_assessment_id, created_at, updated_at
)
SELECT 
  d.id, d.name, d.template_id, d.scheme_id, d.remark, d.content, d.version_number,
  d.update_remark, d.updated_by, d.share_status, d.share_users,
  d.created_by,
  CASE 
    WHEN d.risk_status != 'none' AND d.last_risk_assessment_at IS NOT NULL THEN
      (SELECT ra.id FROM risk_assessment ra 
       WHERE ra.document_type = 'bidding_document' 
       AND ra.user_id = COALESCE(d.created_by, 1)
       AND datetime(ra.created_at) <= datetime(d.last_risk_assessment_at)
       ORDER BY ra.created_at DESC LIMIT 1)
    ELSE NULL
  END as risk_assessment_id,
  d.created_at, d.updated_at
FROM temp_bidding_documents_backup d;

-- 4. 迁移历史版本数据
INSERT INTO tender_scheme_history (
  id, scheme_id, content, version_number, update_remark, 
  updated_by, risk_assessment_id, created_at
)
SELECT 
  h.id, h.scheme_id, h.content, h.version_number, h.update_remark,
  h.updated_by, NULL, h.created_at
FROM temp_tender_scheme_history_backup h;

INSERT INTO bidding_document_history (
  id, document_id, content, version_number, update_remark,
  updated_by, risk_assessment_id, created_at
)
SELECT 
  h.id, h.document_id, h.content, h.version_number, h.update_remark,
  h.updated_by, NULL, h.created_at
FROM temp_bidding_document_history_backup h;

-- 5. 迁移模板段落内容数据
INSERT INTO bidding_document_template_contents (
  id, template_id, content_data, created_at, updated_at
)
SELECT 
  c.id,
  COALESCE((
    SELECT bt.id FROM bidding_document_templates bt 
    WHERE bt.paragraph_refs LIKE '%"paragraphId":' || c.id || '%'
    LIMIT 1
  ), 1) as template_id,
  c.content_data,
  c.created_at,
  c.updated_at
FROM temp_bidding_document_template_contents_backup c;

-- ========================================
-- 第五步：创建索引
-- ========================================

CREATE INDEX idx_risk_assessment_user_id ON risk_assessment(user_id);
CREATE INDEX idx_risk_assessment_status ON risk_assessment(status);
CREATE INDEX idx_risk_assessment_document_type ON risk_assessment(document_type);
CREATE INDEX idx_risk_assessment_created_at ON risk_assessment(created_at);

CREATE INDEX idx_scheme_history_scheme_id ON tender_scheme_history(scheme_id);
CREATE INDEX idx_scheme_history_version ON tender_scheme_history(scheme_id, version_number);

CREATE INDEX idx_document_history_document_id ON bidding_document_history(document_id);
CREATE INDEX idx_document_history_version ON bidding_document_history(document_id, version_number);

CREATE INDEX IF NOT EXISTS idx_ai_chat_sessions_user_id ON ai_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_chat_sessions_status ON ai_chat_sessions(status);
CREATE INDEX IF NOT EXISTS idx_ai_chat_messages_session_id ON ai_chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_ai_chat_messages_timestamp ON ai_chat_messages(timestamp);
CREATE INDEX IF NOT EXISTS idx_ai_chat_messages_role ON ai_chat_messages(role);

CREATE INDEX idx_template_id ON bidding_document_template_contents(template_id);

-- ========================================
-- 第六步：更新序列值
-- ========================================

UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM risk_assessment) WHERE name = 'risk_assessment';
UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM tender_schemes) WHERE name = 'tender_schemes';
UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM bidding_documents) WHERE name = 'bidding_documents';
UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM tender_scheme_history) WHERE name = 'tender_scheme_history';
UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM bidding_document_history) WHERE name = 'bidding_document_history';
UPDATE sqlite_sequence SET seq = (SELECT MAX(id) FROM bidding_document_template_contents) WHERE name = 'bidding_document_template_contents';

-- ========================================
-- 第七步：创建版本记录表并记录迁移
-- ========================================

CREATE TABLE IF NOT EXISTS db_version (
  version TEXT PRIMARY KEY,
  migration_date DATETIME DEFAULT (datetime('now','localtime')),
  description TEXT
);

INSERT OR REPLACE INTO db_version (version, description) 
VALUES ('v2.0', '数据库结构从v1.0迁移到v2.0，重构风险评估模块，调整字段结构');

-- ========================================
-- 清理临时备份表（可选）
-- ========================================

-- DROP TABLE temp_risk_assessment_tasks_backup;
-- DROP TABLE temp_risk_assessment_results_backup;
-- DROP TABLE temp_tender_schemes_backup;
-- DROP TABLE temp_bidding_documents_backup;
-- DROP TABLE temp_tender_scheme_history_backup;
-- DROP TABLE temp_bidding_document_history_backup;
-- DROP TABLE temp_bidding_document_template_contents_backup;

PRAGMA foreign_keys = ON; 