# 风险评估功能使用指南

## 功能概述

招标文件助手新增了智能风险评估功能，可以自动分析招标方案和招标文件中的潜在风险点，帮助用户更好地识别和规避风险。

## 主要特性

### 🚀 核心功能

- **智能分析**：基于 DeepSeek 大模型的深度风险分析
- **异步处理**：长时间评估不阻塞用户操作
- **实时通知**：通过 WebSocket 推送评估完成通知
- **结果持久化**：评估结果自动保存到数据库
- **支持两种文档类型**：招标方案和招标文件

### 📋 风险评估内容

- 提取文档中所有自定义字段内容
- 过滤无效格式，提取纯文本进行分析
- 识别潜在的法律风险、商业风险、技术风险等
- 提供风险等级评估和建议措施

## 使用方法

### 1. 在招标方案列表页使用

1. 登录系统后，进入"招标方案"列表页面
2. 找到要评估的方案，点击操作栏中的"风险评估"按钮
3. 在弹出的确认对话框中，点击"确定评估"
4. 系统将显示"风险评估已启动"的成功提示
5. 评估过程在后台异步进行，您可以继续其他操作
6. 评估完成后，系统会自动推送通知，列表会刷新显示最新状态

### 2. 在招标文件列表页使用

1. 进入"招标文件"列表页面
2. 找到要评估的文件，点击操作栏中的"风险评估"按钮
3. 确认评估后，系统开始后台处理
4. 完成后自动接收通知并更新列表

### 3. 实时通知功能

系统会通过 WebSocket 长连接推送以下通知：

- ✅ **评估完成**：显示成功消息，自动刷新列表
- ❌ **评估失败**：显示错误信息和失败原因
- ℹ️ **评估取消**：显示取消信息

## 注意事项

### ⚠️ 使用限制

- 只有文档所有者才能进行风险评估
- 同一文档同时只能进行一次评估
- 评估过程中请保持网络连接稳定

### 🔧 技术要求

- 需要稳定的网络连接（连接 DeepSeek API）
- WebSocket 连接正常（用于接收实时通知）
- 确保文档中包含有效的自定义字段内容

### 📊 评估结果

- 评估结果会自动保存到数据库
- 包含风险分析报告和建议措施
- 记录 token 消耗和评估时间
- 风险等级直接存储在评估状态中：low(低)、medium(中)、high(高)

### 🔍 风险状态查询机制

新的风险状态查询机制更加简洁高效：

1. **状态判定逻辑**：

   - 如果 `risk_assessment_id` 为空 → 显示"未评估"
   - 如果有 `risk_assessment_id`，查询关联的评估记录：
     - `status = 'pending' 或 'processing'` → 显示"评估中"
     - `status = 'low'` → 显示"低"风险
     - `status = 'medium'` → 显示"中"风险
     - `status = 'high'` → 显示"高"风险

2. **版本更新机制**：

   - 文档内容更新时自动清空 `risk_assessment_id`
   - 需要重新进行风险评估以获取新的评估结果

3. **数据结构优化**：

   - 去除了文档表中冗余的 `risk_status` 字段
   - 风险状态直接通过关联查询获取，保证数据一致性
   - **单向关联设计**：只有文档表通过 `risk_assessment_id` 引用评估结果
   - **避免循环引用**：`risk_assessment` 表不包含 `document_id` 字段，防止文档更新时的数据不一致

4. **设计理念**：
   - 评估记录是历史性的，一旦创建就不应该被修改
   - 文档可以选择关联哪个评估记录，或者不关联任何评估（清空 `risk_assessment_id`）
   - 这种设计使得文档版本更新时的处理更加简单和一致

## 故障排除

### 常见问题

1. **WebSocket 连接失败**

   - 检查服务器是否正常运行
   - 确认端口 8080 是否被占用
   - 检查防火墙设置

2. **评估启动失败**

   - 确认用户权限（是否为文档所有者）
   - 检查文档是否包含有效内容
   - 查看控制台错误日志

3. **评估时间过长**
   - 大文档评估可能需要较长时间
   - DeepSeek API 响应速度影响
   - 网络连接质量影响

### 调试信息

开发者可以通过以下方式查看详细日志：

- 浏览器控制台：WebSocket 连接和前端错误
- 后端日志：评估任务状态和 API 调用信息
- 数据库：评估记录和结果存储

## API 接口说明

### 前端 API 调用

```javascript
// 启动风险评估
const response = await api.riskAssessment.startAssessment({
  document_type: 'tender_scheme', // 或 'bidding_document'
  document_id: documentId
})

// 获取评估结果
const result = await api.riskAssessment.getAssessmentInfo({
  risk_assessment_id: taskId
})
```

### WebSocket 事件

```javascript
// 监听评估完成
webSocketService.on('risk_assessment_completed', data => {
  console.log('评估完成:', data)
})

// 监听评估失败
webSocketService.on('risk_assessment_failed', data => {
  console.log('评估失败:', data)
})
```

## 更新日志

### 版本 1.0.0

- ✨ 新增智能风险评估功能
- 🔄 WebSocket 实时通知系统
- 💾 评估结果持久化存储
- 🎯 支持招标方案和招标文件两种类型
- 🛡️ 完善的权限控制和错误处理

---

如有问题或建议，请联系开发团队。

---

// 风险评估服务会：
// 1. 自动获取招标文件的字段值
// 2. 通过模板服务获取完整的模板内容（包含继承和段落引用处理）
// 3. 提取模板中的 customField 节点并匹配字段值
// 4. 发送给 AI 进行风险评估
// 5. 保存结果并直接发送 WebSocket 通知

---

✅ 风险评估功能集成完成！
我已经完成了招标文件助手项目的完整风险评估功能，包括后端和前端的全面升级。
🔥 功能亮点

1. 智能数据提取
   正确解析招标方案的模板和字段分离存储结构
   特殊标识 customField 节点，发送给 AI 进行重点分析
   提取完整的上下文信息，确保 AI 评估的准确性
2. 强大的可视化体验
   背景高亮：根据风险等级（高/中/低）用不同颜色高亮显示风险字段
   气泡提示：悬停或点击显示详细的风险原因和改进建议
   右侧面板：聚合显示所有风险信息，包括整体评估、字段风险和分类风险
3. 丰富的交互功能
   点击风险面板中的字段可自动滚动到编辑器对应位置
   鼠标悬停可临时高亮字段
   一键刷新评估结果
   WebSocket 实时通知评估完成状态
   📋 新增组件
   RiskHighlightExtension.js - Tiptap 风险高亮扩展
   RiskTooltip.vue - 风险提示气泡组件
   RiskPanel.vue - 右侧风险评估面板
   优化的 riskAssessmentService.js - 智能内容提取和 AI 分析
   🎯 使用流程
   编辑招标方案 → 右侧自动显示风险面板
   点击"开始风险评估" → AI 智能分析文档内容
   实时接收通知 → 评估完成后自动更新显示
   交互式查看 → 高亮显示、气泡提示、详细面板
   🎨 视觉效果
   高风险字段：红色高亮 rgba(255, 77, 79, 0.2)
   中风险字段：橙色高亮 rgba(255, 165, 0, 0.2)
   低风险字段：黄色高亮 rgba(255, 255, 0, 0.2)
   临时高亮：蓝色闪烁效果用于字段定位
   现在用户可以在编辑招标方案时获得智能的风险评估支持，提前发现和解决潜在问题，大大提升了工作效率和文档质量！🚀
