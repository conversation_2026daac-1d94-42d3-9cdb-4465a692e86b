'use strict'

const OpenAI = require('openai')
const { sqlitedbService } = require('./database/sqlitedb')
const aiConfig = require('../config/aiConfig')

/**
 * AI聊天服务
 * @class
 */
class AiChatService {
  constructor(ctx) {
    this.ctx = ctx
    this.db = sqlitedbService.db
    this.sessionsTable = sqlitedbService.aiChatSessionsTableName
    this.messagesTable = sqlitedbService.aiChatMessagesTableName

    // 初始化OpenAI客户端连接DeepSeek API
    this.openai = new OpenAI({
      baseURL: aiConfig.deepseek.baseURL,
      apiKey: aiConfig.deepseek.apiKey,
      timeout: aiConfig.deepseek.timeout
    })
  }

  /**
   * 创建新的聊天会话
   * @param {number} userId - 用户ID
   * @param {string} [title] - 会话标题，可选
   * @returns {Promise<Object>} 新创建的会话信息
   */
  async createSession(userId, title = null) {
    if (!userId) {
      throw new Error('用户ID不能为空')
    }

    const sessionTitle = title || aiConfig.chat.defaultTitle

    const stmt = this.db.prepare(
      `INSERT INTO ${this.sessionsTable} (user_id, title, status, created_at, updated_at)
       VALUES (@user_id, @title, @status, datetime('now','localtime'), datetime('now','localtime'))`
    )

    try {
      const result = stmt.run({
        user_id: userId,
        title: sessionTitle,
        status: 'active'
      })

      return await this.getSessionById(result.lastInsertRowid)
    } catch (error) {
      throw new Error(`创建会话失败: ${error.message}`)
    }
  }

  /**
   * 获取用户的会话列表
   * @param {number} userId - 用户ID
   * @param {Object} params - 查询参数
   * @param {number} [params.page=1] - 页码
   * @param {number} [params.pageSize=10] - 每页大小
   * @param {string} [params.status='active'] - 会话状态
   * @returns {Promise<Object>} { list, total }
   */
  async getSessionsByUserId(userId, params = {}) {
    if (!userId) {
      throw new Error('用户ID不能为空')
    }

    const { page = 1, pageSize = 10, status = 'active' } = params
    const offset = (page - 1) * pageSize

    // 获取总数
    const countStmt = this.db.prepare(
      `SELECT COUNT(id) as total FROM ${this.sessionsTable} 
       WHERE user_id = ? AND status = ?`
    )
    const { total } = countStmt.get(userId, status)

    // 获取列表
    const listStmt = this.db.prepare(
      `SELECT * FROM ${this.sessionsTable}
       WHERE user_id = ? AND status = ?
       ORDER BY updated_at DESC
       LIMIT ? OFFSET ?`
    )
    const list = listStmt.all(userId, status, pageSize, offset)

    return { list, total, page, pageSize }
  }

  /**
   * 根据会话ID获取会话信息
   * @param {number} sessionId - 会话ID
   * @returns {Promise<Object|null>} 会话信息
   */
  async getSessionById(sessionId) {
    if (!sessionId) {
      throw new Error('会话ID不能为空')
    }

    const stmt = this.db.prepare(`SELECT * FROM ${this.sessionsTable} WHERE id = ? AND status != 'deleted'`)

    return stmt.get(sessionId) || null
  }

  /**
   * 更新会话信息
   * @param {number} sessionId - 会话ID
   * @param {Object} updateData - 更新数据
   * @returns {Promise<Object>} 更新后的会话信息
   */
  async updateSession(sessionId, updateData) {
    if (!sessionId) {
      throw new Error('会话ID不能为空')
    }

    // 验证会话是否存在
    const existingSession = await this.getSessionById(sessionId)
    if (!existingSession) {
      throw new Error('会话不存在')
    }

    const allowedFields = ['title', 'status']
    const updateFields = []
    const updateValues = []

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        updateFields.push(`${key} = ?`)
        updateValues.push(value)
      }
    }

    if (updateFields.length === 0) {
      updateFields.push("updated_at = datetime('now','localtime')")
    } else {
      updateFields.push("updated_at = datetime('now','localtime')")
    }
    updateValues.push(sessionId)

    const stmt = this.db.prepare(`UPDATE ${this.sessionsTable} SET ${updateFields.join(', ')} WHERE id = ?`)

    stmt.run(...updateValues)
    return await this.getSessionById(sessionId)
  }

  /**
   * 删除会话（软删除）
   * @param {number} sessionId - 会话ID
   * @param {number} userId - 用户ID（权限验证）
   * @returns {Promise<boolean>} 删除是否成功
   */
  async deleteSession(sessionId, userId) {
    if (!sessionId) {
      throw new Error('会话ID不能为空')
    }

    // 验证会话是否存在且属于该用户
    const existingSession = await this.getSessionById(sessionId)
    if (!existingSession) {
      throw new Error('会话不存在')
    }

    if (existingSession.user_id !== userId) {
      throw new Error('无权限删除此会话')
    }

    const stmt = this.db.prepare(
      `UPDATE ${this.sessionsTable} SET status = 'deleted', updated_at = datetime('now','localtime') 
       WHERE id = ?`
    )

    const result = stmt.run(sessionId)
    return result.changes > 0
  }

  /**
   * 保存消息到数据库
   * @param {number} sessionId - 会话ID
   * @param {string} role - 角色 (user/assistant/system)
   * @param {string} content - 消息内容
   * @param {Object} metadata - 元数据
   * @returns {Promise<Object>} 保存的消息信息
   */
  async saveMessage(sessionId, role, content, metadata = {}) {
    if (!sessionId || !role || !content) {
      throw new Error('会话ID、角色和内容不能为空')
    }

    const stmt = this.db.prepare(
      `INSERT INTO ${this.messagesTable} (session_id, role, content, timestamp, tokens_used, model_name, created_at)
       VALUES (@session_id, @role, @content, datetime('now','localtime'), @tokens_used, @model_name, datetime('now','localtime'))`
    )

    try {
      const result = stmt.run({
        session_id: sessionId,
        role: role,
        content: content,
        tokens_used: metadata.tokensUsed || 0,
        model_name: metadata.modelName || aiConfig.deepseek.model
      })

      // 更新会话的最后更新时间
      await this.updateSession(sessionId, {})

      return {
        id: result.lastInsertRowid,
        session_id: sessionId,
        role: role,
        content: content,
        timestamp: new Date().toISOString(),
        tokens_used: metadata.tokensUsed || 0,
        model_name: metadata.modelName || aiConfig.deepseek.model
      }
    } catch (error) {
      throw new Error(`保存消息失败: ${error.message}`)
    }
  }

  /**
   * 获取会话的消息历史
   * @param {number} sessionId - 会话ID
   * @param {Object} params - 查询参数
   * @param {number} [params.limit] - 限制数量
   * @param {number} [params.offset] - 偏移量
   * @returns {Promise<Array>} 消息列表
   */
  async getMessagesBySessionId(sessionId, params = {}) {
    if (!sessionId) {
      throw new Error('会话ID不能为空')
    }

    const { limit, offset = 0 } = params

    let sql = `SELECT * FROM ${this.messagesTable} WHERE session_id = ? ORDER BY timestamp ASC`
    const sqlParams = [sessionId]

    if (limit) {
      sql += ' LIMIT ? OFFSET ?'
      sqlParams.push(limit, offset)
    }

    const stmt = this.db.prepare(sql)
    return stmt.all(...sqlParams)
  }

  /**
   * 获取用于AI的上下文消息
   * @param {number} sessionId - 会话ID
   * @param {number} maxMessages - 最大消息数量
   * @returns {Promise<Array>} 格式化的消息列表
   */
  async getContextMessages(sessionId, maxMessages = aiConfig.chat.maxHistoryMessages) {
    const messages = await this.getMessagesBySessionId(sessionId)

    // 只取最近的消息，并转换为OpenAI API格式
    const recentMessages = messages.slice(-maxMessages)

    return recentMessages.map(msg => ({
      role: msg.role,
      content: msg.content
    }))
  }

  /**
   * 发送消息给AI并获取流式回复
   * @param {number} sessionId - 会话ID
   * @param {string} userMessage - 用户消息
   * @param {Object} options - 选项
   * @param {Function} onChunk - 流式数据处理回调
   * @returns {Promise<Object>} 完整回复结果
   */
  async sendMessageToAIStream(sessionId, userMessage, options = {}, onChunk = null) {
    if (!sessionId || !userMessage.trim()) {
      throw new Error('会话ID和用户消息不能为空')
    }

    try {
      // 验证会话是否存在
      const session = await this.getSessionById(sessionId)
      if (!session) {
        throw new Error('会话不存在')
      }

      // 保存用户消息
      const userMsg = await this.saveMessage(sessionId, 'user', userMessage.trim())

      // 获取上下文消息
      const contextMessages = await this.getContextMessages(sessionId)

      // 添加系统提示
      // #定义上下文对话
      // #role（可选） 角色类型
      // # - "system"：设定助手行为（通常在对话开头）
      // # - "user"：用户的输入内容（更推荐这里使用）
      // # - "assistant"：助手的先前回复
      const messages = [
        {
          role: 'system',
          content: options.systemPrompt || aiConfig.systemPrompts.default
        },
        ...contextMessages
      ]

      // 调用DeepSeek API流式响应
      const stream = await this.openai.chat.completions.create({
        model: options.model || aiConfig.deepseek.model,
        messages: messages,
        max_tokens: options.maxTokens || aiConfig.deepseek.maxTokens,
        temperature: options.temperature || aiConfig.deepseek.temperature, //  # 控制随机性 (0.0-2.0)
        frequency_penalty: options.frequencyPenalty || aiConfig.deepseek.frequencyPenalty,
        presence_penalty: options.presencePenalty || aiConfig.deepseek.presencePenalty,
        top_p: options.topP || aiConfig.deepseek.topP,
        stream: true, // # 是否启用流式传输
        stream_options: { include_usage: true }
      })

      let fullResponse = ''
      let totalTokensUsed = 0

      // 处理流式响应
      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta

        if (delta?.content) {
          fullResponse += delta.content

          // 调用回调函数，实时传输数据
          if (onChunk && typeof onChunk === 'function') {
            onChunk({
              content: delta.content,
              fullContent: fullResponse,
              isComplete: false
            })
          }
        }

        // 获取token使用量信息
        if (chunk.usage) {
          totalTokensUsed = chunk.usage.total_tokens
        }
      }

      if (!fullResponse.trim()) {
        throw new Error('AI回复为空，请重试')
      }

      // 保存完整的AI回复
      const savedMessage = await this.saveMessage(sessionId, 'assistant', fullResponse, {
        tokensUsed: totalTokensUsed,
        modelName: options.model || aiConfig.deepseek.model
      })

      // 发送完成信号
      if (onChunk && typeof onChunk === 'function') {
        onChunk({
          content: '',
          fullContent: fullResponse,
          isComplete: true,
          message: savedMessage
        })
      }

      // 自动生成会话标题（仅对第一条用户消息）
      if (aiConfig.chat.autoGenerateTitle && contextMessages.length <= 2) {
        await this.generateSessionTitle(sessionId, userMessage)
      }

      return {
        success: true,
        data: {
          message: savedMessage,
          tokensUsed: totalTokensUsed,
          model: options.model || aiConfig.deepseek.model
        }
      }
    } catch (error) {
      console.error('[sendMessageToAIStream] AI流式对话错误:', error)

      // 根据错误类型返回友好的错误信息
      let errorMessage = aiConfig.errorMessages.defaultError

      if (error.message.includes('timeout')) {
        errorMessage = aiConfig.errorMessages.timeoutError
      } else if (error.message.includes('API key')) {
        errorMessage = aiConfig.errorMessages.apiKeyError
      } else if (error.message.includes('rate limit')) {
        errorMessage = aiConfig.errorMessages.rateLimitError
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        errorMessage = aiConfig.errorMessages.networkError
      }

      throw new Error(errorMessage)
    }
  }

  /**
   * 根据首条消息自动生成会话标题
   * @param {number} sessionId - 会话ID
   * @param {string} firstMessage - 第一条用户消息
   * @returns {Promise<string>} 生成的标题
   */
  async generateSessionTitle(sessionId, firstMessage) {
    try {
      // 截取前30个字符作为标题
      let title = firstMessage.length > 30 ? firstMessage.substring(0, 30) + '...' : firstMessage

      // 移除换行符和多余空格
      title = title.replace(/\s+/g, ' ').trim()

      // 更新会话标题
      await this.updateSession(sessionId, { title })

      return title
    } catch (error) {
      console.error('[generateSessionTitle] 生成会话标题失败:', error)
      return '新对话'
    }
  }
}

AiChatService.toString = () => '[class AiChatService]'

module.exports = AiChatService
