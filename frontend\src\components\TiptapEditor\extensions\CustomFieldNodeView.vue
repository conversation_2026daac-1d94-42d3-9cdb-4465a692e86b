<template>
  <node-view-wrapper
    as="span"
    class="custom-field-node-view"
    :class="{
      'is-selected': selected,
      'is-fill-mode': isFillMode,
      'is-template-mode': !isFillMode
    }"
  >
    <node-view-content as="div" class="field-value" :contenteditable="fieldValueEditable" :data-placeholder="placeholderText" />
  </node-view-wrapper>
</template>

<script setup>
import { NodeViewWrapper, NodeViewContent, nodeViewProps } from '@tiptap/vue-3'
import { computed, inject } from 'vue'

const props = defineProps(nodeViewProps)

// 注入填充模式状态
const fillMode = inject('fillMode', false)

// 计算是否为填充模式
const isFillMode = computed(() => fillMode)

// 字段值是否可编辑的逻辑
// 核心逻辑：只有在填充模式下字段才可编辑
const fieldValueEditable = computed(() => {
  // 只有在填充模式下才可编辑
  return isFillMode.value
})

// NodeView 被选中时的样式辅助
const selected = computed(() => props.selected)

// 占位符文本 - 根据模式显示不同的提示
const placeholderText = computed(() => {
  const fieldName = props.node.attrs.field_name || '字段'

  if (isFillMode.value) {
    // 填充模式：提示用户输入值
    return `${fieldName}`
  } else {
    // 模板模式：显示字段标识
    return `【${fieldName}】`
  }
})
</script>

<style lang="less" scoped>
.custom-field-node-view {
  text-indent: 0;
  display: inline-block;
  vertical-align: baseline;
  padding: 2px 6px;
  margin: 0 2px;
  min-width: 80px;
  line-height: 1.5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.3s;

  // 模板模式状态（默认状态）
  &.is-template-mode {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    cursor: default;

    .field-value {
      color: #1890ff;
      font-weight: 500;
      text-align: center;
      user-select: none; // 模板模式下不可选择文本
    }

    &:hover {
      background-color: #bae7ff;
      border-color: #69c0ff;
    }
  }

  // 填充模式状态
  &.is-fill-mode {
    background-color: #fff;
    border-color: #40a9ff;
    cursor: text;

    .field-value {
      color: #333;
      text-align: left;
      user-select: text; // 填充模式下可选择文本
    }

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &:focus-within {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
    }
  }

  // 选中状态
  &.is-selected {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
  }
}

.field-value {
  outline: none;
  display: block;
  width: 100%;
  min-height: 1.2em;
  white-space: pre-wrap;
  word-break: break-word;
  padding: 2px 4px;
  line-height: 1.4;

  // 占位符样式
  &:empty::before {
    content: attr(data-placeholder);
    color: #999;
    pointer-events: none;
    display: block;
    font-style: italic;
    opacity: 0.8;
  }

  // 非可编辑状态的样式
  &[contenteditable='false'] {
    cursor: default;
    user-select: none;

    &:empty::before {
      font-style: normal;
      opacity: 1;
      color: #1890ff; // 模板模式下占位符使用主题色
      font-weight: 500;
    }
  }

  // 可编辑状态的样式
  &[contenteditable='true'] {
    cursor: text;
    user-select: text;

    &:empty::before {
      color: #999;
      font-style: italic;
      font-weight: normal;
    }

    &:focus {
      background-color: rgba(24, 144, 255, 0.05);
    }
  }
}
</style>
