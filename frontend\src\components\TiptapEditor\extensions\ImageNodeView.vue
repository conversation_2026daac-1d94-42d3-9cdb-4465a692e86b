<template>
  <node-view-wrapper class="image-node-view" :class="{ 'is-selected': selected }">
    <span class="image-container" @click="handleImageClick">
      <img :src="node.attrs.src" :style="imageStyle" @load="onImageLoad" @error="onImageError" />

      <!-- 选中时显示的控制面板 (填充模式下不显示) -->
      <div v-if="showControls && !fillMode.value" class="image-controls" @click="handleControlsClick">
        <a-space direction="vertical" size="small">
          <!-- 尺寸控制 -->
          <div class="control-group">
            <span class="control-label">尺寸:</span>
            <a-space size="small">
              <a-input-number v-model:value="editableWidth" :min="10" :max="800" :step="1" size="small" placeholder="宽度" @change="updateWidth" style="width: 80px" />
              px
              <span>×</span>
              <a-input-number v-model:value="editableHeight" :min="10" :max="600" :step="1" size="small" placeholder="高度" @change="updateHeight" style="width: 80px" />
              px
              <a-button size="small" @click="resetSize">重置</a-button>
            </a-space>
          </div>
        </a-space>
      </div>
    </span>
  </node-view-wrapper>
</template>

<script setup>
import { NodeViewWrapper, nodeViewProps } from '@tiptap/vue-3'
import { ref, computed, watch, onMounted, onBeforeUnmount, inject } from 'vue'

const props = defineProps(nodeViewProps)

// 注入填充模式状态
const fillMode = inject('fillMode', false)

// 可编辑的属性
const editableWidth = ref(null)
const editableHeight = ref(null)

// 原始图片尺寸
const originalWidth = ref(null)
const originalHeight = ref(null)

// 控制面板显示状态
const showControls = ref(false)
const controlsTimer = ref(null)

// 处理图片点击事件
const handleImageClick = event => {
  console.log('[ImageNodeView] Image clicked')

  // 检查编辑器是否可编辑（只读模式下不做任何操作）
  if (!props.editor.isEditable) {
    console.log('[ImageNodeView] 只读模式下点击图片，不做任何操作')
    event.stopPropagation() // 阻止事件冒泡
    return
  }

  // 如果在填充模式下，不显示控制面板，而是重定向到最近的字段
  if (fillMode.value) {
    console.log('[ImageNodeView] 填充模式下点击图片，重定向到最近字段')
    // 不阻止事件冒泡，让FillModeExtension处理
    return
  }

  event.stopPropagation() // 阻止事件冒泡

  // 切换控制面板显示状态
  showControls.value = !showControls.value

  // 清除之前的定时器
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
    controlsTimer.value = null
  }

  // 如果显示控制面板，添加全局点击监听器
  if (showControls.value) {
    // 延迟添加监听器，避免立即触发
    setTimeout(() => {
      document.addEventListener('click', handleDocumentClick)
    }, 100)
  } else {
    // 如果隐藏控制面板，移除全局监听器
    document.removeEventListener('click', handleDocumentClick)
  }
}

// 处理文档点击事件（用于隐藏控制面板）
const handleDocumentClick = event => {
  console.log('[ImageNodeView] Document clicked')

  // 检查点击是否在图片容器或控制面板内
  const imageContainer = event.target.closest('.image-container')
  const controlsPanel = event.target.closest('.image-controls')

  if (!imageContainer && !controlsPanel) {
    // 点击在图片容器和控制面板外，隐藏控制面板
    showControls.value = false
    document.removeEventListener('click', handleDocumentClick)
  }
}

// 保持控制面板显示的方法
const keepControlsVisible = () => {
  console.log('[ImageNodeView] keepControlsVisible called')
  // 简单地确保控制面板保持显示
  showControls.value = true
}

// 处理控制面板点击
const handleControlsClick = event => {
  console.log('[ImageNodeView] Click on controls')
  // 阻止事件冒泡，防止隐藏控制面板
  event.stopPropagation()
}

// 初始化可编辑属性
onMounted(() => {
  // 直接使用像素值
  if (props.node.attrs.width) {
    editableWidth.value = parseInt(props.node.attrs.width)
  } else {
    editableWidth.value = null
  }

  if (props.node.attrs.height) {
    editableHeight.value = parseInt(props.node.attrs.height)
  } else {
    editableHeight.value = null
  }

  // 初始化控制面板状态为隐藏
  showControls.value = false
})

// 图片样式
const imageStyle = computed(() => {
  const style = {
    maxWidth: '100%',
    height: 'auto',
    cursor: 'pointer',
    verticalAlign: 'middle' // 内联图片垂直对齐
  }

  // 直接使用像素值
  if (editableWidth.value) {
    style.width = `${editableWidth.value}px`
  }

  if (editableHeight.value) {
    style.height = `${editableHeight.value}px`
  }

  return style
})

// 图片加载完成
const onImageLoad = event => {
  if (!originalWidth.value && !originalHeight.value) {
    originalWidth.value = event.target.naturalWidth
    originalHeight.value = event.target.naturalHeight

    // 如果没有设置尺寸，使用合理的默认尺寸（像素单位）
    if (!editableWidth.value && !editableHeight.value) {
      // 设置默认最大宽度为400px
      const maxWidthPx = 400

      if (originalWidth.value > maxWidthPx) {
        // 如果原图太大，按比例缩放
        const ratio = maxWidthPx / originalWidth.value
        editableWidth.value = maxWidthPx
        editableHeight.value = Math.round(originalHeight.value * ratio)
      } else {
        // 如果原图不大，使用原始尺寸
        editableWidth.value = originalWidth.value
        editableHeight.value = originalHeight.value
      }
      updateSize()
    }
  }
}

// 图片加载错误
const onImageError = () => {
  console.error('图片加载失败:', props.node.attrs.src)
}

// 更新属性的通用方法
const updateAttributes = attrs => {
  props.updateAttributes(attrs)
}

// 更新宽度
const updateWidth = () => {
  console.log('[ImageNodeView] updateWidth called')
  updateSize()
}

// 更新高度
const updateHeight = () => {
  console.log('[ImageNodeView] updateHeight called')
  updateSize()
}

// 更新尺寸
const updateSize = () => {
  updateAttributes({
    width: editableWidth.value,
    height: editableHeight.value
  })
}

// 重置尺寸
const resetSize = () => {
  if (originalWidth.value && originalHeight.value) {
    // 重置为默认最大宽度400px
    const maxWidthPx = 400

    if (originalWidth.value > maxWidthPx) {
      const ratio = maxWidthPx / originalWidth.value
      editableWidth.value = maxWidthPx
      editableHeight.value = Math.round(originalHeight.value * ratio)
    } else {
      editableWidth.value = originalWidth.value
      editableHeight.value = originalHeight.value
    }
    updateSize()
  }
}

// 组件卸载时清理事件监听器
onBeforeUnmount(() => {
  // 移除全局点击监听器
  document.removeEventListener('click', handleDocumentClick)

  // 清理定时器（如果有的话）
  if (controlsTimer.value) {
    clearTimeout(controlsTimer.value)
    controlsTimer.value = null
  }
})
</script>

<style lang="less" scoped>
.image-node-view {
  display: inline-block;
  position: relative;
  vertical-align: middle;

  &.is-selected {
    .image-container::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 2px solid #1890ff;
      border-radius: 4px;
      pointer-events: none;
    }
  }
}

.image-container {
  position: relative;
  display: inline-block;
}

.image-controls {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 4px;
  white-space: nowrap;
  min-width: 300px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  font-size: 12px;
  color: #666;
  min-width: 40px;
}
</style>
