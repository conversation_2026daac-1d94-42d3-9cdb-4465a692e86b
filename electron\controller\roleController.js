'use strict'

const RoleService = require('../service/roleService')

/**
 * 角色管理控制器
 * @class
 */
class RoleController {
  constructor(ctx) {
    this.roleService = new RoleService()
  }

  /**
   * 获取角色列表
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async list(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {}

    try {
      const result = await this.roleService.list(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取单个角色信息
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async get(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的角色ID' }
    }

    try {
      const result = await this.roleService.getById(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 创建角色
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async create(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body
    const { name, description, permissions } = params

    if (!name) {
      return { success: false, message: '角色名称不能为空' }
    }

    try {
      const result = await this.roleService.create({
        name,
        description,
        permissions
      })
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 更新角色
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async update(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    const { id, ...updateData } = params
    const roleId = parseInt(id)

    if (isNaN(roleId)) {
      return { success: false, message: '无效的角色ID' }
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return { success: false, message: '没有提供需要更新的数据' }
    }

    try {
      const result = await this.roleService.update(roleId, updateData)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 删除角色
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async delete(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的角色ID' }
    }

    try {
      const result = await this.roleService.delete(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
}

RoleController.toString = () => '[class RoleController]'

module.exports = RoleController
