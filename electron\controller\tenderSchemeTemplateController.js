'use strict'

const TenderSchemeTemplateService = require('../service/tenderSchemeTemplateService')

/**
 * 招标方案模板控制器
 * @class
 */
class TenderSchemeTemplateController {
  constructor(ctx) {
    this.tenderSchemeTemplateService = new TenderSchemeTemplateService()
  }

  /**
   * 创建招标方案模板
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async create(args, ctx) {
    // 获取前端参数：优先从args获取，如果为空则从ctx.request.body获取
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    try {
      const result = await this.tenderSchemeTemplateService.create(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取招标方案模板列表
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async list(args, ctx) {
    // 获取查询参数：优先从args获取，如果为空则从ctx.query获取
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {}

    try {
      const result = await this.tenderSchemeTemplateService.list(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取单个招标方案模板
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async get(args, ctx) {
    // 获取ID参数
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的模板ID' }
    }

    try {
      const result = await this.tenderSchemeTemplateService.getById(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 更新招标方案模板
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async update(args, ctx) {
    // 获取参数：优先从args获取，如果为空则从ctx.request.body获取
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    const { id, ...updateData } = params
    const templateId = parseInt(id)

    if (isNaN(templateId)) {
      return { success: false, message: '无效的模板ID' }
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return { success: false, message: '没有提供需要更新的数据' }
    }

    try {
      const result = await this.tenderSchemeTemplateService.update(templateId, updateData)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 删除招标方案模板
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async delete(args, ctx) {
    // 获取ID参数
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的模板ID' }
    }

    try {
      const result = await this.tenderSchemeTemplateService.delete(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
}

TenderSchemeTemplateController.toString = () => '[class TenderSchemeTemplateController]'

module.exports = TenderSchemeTemplateController
