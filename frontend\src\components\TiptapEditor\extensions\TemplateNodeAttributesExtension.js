import { Extension } from '@tiptap/core'
import { Paragraph } from '@tiptap/extension-paragraph'
import { Heading } from '@tiptap/extension-heading'
import { BulletList } from '@tiptap/extension-bullet-list'
import { OrderedList } from '@tiptap/extension-ordered-list'
import { HorizontalRule } from '@tiptap/extension-horizontal-rule'
import { Table } from '@tiptap/extension-table'
import { PageBreakExtension } from './PageBreakExtension'
import { TableOfContentsExtension } from './TableOfContentsExtension'

/**
 * 模板节点属性扩展
 * 为所有一级节点（段落、标题、列表、表格、分页符、目录等）添加模板管理相关的自定义属性
 * 这些属性用于支持模板的分级管理和只读内容控制
 */

// 通用的模板属性定义
const TEMPLATE_ATTRIBUTES = {
  // 数据库中的段落ID
  dbParagraphId: {
    default: null,
    parseHTML: element => element.getAttribute('data-db-paragraph-id'),
    renderHTML: attributes => {
      if (!attributes.dbParagraphId) {
        return {}
      }
      return {
        'data-db-paragraph-id': attributes.dbParagraphId
      }
    }
  },
  // 所属模板ID
  templateId: {
    default: null,
    parseHTML: element => element.getAttribute('data-template-id'),
    renderHTML: attributes => {
      if (!attributes.templateId) {
        return {}
      }
      return {
        'data-template-id': attributes.templateId
      }
    }
  },
  // 是否为只读内容
  isReadonly: {
    default: false,
    parseHTML: element => element.getAttribute('data-is-readonly') === 'true',
    renderHTML: attributes => {
      if (!attributes.isReadonly) {
        return {}
      }
      return {
        'data-is-readonly': 'true'
      }
    }
  }
}

// 扩展段落节点
const EnhancedParagraph = Paragraph.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      ...TEMPLATE_ATTRIBUTES
    }
  }
})

// 扩展标题节点
const EnhancedHeading = Heading.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      ...TEMPLATE_ATTRIBUTES
    }
  }
})

// 扩展无序列表节点
const EnhancedBulletList = BulletList.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      ...TEMPLATE_ATTRIBUTES
    }
  }
})

// 扩展有序列表节点
const EnhancedOrderedList = OrderedList.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      ...TEMPLATE_ATTRIBUTES
    }
  }
})

// 扩展水平线节点
const EnhancedHorizontalRule = HorizontalRule.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      ...TEMPLATE_ATTRIBUTES
    }
  }
})

// 扩展表格节点
const EnhancedTable = Table.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      ...TEMPLATE_ATTRIBUTES
    }
  }
})

// 扩展分页符节点
const EnhancedPageBreak = PageBreakExtension.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      ...TEMPLATE_ATTRIBUTES
    }
  }
})

// 扩展目录节点
const EnhancedTableOfContents = TableOfContentsExtension.extend({
  addAttributes() {
    return {
      ...this.parent?.(),
      ...TEMPLATE_ATTRIBUTES
    }
  }
})

export const TemplateNodeAttributesExtension = Extension.create({
  name: 'templateNodeAttributes',

  addExtensions() {
    return [
      EnhancedParagraph,
      EnhancedHeading,
      EnhancedBulletList,
      EnhancedOrderedList,
      EnhancedHorizontalRule,
      EnhancedTable,
      EnhancedPageBreak,
      EnhancedTableOfContents
    ]
  }
})
