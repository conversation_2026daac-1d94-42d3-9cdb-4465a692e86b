{"name": "bidding-document-assistant", "version": "1.0.0", "description": "招标文件助手", "main": "./public/electron/main.js", "scripts": {"dev": "ee-bin dev", "build": "npm run build-frontend && npm run build-electron && ee-bin encrypt", "start": "ee-bin start", "dev-frontend": "ee-bin dev --serve=frontend", "dev-electron": "ee-bin dev --serve=electron", "dev-go": "ee-bin exec --cmds=go", "dev-go-w": "ee-bin exec --cmds=go_w", "dev-python": "ee-bin exec --cmds=python", "build-frontend": "ee-bin build --cmds=frontend && ee-bin move --flag=frontend_dist", "build-electron": "ee-bin build --cmds=electron", "build-go-w": "ee-bin move --flag=go_static,go_config,go_package && ee-bin build --cmds=go_w", "build-go-m": "ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_m", "build-go-l": "ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_l", "build-python": "ee-bin build --cmds=python && ee-bin move --flag=python_dist", "encrypt": "ee-bin encrypt", "icon": "ee-bin icon", "re-sqlite": "electron-rebuild -f -w better-sqlite3", "build-w": "ee-bin build --cmds=win64", "build-we": "ee-bin build --cmds=win_e", "build-m": "ee-bin build --cmds=mac", "build-m-arm64": "ee-bin build --cmds=mac_arm64", "build-l": "ee-bin build --cmds=linux", "debug-dev": "cross-env DEBUG=ee-* ee-bin dev", "debug-encrypt": "ee-bin encrypt", "debug-electron": "cross-env DEBUG=ee-* ee-bin dev --serve=electron", "debug-move": "ee-bin move --flag=frontend_dist"}, "repository": "https://github.com/dromara/electron-egg.git", "keywords": ["Electron", "electron-egg", "ElectronEgg"], "author": "山东浪潮创新创业科技有限公司", "license": "Apache", "devDependencies": {"@electron/rebuild": "^3.7.1", "@types/better-sqlite3": "^7.6.12", "@types/node": "^20.16.0", "cross-env": "^7.0.3", "debug": "^4.4.0", "ee-bin": "^4.1.7", "electron": "^31.7.6", "electron-builder": "^25.1.8"}, "dependencies": {"axios": "^1.7.9", "bcrypt": "^6.0.0", "better-sqlite3": "^11.7.0", "dayjs": "^1.11.13", "ee-core": "^4.1.2", "electron-updater": "^6.3.8", "node-fetch": "^2.7.0", "openai": "^5.1.0", "ws": "^8.18.2"}}