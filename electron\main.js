const { ElectronEgg } = require('ee-core');
const { Lifecycle } = require('./preload/lifecycle');
// const { preload } = require('./preload'); // 移除对 preload 脚本的直接导入

// new app
const app = new ElectronEgg();

// register lifecycle
const life = new Lifecycle();
app.register("ready", life.ready);
app.register("electron-app-ready", life.electronAppReady);
app.register("window-ready", life.windowReady);
app.register("before-close", life.beforeClose);

// register preload
// app.register("preload", preload); // 移除 preload 的注册，ElectronEgg 应该通过 webPreferences.preload 处理

// run
app.run();