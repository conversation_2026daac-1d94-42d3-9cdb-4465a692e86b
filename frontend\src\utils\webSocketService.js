/**
 * 前端WebSocket服务
 * 负责连接后端WebSocket服务器，接收风险评估等实时通知
 */
import { message } from 'ant-design-vue'
import store from '@/store'

class WebSocketService {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 5000
    this.isConnecting = false
    this.isManualClose = false
    this.listeners = new Map() // 事件监听器
  }

  /**
   * 连接WebSocket服务器
   */
  connect() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      console.log('WebSocket已连接')
      return
    }

    if (this.isConnecting) {
      console.log('WebSocket正在连接中...')
      return
    }

    try {
      this.isConnecting = true
      this.isManualClose = false

      // 获取服务器地址
      const IP = localStorage.getItem('IP') || 'localhost'
      const wsUrl = `ws://${IP}:8081`

      console.log('正在连接WebSocket服务器:', wsUrl)
      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
      this.ws.onerror = this.handleError.bind(this)
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.isConnecting = false
      this.scheduleReconnect()
    }
  }

  /**
   * 连接打开处理
   */
  handleOpen() {
    console.log('WebSocket连接已建立')
    this.isConnecting = false
    this.reconnectAttempts = 0

    // 发送用户认证信息
    const user = store.state.auth.user
    if (user) {
      this.send({
        type: 'auth',
        userId: user.id
      })
    }

    // 发送心跳
    this.startHeartbeat()
  }

  /**
   * 消息处理
   * @param {MessageEvent} event 
   */
  handleMessage(event) {
    try {
      const data = JSON.parse(event.data)
      console.log('收到WebSocket消息:', data)

      switch (data.type) {
        case 'auth_success':
          console.log('WebSocket认证成功')
          break

        case 'auth_failed':
          console.error('WebSocket认证失败:', data.message)
          break

        case 'pong':
          // 心跳响应，无需处理
          break

        case 'risk_assessment_completed':
          this.handleRiskAssessmentCompleted(data.data)
          break

        case 'risk_assessment_failed':
          this.handleRiskAssessmentFailed(data.data)
          break

        case 'risk_assessment_cancelled':
          this.handleRiskAssessmentCancelled(data.data)
          break

        default:
          console.log('收到未知类型的WebSocket消息:', data.type)
          // 触发自定义事件监听器
          this.emit(data.type, data.data)
          break
      }
    } catch (error) {
      console.error('解析WebSocket消息失败:', error)
    }
  }

  /**
   * 连接关闭处理
   */
  handleClose(event) {
    console.log('WebSocket连接已关闭', event.code, event.reason)
    this.isConnecting = false
    this.stopHeartbeat()

    if (!this.isManualClose) {
      this.scheduleReconnect()
    }
  }

  /**
   * 连接错误处理
   */
  handleError(error) {
    console.error('WebSocket连接错误:', error)
    this.isConnecting = false
  }

  /**
   * 发送消息
   * @param {Object} data 消息数据
   */
  send(data) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(data))
        return true
      } catch (error) {
        console.error('发送WebSocket消息失败:', error)
        return false
      }
    } else {
      console.warn('WebSocket未连接，无法发送消息')
      return false
    }
  }

  /**
   * 手动关闭连接
   */
  close() {
    this.isManualClose = true
    this.stopHeartbeat()
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  /**
   * 计划重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限，停止重连')
      return
    }

    this.reconnectAttempts++
    console.log(`将在${this.reconnectInterval / 1000}秒后尝试第${this.reconnectAttempts}次重连...`)

    setTimeout(() => {
      if (!this.isManualClose) {
        this.connect()
      }
    }, this.reconnectInterval)
  }

  /**
   * 开始心跳检测
   */
  startHeartbeat() {
    this.heartbeatTimer = setInterval(() => {
      this.send({ type: 'ping' })
    }, 30000) // 每30秒发送一次心跳
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 处理风险评估完成通知
   * @param {Object} data 评估结果数据
   */
  handleRiskAssessmentCompleted(data) {
    const { taskId, documentType, documentId, result } = data
    
    // 显示成功通知
    message.success({
      content: `风险评估已完成！文档类型：${this.getDocumentTypeName(documentType)}`,
      duration: 5
    })

    // 触发自定义事件，供页面监听
    this.emit('risk_assessment_completed', data)

    console.log('风险评估完成:', data)
  }

  /**
   * 处理风险评估失败通知
   * @param {Object} data 失败信息
   */
  handleRiskAssessmentFailed(data) {
    const { taskId, error } = data
    
    // 显示错误通知
    message.error({
      content: `风险评估失败：${error}`,
      duration: 8
    })

    // 触发自定义事件
    this.emit('risk_assessment_failed', data)

    console.error('风险评估失败:', data)
  }

  /**
   * 处理风险评估取消通知
   * @param {Object} data 取消信息
   */
  handleRiskAssessmentCancelled(data) {
    const { taskId } = data
    
    // 显示信息通知
    message.info({
      content: '风险评估已取消',
      duration: 3
    })

    // 触发自定义事件
    this.emit('risk_assessment_cancelled', data)

    console.log('风险评估已取消:', data)
  }

  /**
   * 获取文档类型显示名称
   * @param {string} documentType 文档类型
   * @returns {string} 显示名称
   */
  getDocumentTypeName(documentType) {
    const typeMap = {
      'tender_scheme': '招标方案',
      'bidding_document': '招标文件'
    }
    return typeMap[documentType] || documentType
  }

  /**
   * 添加事件监听器
   * @param {string} event 事件名称
   * @param {Function} listener 监听器函数
   */
  on(event, listener) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(listener)
  }

  /**
   * 移除事件监听器
   * @param {string} event 事件名称
   * @param {Function} listener 监听器函数
   */
  off(event, listener) {
    if (!this.listeners.has(event)) {
      return
    }
    const listeners = this.listeners.get(event)
    const index = listeners.indexOf(listener)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }

  /**
   * 触发事件
   * @param {string} event 事件名称
   * @param {*} data 事件数据
   */
  emit(event, data) {
    if (!this.listeners.has(event)) {
      return
    }
    const listeners = this.listeners.get(event)
    listeners.forEach(listener => {
      try {
        listener(data)
      } catch (error) {
        console.error('事件监听器执行错误:', error)
      }
    })
  }

  /**
   * 获取连接状态
   * @returns {boolean} 是否已连接
   */
  isConnected() {
    return this.ws && this.ws.readyState === WebSocket.OPEN
  }

  /**
   * 获取连接状态文本
   * @returns {string} 状态文本
   */
  getStatus() {
    if (!this.ws) return '未连接'
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return '连接中'
      case WebSocket.OPEN: return '已连接'
      case WebSocket.CLOSING: return '关闭中'
      case WebSocket.CLOSED: return '已关闭'
      default: return '未知状态'
    }
  }
}

// 创建单例实例
const webSocketService = new WebSocketService()

export default webSocketService 