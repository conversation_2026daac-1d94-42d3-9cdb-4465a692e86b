/*
 * 如果启用了上下文隔离，渲染进程无法使用electron的api，
 * 可通过contextBridge 导出api给渲染进程使用
 */

const { contextBridge, ipcRenderer } = require('electron');

// 将 ipcRenderer 的必要方法暴露给渲染进程
contextBridge.exposeInMainWorld('electron', {
  ipcRenderer: {
    send: (channel, ...args) => ipcRenderer.send(channel, ...args),
    invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
    on: (channel, listener) => {
      const subscription = (event, ...args) => listener(event, ...args);
      ipcRenderer.on(channel, subscription);
      return () => ipcRenderer.removeListener(channel, subscription);
    },
    once: (channel, listener) => {
        const subscription = (event, ...args) => listener(event, ...args);
        ipcRenderer.once(channel, subscription);
        return () => ipcRenderer.removeListener(channel, subscription);
    },
    removeListener: (channel, listener) => {
      ipcRenderer.removeListener(channel, listener);
    },
    removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  },
  // 你也可以在这里暴露其他服务或值
  // e.g., version: process.versions.electron
});

// 服务初始化逻辑已移至 main process lifecycle (e.g., electron/preload/lifecycle.js)