<template>
  <div class="docx-preview-panel" :class="{ 'docx-preview-panel-collapsed': collapsed }">
    <!-- 面板头部 -->
    <div class="docx-preview-panel-header" @click="toggleCollapsed">
      <div class="header-left">
        <FileWordOutlined class="docx-icon" />
        <span class="title">文档预览</span>
        <span v-if="displayFileName" class="file-name">（{{ displayFileName }}）</span>
      </div>
      <div class="header-right">
        <a-button type="text" size="small" @click.stop="toggleCollapsed">
          <LeftOutlined :class="{ 'collapsed-icon': collapsed }" />
        </a-button>
      </div>
    </div>

    <!-- 面板内容 -->
    <div class="docx-preview-panel-content" v-show="!collapsed">
      <!-- 文档内容显示 -->
      <div class="docx-content-section" v-if="fileContent">
        <div class="docx-content-wrapper">
          <a-spin :spinning="isLoading" tip="正在加载文档...">
            <div ref="previewContainer" class="docx-preview-container"></div>
          </a-spin>
        </div>
      </div>

      <!-- 无文档状态 -->
      <div class="no-docx-state" v-else>
        <a-empty description="暂无选中的文档文件">
          <template #image>
            <FileWordOutlined style="font-size: 48px; color: #d9d9d9" />
          </template>
        </a-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { FileWordOutlined, LeftOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { renderAsync } from 'docx-preview'
import api from '@/api'

// Props定义
const props = defineProps({
  // 文件数据
  fileData: { type: File, default: null },
  // 文件名称
  fileName: { type: String, default: '' },
  // 文件路径（用于 Electron 协议加载）
  filePath: { type: String, default: '' }
})

// 响应式数据
const collapsed = ref(false)
const isLoading = ref(false)
const fileContent = ref(null)
const previewContainer = ref(null)

// 计算属性
const displayFileName = computed(() => {
  return props.fileName || props.fileData?.name || ''
})

// 方法
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

// 加载并预览 docx 文件
const loadDocxFile = async () => {
  // 如果既没有文件对象也没有文件路径，则清空内容
  if (!props.fileData && !props.filePath) {
    fileContent.value = null
    return
  }

  isLoading.value = true
  try {
    let arrayBuffer = null

    if (props.fileData) {
      // 直接从文件对象读取
      arrayBuffer = await props.fileData.arrayBuffer()
    } else if (props.filePath) {
      // 通过 API 从主进程读取本地文件
      console.log('通过 API 读取本地文件:', props.filePath)

      const response = await api.os.readLocalFile({ filePath: props.filePath })

      if (!response.success || !response.data.content) {
        throw new Error(response.message || '读取本地文件失败或文件不存在')
      }

      // 将 base64 字符串转换为 ArrayBuffer
      const binaryString = atob(response.data.content)
      const bytes = new Uint8Array(binaryString.length)
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i)
      }
      arrayBuffer = bytes.buffer
    }

    if (!arrayBuffer) {
      throw new Error('无法获取文件内容')
    }

    fileContent.value = arrayBuffer

    // 等待 DOM 更新
    await new Promise(resolve => setTimeout(resolve, 100))

    // 清空预览容器
    if (previewContainer.value) {
      previewContainer.value.innerHTML = ''
    }

    // 渲染文档预览
    if (previewContainer.value && arrayBuffer) {
      await renderAsync(arrayBuffer, previewContainer.value, undefined, {
        // 预览配置选项
        className: 'docx-preview-content',
        inWrapper: true,
        ignoreWidth: false,
        ignoreHeight: false,
        ignoreFonts: false,
        breakPages: true,
        ignoreLastRenderedPageBreak: true,
        experimental: false,
        trimXmlDeclaration: true,
        debug: false
      })
    }
  } catch (error) {
    console.error('加载文档预览失败:', error)
    message.error('加载文档预览失败: ' + error.message)
    fileContent.value = null
  } finally {
    isLoading.value = false
  }
}

// 监听文件变化
watch(() => [props.fileData, props.filePath], loadDocxFile, { immediate: true })

// 清理资源
onUnmounted(() => {
  if (previewContainer.value) {
    previewContainer.value.innerHTML = ''
  }
})
</script>

<style lang="less" scoped>
.docx-preview-panel {
  width: 21cm;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  margin-right: 16px;
  transition: all 0.3s ease;

  &.docx-preview-panel-collapsed {
    width: 48px;
    min-width: 48px;
    max-width: 48px;
  }

  .docx-preview-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #e8e8e8;
    background: #f8f9fa;
    cursor: pointer;
    user-select: none;
    min-height: 48px;

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      overflow: hidden;

      .docx-icon {
        color: #1890ff;
        font-size: 16px;
        flex-shrink: 0;
      }

      .title {
        font-weight: 600;
        color: #262626;
        white-space: nowrap;
        flex-shrink: 0;
      }

      .file-name {
        color: #666;
        font-size: 12px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 4px;
      flex-shrink: 0;

      .collapsed-icon {
        transform: rotate(180deg);
      }
    }
  }

  .docx-preview-panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .docx-content-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .docx-content-wrapper {
        flex: 1;
        padding: 16px;
        overflow: auto;
        border: 1px solid #f0f0f0;
        border-radius: 6px;
        margin: 16px;
        background: #fafafa;
      }
    }

    .no-docx-state {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
    }
  }
}

// 折叠状态下隐藏内容
.docx-preview-panel-collapsed {
  .header-left .title,
  .header-left .file-name,
  .docx-preview-panel-content {
    display: none;
  }

  .docx-preview-panel-header {
    justify-content: center;

    .header-left {
      justify-content: center;
    }

    .header-right {
      display: none;
    }
  }
}

// docx-preview 样式调整
.docx-preview-container {
  width: 100%;
  min-height: 400px;

  :deep(.docx-preview-content) {
    width: 100%;
    background: #fff;
    padding: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    // 调整文档内容样式
    p {
      line-height: 1.6;
      margin-bottom: 12px;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 20px;
      margin-bottom: 12px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 16px;

      th,
      td {
        border: 1px solid #d9d9d9;
        padding: 8px;
        text-align: left;
      }

      th {
        background-color: #f5f5f5;
        font-weight: 600;
      }
    }
  }
}
</style>
