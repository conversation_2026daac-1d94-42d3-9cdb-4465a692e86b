/**
 * 表单基础组合式函数
 * 提供表单状态管理、验证规则、提交逻辑等公共功能
 */

import { ref, reactive, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

/**
 * 表单基础功能组合式函数
 * @param {Object} options 配置选项
 * @param {string} options.entityType 实体类型 ('biddingDocument' | 'tenderScheme')
 * @param {Function} options.api API对象
 * @param {Object} options.initialFormState 初始表单状态
 * @param {Object} options.validationRules 验证规则
 * @returns {Object} 表单相关的状态和方法
 */
export function useFormBase(options) {
  const { entityType, api, initialFormState, validationRules } = options
  
  const route = useRoute()
  const router = useRouter()
  
  // 基础状态
  const formRef = ref(null)
  const isLoading = ref(false)
  const isSubmitting = ref(false)
  
  // 从路由参数获取ID，判断是否为编辑模式
  const entityId = ref(route.params.id || null)
  const isEditMode = computed(() => !!entityId.value)
  
  // 表单状态
  const formState = reactive({ ...initialFormState() })
  
  // 验证规则
  const rules = validationRules || {}
  
  // 存储原始内容，用于检查是否有变化
  const originalContent = ref({})
  const contentChanged = ref(false)
  
  /**
   * 检查内容是否有变化
   * @param {Object} currentFieldValues 当前字段值
   * @returns {boolean} 是否有变化
   */
  const checkContentChanged = (currentFieldValues) => {
    if (!isEditMode.value) {
      return false
    }
    
    const originalContentStr = JSON.stringify(originalContent.value || {})
    const currentContentStr = JSON.stringify(currentFieldValues || {})
    
    return originalContentStr !== currentContentStr
  }
  
  /**
   * 重置表单状态
   */
  const resetFormState = () => {
    Object.assign(formState, initialFormState())
    originalContent.value = {}
    contentChanged.value = false
  }
  
  /**
   * 设置原始内容（用于编辑模式的变化检测）
   * @param {Object} content 原始内容
   */
  const setOriginalContent = (content) => {
    if (isEditMode.value) {
      originalContent.value = JSON.parse(JSON.stringify(content || {}))
    }
  }
  
  /**
   * 获取页面标题
   * @param {string} createTitle 创建模式标题
   * @param {string} editTitle 编辑模式标题
   * @returns {string} 页面标题
   */
  const getPageTitle = (createTitle, editTitle) => {
    return isEditMode.value ? editTitle : createTitle
  }
  
  /**
   * 获取提交按钮文本
   * @param {string} createText 创建模式按钮文本
   * @param {string} editText 编辑模式按钮文本
   * @returns {string} 按钮文本
   */
  const getSubmitButtonText = (createText, editText) => {
    return isEditMode.value ? editText : createText
  }
  
  /**
   * 通用的API调用错误处理
   * @param {Error} error 错误对象
   * @param {string} operation 操作名称
   */
  const handleApiError = (error, operation) => {
    console.error(`${operation}失败:`, error)
    message.error(`${operation}时发生错误: ${error.message || '未知错误'}`)
  }
  
  /**
   * 通用的成功消息处理
   * @param {string} operation 操作名称
   * @param {string} customMessage 自定义消息
   */
  const handleSuccess = (operation, customMessage) => {
    message.success(customMessage || `${operation}成功`)
  }
  
  /**
   * 导航到列表页
   * @param {string} listRouteName 列表页路由名称
   */
  const navigateToList = (listRouteName) => {
    router.push({ name: listRouteName })
  }
  
  /**
   * 返回上一页
   */
  const goBack = () => {
    router.go(-1)
  }
  
  return {
    // 状态
    formRef,
    isLoading,
    isSubmitting,
    entityId,
    isEditMode,
    formState,
    rules,
    originalContent,
    contentChanged,
    
    // 方法
    checkContentChanged,
    resetFormState,
    setOriginalContent,
    getPageTitle,
    getSubmitButtonText,
    handleApiError,
    handleSuccess,
    navigateToList,
    goBack
  }
}
