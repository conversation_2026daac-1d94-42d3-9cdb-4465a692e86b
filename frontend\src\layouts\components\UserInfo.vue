<template>
  <div class="sider-bottom">
    <a-dropdown placement="top">
      <a class="ant-dropdown-link personal gap-x-[10px]" @click.prevent>
        <img class="avatar" width="36" height="36" src="~@/assets/personal.png" alt="个人头像" />
        <div class="personal-info" v-if="!collapsed">
          <div class="nickname break-keep">个人中心</div>
        </div>
      </a>
      <template #overlay>
        <a-menu class="personal-menu">
          <a-menu-item key="editinfo" @click="handleInfo(1)">
            <template #icon><i class="anticon anticon-user" /></template>
            修改信息
          </a-menu-item>
          <a-menu-item key="info" @click="handleInfo(2)">
            <template #icon><i class="anticon anticon-user" /></template>
            个人信息
          </a-menu-item>
          <a-menu-item key="logout" @click="handleLogout">
            <template #icon><i class="anticon anticon-logout" /></template>
            退出登录
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <a-modal v-model:visible="modalVisible" title="个人信息" destroyOnClose>
      <a-descriptions bordered :column="1" v-if="typeNum == 2">
        <a-descriptions-item label="用户名">{{ formState.username }}</a-descriptions-item>
        <a-descriptions-item label="昵称">{{ formState.nickname }}</a-descriptions-item>
        <a-descriptions-item label="邮箱">{{ formState.email }}</a-descriptions-item>
        <a-descriptions-item label="角色">
          <a-tag color="green">
            {{ roles.find(role => role.id === formState.role_id).description }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>
      <a-form ref="formRef" v-else :model="formState" :confirmLoading="modalLoading" layout="vertical" :rules="rules">
        <a-form-item label="用户名" name="username">
          <a-input v-model:value="formState.username" disabled />
        </a-form-item>
        <a-form-item label="昵称" name="nickname">
          <a-input v-model:value="formState.nickname" />
        </a-form-item>
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="formState.email" />
        </a-form-item>
        <!-- <a-form-item label="密码" name="password">
                    <a-input-password v-model:value="formState.password" placeholder="创建时必填" />
                </a-form-item> -->
        <!-- <a-form-item label="原始密码" name="newPassword" v-if="modalMode === 'edit'">
                    <a-input-password v-model:value="formState.password" disabled placeholder="" />
                </a-form-item> -->
        <a-form-item label="新密码" name="newPassword">
          <a-input-password v-model:value="formState.newPassword" placeholder="留空则不修改密码" />
        </a-form-item>
        <a-form-item label="角色" name="role_id">
          <a-select v-model:value="formState.role_id" disabled placeholder="请选择角色">
            <a-select-option v-for="role in roles" :key="role.id" :value="role.id">{{ role.name }} ({{ role.description }})</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="handleModalCancel">{{ typeNum === 1 ? '取消' : '关闭' }}</a-button>
        <a-button type="primary" @click="handleModalOk" v-if="typeNum == 1">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import { Modal, message } from 'ant-design-vue'
import { useRouter, useRoute } from 'vue-router'
// 使用统一的HTTP API
import api from '@/api'

const props = defineProps({
  collapsed: {
    type: Boolean,
    default: false
  }
})

const store = useStore()
const router = useRouter()
const route = useRoute()
const formRef = ref()
const currentEditUserId = ref(null)
const modalLoading = ref(false)
const modalVisible = ref(false)
const formState = ref({
  username: '',
  nickname: '',
  email: '',
  password: '',
  newPassword: '', // 用于编辑时
  role_id: undefined,
  status: 'active'
})
const typeNum = ref(0)
const roles = ref([])
const rules = {
  username: [{ required: true, message: '请输入用户名' }],
  email: [{ type: 'email', message: '请输入有效的邮箱地址' }],
  password: [{ required: true, message: '请输入密码' }], // 创建时必填
  role_id: [{ required: true, message: '请选择角色' }]
}
function menuHandle(e) {
  if (e && e.key) {
    selectedKey.value = e.key // Ant Design Menu的key现在是路由的name
    router.push({ name: e.key })
  }
}
const fetchRoles = async () => {
  try {
    // 使用新的HTTP API对象结构
    const response = await api.role.list() // response 是角色数组
    roles.value = response.data
  } catch (error) {
    message.error('获取角色列表失败: ' + error.message)
  }
}
const handleLogout = () => {
  Modal.confirm({
    title: '确认退出登录?',
    content: '退出后将返回登录页面。',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await store.dispatch('auth/logout')
        router.push('/login')
      } catch (err) {
        console.error('退出登录失败:', err)
      }
    }
  })
}
const handleInfo = type => {
  let user = JSON.parse(localStorage.getItem('user'))
  formState.value = user
  modalVisible.value = true
  typeNum.value = type
  currentEditUserId.value = user.id
}
const handleModalCancel = () => {
  modalVisible.value = false
}
const handleModalOk = async () => {
  try {
    await formRef.value.validate()
    modalLoading.value = true
    console.log('formState.role_id', formState.role_id)
    // return
    const dataToSubmit = {
      nickname: formState.value.nickname,
      email: formState.value.email,
      role_id: formState.value.role_id,
      // role_id: JSON.stringify(formState.value.role_id || []),
      status: formState.value.status
    }
    if (formState.value.newPassword) {
      dataToSubmit.password = formState.value.newPassword // 如果输入了新密码
    }
    dataToSubmit.id = currentEditUserId.value

    // return
    let result = await api.user.update({ id: currentEditUserId.value, ...dataToSubmit })
    localStorage.setItem('user', JSON.stringify(result))
    if (result.success) {
      message.success('用户信息更新成功')
    }
    modalVisible.value = false
  } catch (errorInfo) {
    if (errorInfo.message) {
      // API 调用错误
      message.error('操作失败: ' + errorInfo.message)
    } else {
      // 表单校验错误
      console.log('表单校验失败:', errorInfo)
      message.error('请检查表单输入项')
    }
  } finally {
    modalLoading.value = false
  }
}
fetchRoles()
</script>
<style lang="less" scoped>
.sider-bottom {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  padding: 3px 0 3px 0;
  display: flex;
  justify-content: center;
  background: #fff;
  box-shadow: 0 -2px 8px 0 rgba(0, 0, 0, 0.04);
  z-index: 10;
}

.personal {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 6px 18px 6px 12px;
  border-radius: 24px;
  transition: background 0.2s;

  &:hover {
    background: #f5f5f5;
  }

  .avatar {
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 2px solid #f0f0f0;
  }

  .personal-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .nickname {
      font-weight: 600;
      font-size: 15px;
      color: #222;
      line-height: 1.2;
    }

    .desc {
      font-size: 12px;
      color: #999;
      margin-top: 2px;
    }
  }

  .arrow {
    margin-left: 8px;
    display: flex;
    align-items: center;
  }
}
</style>
