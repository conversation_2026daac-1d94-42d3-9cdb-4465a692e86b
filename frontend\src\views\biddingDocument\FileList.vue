<template>
  <div>
    <a-card title="招标文件">
      <!-- 搜索和操作区域 -->
      <div class="flex justify-between mb-[16px]">
        <a-form layout="inline" :model="searchParams" class="gap-y-[10px]">
          <a-form-item label="文件名称">
            <a-input v-model:value="searchParams.name" placeholder="按文件名称模糊查询" @pressEnter="handleSearch" />
          </a-form-item>
          <a-form-item label="使用模板">
            <a-tree-select
              v-model:value="searchParams.template_id"
              style="width: 280px"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder="选择模板"
              allow-clear
              show-search
              tree-default-expand-all
              :tree-data="templateTreeData"
              :field-names="{ children: 'children', label: 'name', value: 'id' }"
              tree-node-filter-prop="name"
              :loading="isTemplatesLoading"
              @change="handleSearch"
            />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch" :loading="isLoading">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
            <a-button style="margin-left: 8px" @click="resetQuery">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-form-item>
        </a-form>

        <a-button type="primary" @click="handleCreate">
          <template #icon><PlusOutlined /></template>
          新建文件
        </a-button>
      </div>

      <!-- 列表区域 -->
      <a-table
        :columns="columns"
        :dataSource="dataSource"
        :scroll="{ x: '100%' }"
        :loading="isLoading"
        :pagination="pagination"
        rowKey="id"
        bordered
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'template_name'">{{ record.template_name || 'N/A' }}</template>
          <template v-if="column.dataIndex === 'scheme_name'">{{ record.scheme_name || 'N/A' }}</template>
          <template v-if="column.dataIndex === 'share_status'">
            <a-tag>{{ formatShareStatus(record[column.dataIndex]) }}</a-tag>
          </template>
          <template v-if="column.dataIndex === 'display_risk_status'">
            <a-tag :color="getRiskStatusColor(record[column.dataIndex])">{{ formatRiskStatus(record[column.dataIndex]) }}</a-tag>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <template v-if="isOwner(record)">
                <a-button type="link" size="small" @click="handleShare(record)">分享</a-button>
                <a-button
                  v-if="!['low', 'medium', 'high'].includes(record['display_risk_status'])"
                  type="link"
                  size="small"
                  @click="handleRiskAssessment(record)"
                  :loading="isRiskAssessing(record.id)"
                >
                  风险评估
                </a-button>
                <a-popconfirm title="确定删除此招标文件吗？" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record.id)">
                  <a-button type="link" size="small" danger>删除</a-button>
                </a-popconfirm>
              </template>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 分享模态框 -->
      <a-modal
        v-model:visible="modalVisible"
        title="分享文件"
        width="550px"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        :confirmLoading="modalLoading"
        destroyOnClose
      >
        <a-form ref="formRef" :model="formState" :label-col="{ style: { width: '90px' } }">
          <a-form-item label="分享方式" name="share_status">
            <a-radio-group v-model:value="formState.share_status" @change="handleShareStatusChange">
              <a-radio value="private">私有</a-radio>
              <a-radio value="public">公开</a-radio>
              <a-radio value="specific_users">指定用户</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="请选择用户" name="share_users" v-if="formState.share_status === 'specific_users'">
            <a-transfer
              v-model="formState.share_users"
              :target-keys="formState.share_users"
              :data-source="userList"
              :render="item => item.title"
              :titles="['可选用户', '已选择用户']"
              @change="handleTransferChange"
              show-search
              :filter-option="filterOption"
            />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { useStore } from 'vuex'
import api from '@/api'
import webSocketService from '@/utils/webSocketService'

const router = useRouter()
const store = useStore()

// 响应式数据
const dataSource = ref([])
const modalVisible = ref(false)
const isLoading = ref(false)
const modalLoading = ref(false)
const userList = reactive([])
const templateTreeData = ref([])
const isTemplatesLoading = ref(false)
// 风险评估相关状态
const riskAssessmentLoadingMap = ref(new Map()) // 记录正在进行风险评估的文件ID

// 搜索参数
const searchParams = reactive({
  name: '',
  template_id: null
})

// 分享表单状态
const formState = reactive({
  share_users: [],
  share_status: 'private',
  currentDocument: null
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: total => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  { title: '文件名称', dataIndex: 'name', width: 150, ellipsis: true },
  { title: '关联模板', dataIndex: 'template_name', width: 150, ellipsis: true },
  // { title: '关联方案', dataIndex: 'scheme_name', width: 150, ellipsis: true },
  { title: '分享状态', dataIndex: 'share_status', width: 100 },
  { title: '风险状态', dataIndex: 'display_risk_status', width: 100 },
  { title: '当前版本', dataIndex: 'version_number', width: 100 },
  { title: '更新时间', dataIndex: 'updated_at', width: 180 },
  { title: '创建时间', dataIndex: 'created_at', width: 180 },
  { title: '操作', dataIndex: 'action', width: 250, fixed: 'right' }
]

// 计算属性
const currentUserId = computed(() => store.state.auth.user?.id)

// 工具函数
const formatShareStatus = status => {
  const statusMap = {
    private: '私有',
    public: '公开',
    specific_users: '指定用户'
  }
  return statusMap[status] || '未知'
}

const formatRiskStatus = status => {
  const statusMap = {
    none: '未评估',
    processing: '评估中',
    low: '低',
    medium: '中',
    high: '高'
  }
  return statusMap[status] || '未评估'
}

const getRiskStatusColor = status => {
  const colorMap = {
    none: 'default',
    processing: 'blue',
    low: 'green',
    medium: 'orange',
    high: 'red'
  }
  return colorMap[status] || 'default'
}

const isOwner = record => {
  return record.created_by === currentUserId.value && record.updated_by === currentUserId.value
}

// 数据获取
const fetchDocumentList = async (params = {}) => {
  isLoading.value = true
  try {
    const queryParams = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      name: searchParams.name || undefined,
      template_id: searchParams.template_id === null ? undefined : searchParams.template_id,
      ...params
    }
    const response = await api.biddingDocument.list(queryParams)

    if (response?.success && response.data) {
      dataSource.value = response.data.list
      Object.assign(pagination, {
        total: response.data.pagination.total,
        current: response.data.pagination.page,
        pageSize: response.data.pagination.pageSize
      })
    } else {
      message.error(response?.message || '获取招标文件列表失败')
      dataSource.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('Error fetching bidding documents:', error)
    message.error('获取招标文件列表时发生错误')
    dataSource.value = []
    pagination.total = 0
  } finally {
    isLoading.value = false
  }
}

const fetchAllTemplates = async () => {
  isTemplatesLoading.value = true
  try {
    const response = await api.biddingDocumentTemplate.getTree()
    templateTreeData.value = response?.success && response.data ? response.data : []
  } catch (error) {
    console.error('Error fetching templates:', error)
    templateTreeData.value = []
  } finally {
    isTemplatesLoading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await api.user.list({ page: 1, pageSize: 1000 })
    userList.splice(0, userList.length, ...response.list.filter(user => user.id !== currentUserId.value).map(user => ({ key: user.id, title: user.username })))
  } catch (error) {
    message.error('获取用户列表失败: ' + error.message)
  }
}

// 事件处理
const handleSearch = () => {
  pagination.current = 1
  fetchDocumentList()
}

const resetQuery = () => {
  searchParams.name = ''
  searchParams.template_id = null
  pagination.current = 1
  fetchDocumentList()
}

const handleTableChange = pageInfo => {
  pagination.current = pageInfo.current
  pagination.pageSize = pageInfo.pageSize
  fetchDocumentList()
}

const handleCreate = () => {
  router.push({ name: 'BiddingDocumentFileNew' })
}

const handleEdit = record => {
  router.push({ name: 'BiddingDocumentFileEdit', params: { id: record.id } })
}

const handleShare = async record => {
  formState.currentDocument = record
  formState.share_status = record.share_status || 'private'

  // 解析已分享的用户ID
  if (record.share_status === 'specific_users' && record.share_users) {
    formState.share_users = record.share_users
      .split(',')
      .map(id => parseInt(id.trim()))
      .filter(id => !isNaN(id))
  } else {
    formState.share_users = []
  }

  await fetchUsers()
  modalVisible.value = true
}

const handleDelete = async id => {
  try {
    const response = await api.biddingDocument.delete({ id })
    if (response?.success) {
      message.success(response.message || '删除成功！')
      fetchDocumentList()
    } else {
      message.error(response?.message || '删除失败')
    }
  } catch (error) {
    console.error('Error deleting bidding document:', error)
    message.error('删除时发生错误: ' + (error.message || '未知错误'))
  }
}

// 分享相关处理
const handleShareStatusChange = () => {
  formState.share_users = []
}

const handleTransferChange = keys => {
  formState.share_users = keys
}

const filterOption = (inputValue, option) => {
  return option.title.toLowerCase().includes(inputValue.toLowerCase())
}

const handleModalCancel = () => {
  userList.splice(0, userList.length)
  Object.assign(formState, {
    share_users: [],
    share_status: 'private',
    currentDocument: null
  })
  modalVisible.value = false
}

const getConfirmContent = () => {
  const statusTexts = {
    private: '将文件设置为私有（仅自己可见）',
    public: '将文件设置为公开（所有用户可见）',
    specific_users: () => {
      const userNames = userList
        .filter(user => formState.share_users.includes(user.key))
        .map(user => user.title)
        .join(',')
      return `将文件分享给指定用户：${userNames}`
    }
  }

  const content = statusTexts[formState.share_status]
  return typeof content === 'function' ? content() : content
}

const handleModalOk = async () => {
  if (formState.share_status === 'specific_users' && formState.share_users.length === 0) {
    message.error('请选择要分享的用户')
    return
  }

  Modal.confirm({
    title: '确认分享设置？',
    content: getConfirmContent(),
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const updateData = {
          share_status: formState.share_status,
          share_users: formState.share_status === 'specific_users' ? formState.share_users.join(',') : null
        }

        const result = await api.biddingDocument.update({
          id: formState.currentDocument.id,
          ...updateData
        })

        if (result.success) {
          message.success('分享设置更新成功')
          fetchDocumentList()
          handleModalCancel()
        } else {
          message.error(result.message || '分享设置更新失败')
        }
      } catch (err) {
        console.error('分享设置更新失败:', err)
        message.error('分享设置更新失败: ' + (err.message || '未知错误'))
      }
    }
  })
}

// 风险评估相关处理
const isRiskAssessing = id => riskAssessmentLoadingMap.value.has(id)

const handleRiskAssessment = async record => {
  if (isRiskAssessing(record.id)) {
    message.warning('此文件正在评估中，请稍后再试')
    return
  }

  // 显示确认对话框
  Modal.confirm({
    title: '确认进行风险评估？',
    content: `即将对招标文件"${record.name}"进行风险评估。评估过程可能需要一些时间，请耐心等待。评估完成后将自动推送通知。`,
    okText: '确定评估',
    cancelText: '取消',
    onOk: async () => {
      riskAssessmentLoadingMap.value.set(record.id, true)

      try {
        const response = await api.riskAssessment.startAssessment({
          document_type: 'bidding_document',
          document_id: record.id
        })

        if (response?.success) {
          message.success('风险评估已启动，评估完成后将自动通知您')
          console.log('风险评估任务已启动，任务ID:', response.data?.taskId)
        } else {
          message.error(response?.message || '启动风险评估失败')
          riskAssessmentLoadingMap.value.delete(record.id)
        }
      } catch (error) {
        console.error('启动风险评估时发生错误:', error)
        message.error('启动风险评估时发生错误: ' + (error.message || '未知错误'))
        riskAssessmentLoadingMap.value.delete(record.id)
      }
    }
  })
}

// WebSocket事件监听
const setupWebSocketListeners = () => {
  // 监听风险评估完成事件
  const handleRiskAssessmentCompleted = data => {
    const { documentType, documentId } = data
    if (documentType === 'bidding_document') {
      // 清除加载状态
      riskAssessmentLoadingMap.value.delete(documentId)
      // 刷新列表以显示最新的风险状态
      fetchDocumentList()
    }
  }

  // 监听风险评估失败事件
  const handleRiskAssessmentFailed = data => {
    const { documentType, documentId } = data
    if (documentType === 'bidding_document') {
      // 清除加载状态
      riskAssessmentLoadingMap.value.delete(documentId)
    }
  }

  // 监听风险评估取消事件
  const handleRiskAssessmentCancelled = data => {
    const { documentType, documentId } = data
    if (documentType === 'bidding_document') {
      // 清除加载状态
      riskAssessmentLoadingMap.value.delete(documentId)
    }
  }

  // 添加事件监听器
  webSocketService.on('risk_assessment_completed', handleRiskAssessmentCompleted)
  webSocketService.on('risk_assessment_failed', handleRiskAssessmentFailed)
  webSocketService.on('risk_assessment_cancelled', handleRiskAssessmentCancelled)

  // 返回清理函数
  return () => {
    webSocketService.off('risk_assessment_completed', handleRiskAssessmentCompleted)
    webSocketService.off('risk_assessment_failed', handleRiskAssessmentFailed)
    webSocketService.off('risk_assessment_cancelled', handleRiskAssessmentCancelled)
  }
}

// 生命周期
onMounted(() => {
  fetchAllTemplates()
  fetchDocumentList()

  // 设置WebSocket事件监听器
  const cleanupWebSocket = setupWebSocketListeners()

  // 组件卸载时清理事件监听器
  onUnmounted(() => {
    cleanupWebSocket()
  })
})
</script>

<style scoped>
/* Add any specific styles if needed */
</style>
