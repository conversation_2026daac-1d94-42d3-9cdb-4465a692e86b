import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'

export const readonlyContentPluginKey = new PluginKey('readonlyContent')

export const ReadonlyContentExtension = Extension.create({
  name: 'readonlyContentExtension',

  addOptions() {
    return {
      // 获取只读段落ID数组的函数（数据库中的段落ID）
      getReadonlyParagraphIds: () => [],
      // 获取当前模板ID的函数
      getCurrentTemplateId: () => null
    }
  },

  addProseMirrorPlugins() {
    const { getReadonlyParagraphIds, getCurrentTemplateId } = this.options

    return [
      // 只读段落标记插件 - 用于给只读段落添加视觉标记
      new Plugin({
        key: new PluginKey('readonlyMarking'),
        view(editorView) {
          // 标记只读段落的函数
          const markReadonlyParagraphs = () => {
            const readonlyIds = getReadonlyParagraphIds()
            const currentTemplateId = getCurrentTemplateId()

            if (!readonlyIds || readonlyIds.length === 0) {
              return
            }

            const { state } = editorView
            const tr = state.tr
            let hasChanges = false

            // 遍历文档中的所有一级节点
            state.doc.descendants((node, pos) => {
              // 检查所有可能的一级节点类型
              const isTopLevelNode = ['paragraph', 'heading', 'bulletList', 'orderedList', 'table', 'horizontalRule', 'pageBreak', 'tableOfContents'].includes(
                node.type.name
              )

              if (isTopLevelNode) {
                const shouldBeReadonly = isNodeReadonly(node, readonlyIds, currentTemplateId)
                const currentlyReadonly = node.attrs.isReadonly === true

                // 如果状态不匹配，更新属性
                if (shouldBeReadonly !== currentlyReadonly) {
                  tr.setNodeMarkup(pos, null, {
                    ...node.attrs,
                    isReadonly: shouldBeReadonly
                  })
                  hasChanges = true
                  console.log(`[ReadonlyMarking] 更新${node.type.name}只读状态: pos=${pos}, readonly=${shouldBeReadonly}`)
                }
              }
            })

            // 如果有变化，应用事务
            if (hasChanges) {
              editorView.dispatch(tr)
            }
          }

          // 初始标记
          setTimeout(markReadonlyParagraphs, 100)

          return {
            update: (view, prevState) => {
              // 当文档内容或只读条件变化时重新标记
              if (view.state.doc !== prevState.doc) {
                setTimeout(markReadonlyParagraphs, 50)
              }
            }
          }
        }
      }),
      new Plugin({
        key: readonlyContentPluginKey,
        props: {
          // 处理键盘输入
          handleKeyDown(view, event) {
            const readonlyIds = getReadonlyParagraphIds()
            const currentTemplateId = getCurrentTemplateId()

            // 如果没有只读段落ID，则允许所有操作
            if (!readonlyIds || readonlyIds.length === 0) {
              return false
            }

            const { state } = view
            const { selection } = state
            const { $from, $to } = selection

            // 特殊处理 Enter 键：在只读段落的起始或末尾位置允许换行
            if (event.key === 'Enter') {
              const result = handleEnterInReadonlyParagraph(view, state, selection, readonlyIds, currentTemplateId)
              if (result) {
                event.preventDefault()
                return true
              }
            }

            // 检查当前选择是否在只读内容中
            if (isPositionInReadonlyContent($from, readonlyIds, currentTemplateId) || isPositionInReadonlyContent($to, readonlyIds, currentTemplateId)) {
              // 阻止在只读内容中的其他编辑操作
              if (event.key === 'Backspace' || event.key === 'Delete' || event.key.length === 1) {
                event.preventDefault()
                return true
              }
            }

            return false
          },

          // 处理鼠标点击
          handleClick(view, pos, event) {
            const readonlyIds = getReadonlyParagraphIds()
            const currentTemplateId = getCurrentTemplateId()

            // 如果没有只读段落ID，则允许所有操作
            if (!readonlyIds || readonlyIds.length === 0) {
              return false
            }

            const { state } = view
            const $pos = state.doc.resolve(pos)

            if (isPositionInReadonlyContent($pos, readonlyIds, currentTemplateId)) {
              // 在只读内容中点击时，将光标移动到只读段落的前面或后面
              const editablePos = findNearestEditablePositionAroundReadonly(state, pos, readonlyIds, currentTemplateId)
              if (editablePos !== -1) {
                const tr = state.tr.setSelection(state.selection.constructor.near(state.doc.resolve(editablePos)))
                view.dispatch(tr)
              }
              return true
            }

            return false
          },

          // 过滤事务，阻止对只读内容的修改
          filterTransaction(transaction, state) {
            const readonlyIds = getReadonlyParagraphIds()
            const currentTemplateId = getCurrentTemplateId()

            // 如果没有只读段落ID，则允许所有事务
            if (!readonlyIds || readonlyIds.length === 0) {
              return true
            }

            // 如果事务不改变文档，允许通过
            if (!transaction.docChanged) {
              return true
            }

            // 检查是否是我们的特殊换行操作（通过元数据标记）
            if (transaction.getMeta('allowReadonlyBreak')) {
              return true
            }

            // 检查事务是否试图修改只读内容
            try {
              // 遍历事务的每个步骤，检查是否涉及只读内容的修改
              for (const step of transaction.steps) {
                // 检查步骤的影响范围
                if (step.from !== undefined && step.to !== undefined) {
                  // 检查被修改的范围是否包含只读内容
                  for (let pos = step.from; pos < step.to; pos++) {
                    try {
                      const $pos = state.doc.resolve(pos)
                      if (isPositionInReadonlyContent($pos, readonlyIds, currentTemplateId)) {
                        console.log('阻止修改只读内容，位置:', pos)
                        return false
                      }
                    } catch (e) {
                      // 位置解析失败，跳过
                      continue
                    }
                  }
                }
              }

              // 如果没有检测到对只读内容的修改，允许事务通过
              return true
            } catch (error) {
              console.warn('filterTransaction 检查时出错:', error)
              // 出错时允许事务通过，避免阻塞正常编辑
              return true
            }
          }
        }
      })
    ]
  }
})

// 辅助函数：判断节点是否应该为只读
function isNodeReadonly(node, readonlyParagraphIds, currentTemplateId) {
  if (!node || !node.attrs) {
    return false
  }

  // 检查节点是否已经有只读标记（父模板内容）
  if (node.attrs.isReadonly) {
    return true
  }

  // 检查节点的数据库段落ID是否在只读列表中
  if (readonlyParagraphIds && readonlyParagraphIds.length > 0 && node.attrs.dbParagraphId && readonlyParagraphIds.includes(node.attrs.dbParagraphId)) {
    return true
  }

  // 检查templateId是否与当前模板ID不同（使用 != 而不是 !== 来处理类型转换）
  if (readonlyParagraphIds && readonlyParagraphIds.length > 0 && currentTemplateId && node.attrs.templateId && node.attrs.templateId != currentTemplateId) {
    return true
  }

  return false
}

// 辅助函数：检查位置是否在只读内容中
function isPositionInReadonlyContent($pos, readonlyParagraphIds, currentTemplateId) {
  if (!$pos) {
    return false
  }

  // 遍历位置的所有祖先节点，检查是否有只读内容ID或只读标记
  for (let depth = 0; depth <= $pos.depth; depth++) {
    const node = $pos.node(depth)

    // 检查所有可能的一级节点类型
    const isTopLevelNode = ['paragraph', 'heading', 'bulletList', 'orderedList', 'table', 'horizontalRule', 'pageBreak', 'tableOfContents'].includes(node.type.name)

    if (isTopLevelNode) {
      console.log('[isPositionInReadonlyContent] 检查一级节点:', {
        type: node.type.name,
        dbParagraphId: node.attrs?.dbParagraphId,
        templateId: node.attrs?.templateId,
        isReadonly: node.attrs?.isReadonly,
        currentTemplateId: currentTemplateId,
        readonlyParagraphIds: readonlyParagraphIds
      })

      // 使用统一的只读判断逻辑
      if (isNodeReadonly(node, readonlyParagraphIds, currentTemplateId)) {
        console.log('[isPositionInReadonlyContent] 一级节点判断为只读')
        return true
      }
    }

    // 对于非一级节点，保持原有逻辑
    if (!isTopLevelNode) {
      // 检查节点是否有只读标记（父模板内容）
      if (node.attrs && node.attrs.isReadonly) {
        console.log('[isPositionInReadonlyContent] 节点有isReadonly标记，判断为只读')
        return true
      }

      // 检查节点的数据库段落ID是否在只读列表中
      if (readonlyParagraphIds && readonlyParagraphIds.length > 0 && node.attrs && node.attrs.dbParagraphId && readonlyParagraphIds.includes(node.attrs.dbParagraphId)) {
        console.log(`[isPositionInReadonlyContent] 段落ID ${node.attrs.dbParagraphId} 在只读列表中，判断为只读`)
        return true
      }

      // 只有当有只读段落ID时，才检查templateId（避免在编辑当前模板时误判）
      // 使用 != 而不是 !== 来处理类型转换
      if (
        readonlyParagraphIds &&
        readonlyParagraphIds.length > 0 &&
        currentTemplateId &&
        node.attrs &&
        node.attrs.templateId &&
        node.attrs.templateId != currentTemplateId
      ) {
        console.log(`[isPositionInReadonlyContent] templateId ${node.attrs.templateId} != ${currentTemplateId}，判断为只读`)
        return true
      }
    }
  }

  console.log('[isPositionInReadonlyContent] 未找到只读条件，判断为可编辑')
  return false
}

// 辅助函数：找到最近的可编辑位置
function findNearestEditablePosition(state, pos, readonlyParagraphIds, currentTemplateId) {
  const doc = state.doc

  // 向前搜索可编辑位置
  for (let i = pos; i >= 0; i--) {
    const $pos = doc.resolve(i)
    if (!isPositionInReadonlyContent($pos, readonlyParagraphIds, currentTemplateId)) {
      return i
    }
  }

  // 向后搜索可编辑位置
  for (let i = pos; i <= doc.content.size; i++) {
    const $pos = doc.resolve(i)
    if (!isPositionInReadonlyContent($pos, readonlyParagraphIds, currentTemplateId)) {
      return i
    }
  }

  return -1 // 没有找到可编辑位置
}

// 辅助函数：找到只读段落前后的可编辑位置
function findNearestEditablePositionAroundReadonly(state, pos, readonlyParagraphIds, currentTemplateId) {
  const doc = state.doc
  const $pos = state.doc.resolve(pos)

  // 找到当前只读段落的边界
  let readonlyStart = pos
  let readonlyEnd = pos

  // 向前找到只读段落的开始
  for (let i = pos; i >= 0; i--) {
    const $testPos = doc.resolve(i)
    if (isPositionInReadonlyContent($testPos, readonlyParagraphIds, currentTemplateId)) {
      readonlyStart = i
    } else {
      break
    }
  }

  // 向后找到只读段落的结束
  for (let i = pos; i <= doc.content.size; i++) {
    const $testPos = doc.resolve(i)
    if (isPositionInReadonlyContent($testPos, readonlyParagraphIds, currentTemplateId)) {
      readonlyEnd = i
    } else {
      break
    }
  }

  // 优先选择段落后面的位置（用于添加新内容）
  if (readonlyEnd + 1 <= doc.content.size) {
    const $afterPos = doc.resolve(readonlyEnd + 1)
    if (!isPositionInReadonlyContent($afterPos, readonlyParagraphIds, currentTemplateId)) {
      return readonlyEnd + 1
    }
  }

  // 如果后面不可编辑，选择段落前面的位置
  if (readonlyStart - 1 >= 0) {
    const $beforePos = doc.resolve(readonlyStart - 1)
    if (!isPositionInReadonlyContent($beforePos, readonlyParagraphIds, currentTemplateId)) {
      return readonlyStart - 1
    }
  }

  // 如果都不行，使用原来的逻辑
  return findNearestEditablePosition(state, pos, readonlyParagraphIds, currentTemplateId)
}

// 处理在只读段落起始/末尾位置的换行操作
function handleEnterInReadonlyParagraph(view, state, selection, readonlyIds, currentTemplateId) {
  const { $from } = selection

  // 检查当前位置是否在只读段落中
  if (!isPositionInReadonlyContent($from, readonlyIds, currentTemplateId)) {
    return false // 不在只读段落中，不处理
  }

  // 找到当前只读段落节点
  const readonlyParagraph = findReadonlyParagraphNode($from, readonlyIds, currentTemplateId)
  if (!readonlyParagraph) {
    return false
  }

  const { node: paragraphNode, pos: paragraphPos } = readonlyParagraph
  const paragraphStart = paragraphPos + 1 // 段落内容开始位置
  const paragraphEnd = paragraphPos + paragraphNode.nodeSize - 1 // 段落内容结束位置
  const cursorPos = $from.pos

  // 检查光标是否在段落的起始或末尾位置
  const isAtStart = cursorPos === paragraphStart
  const isAtEnd = cursorPos === paragraphEnd

  if (!isAtStart && !isAtEnd) {
    return false // 不在起始或末尾位置，不允许换行
  }

  const tr = state.tr

  // 添加特殊标记，让 filterTransaction 知道这是允许的操作
  tr.setMeta('allowReadonlyBreak', true)

  if (isAtStart) {
    // 在起始位置换行：在当前段落前插入新的空段落
    const newParagraph = state.schema.nodes.paragraph.create(
      { dbParagraphId: null, templateId: null }, // 新段落属性为空，可编辑
      []
    )

    tr.insert(paragraphPos, newParagraph)

    // 将光标移动到新段落中
    const newCursorPos = paragraphPos + 1
    tr.setSelection(state.selection.constructor.near(tr.doc.resolve(newCursorPos)))
  } else if (isAtEnd) {
    // 在末尾位置换行：在当前段落后插入新的空段落
    const newParagraph = state.schema.nodes.paragraph.create(
      { dbParagraphId: null, templateId: null }, // 新段落属性为空，可编辑
      []
    )

    const insertPos = paragraphPos + paragraphNode.nodeSize
    tr.insert(insertPos, newParagraph)

    // 将光标移动到新段落中
    const newCursorPos = insertPos + 1
    tr.setSelection(state.selection.constructor.near(tr.doc.resolve(newCursorPos)))
  }

  // 应用事务
  view.dispatch(tr)
  return true
}

// 找到包含指定位置的只读一级节点
function findReadonlyParagraphNode($pos, readonlyIds, currentTemplateId) {
  for (let depth = 0; depth <= $pos.depth; depth++) {
    const node = $pos.node(depth)
    const pos = $pos.start(depth) - 1 // 节点的起始位置

    // 检查所有可能的一级节点类型
    const isTopLevelNode = ['paragraph', 'heading', 'bulletList', 'orderedList', 'table', 'horizontalRule', 'pageBreak', 'tableOfContents'].includes(node.type.name)

    // 检查是否是一级节点且为只读
    if (isTopLevelNode) {
      // 检查节点是否有只读标记（父模板内容）
      if (node.attrs && node.attrs.isReadonly) {
        return { node, pos }
      }

      // 检查节点的数据库段落ID是否在只读列表中
      if (readonlyIds && readonlyIds.length > 0 && node.attrs && node.attrs.dbParagraphId && readonlyIds.includes(node.attrs.dbParagraphId)) {
        return { node, pos }
      }

      // 检查templateId是否与当前模板ID不同（使用 != 而不是 !== 来处理类型转换）
      if (readonlyIds && readonlyIds.length > 0 && currentTemplateId && node.attrs && node.attrs.templateId && node.attrs.templateId != currentTemplateId) {
        return { node, pos }
      }
    }
  }

  return null
}
