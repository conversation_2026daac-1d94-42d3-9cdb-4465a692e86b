import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import ImageNodeView from './ImageNodeView.vue'

/**
 * 增强的图片扩展 ImageExtension
 * 支持图片插入、尺寸调整、对齐方式、边框等功能
 */
export const ImageExtension = Node.create({
  name: 'image',
  group: 'inline',
  inline: true,
  atom: true,
  selectable: true,
  draggable: true,

  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: element => element.getAttribute('src'),
        renderHTML: attributes => ({
          src: attributes.src
        })
      },
      width: {
        default: null,
        parseHTML: element => element.getAttribute('data-width'),
        renderHTML: attributes => ({
          'data-width': attributes.width
        })
      },
      height: {
        default: null,
        parseHTML: element => element.getAttribute('data-height'),
        renderHTML: attributes => ({
          'data-height': attributes.height
        })
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'img[src]'
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['img', mergeAttributes(HTMLAttributes)]
  },

  addNodeView() {
    return VueNodeViewRenderer(ImageNodeView)
  },

  addCommands() {
    return {
      insertImage:
        options =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options
          })
        },
      updateImageAttributes:
        attributes =>
        ({ commands }) => {
          return commands.updateAttributes(this.name, attributes)
        }
    }
  }
})

export default ImageExtension
