<template>
  <!-- <div class="page-container"> -->
    <div>
    <!-- <a-page-header title="模板字段池" /> -->
     <a-card title="模版字段池">
    <!-- 搜索与操作区域 -->
    <!-- <a-card :bordered="false" class="search-card"> -->
      <div class="flex justify-between mb-[16px]">
        <a-form layout="inline" :model="searchForm" class="gap-y-[10px]">
          <a-form-item label="字段名称">
            <a-input v-model:value="searchForm.field_name" placeholder="请输入字段名称" @pressEnter="handleSearch" />
          </a-form-item>
          <a-form-item label="字段键">
            <a-input v-model:value="searchForm.field_key" placeholder="请输入字段键" @pressEnter="handleSearch" />
          </a-form-item>
          <a-form-item label="字段类型">
            <a-select v-model:value="searchForm.field_type" placeholder="请选择字段类型" style="width: 180px" allow-clear @change="handleSearch">
              <a-select-option v-for="item in fieldTypeOptions" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
            <a-button style="margin-left: 8px" @click="resetSearchForm">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-form-item>
        </a-form>

        <a-button type="primary" @click="handleAddNew">
          <template #icon><PlusOutlined /></template>
          新建字段
        </a-button>
      </div>

      <a-table row-key="id" :columns="columns" :data-source="fields" :pagination="paginationConfig" :loading="isLoading" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'field_type'">
            {{ formatFieldType(record.field_type) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="handleEdit(record)">编辑</a-button>
              <a-popconfirm title="确定删除此模板字段吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record.id)">
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    <!-- </a-card> -->
    </a-card>
    </div>
  <!-- </div> -->
</template>

<script setup>
import { ref, reactive, computed, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue'
import api from '@/api'

const router = useRouter()

// 预定义的字段类型，用于搜索下拉和格式化显示
// 您可以根据实际需求调整这里的选项，或者从后端动态获取
const fieldTypeOptions = ref([
  { value: 'custom', label: '自定义字段' },
  { value: 'default', label: '系统默认字段' },
  { value: 'system_reserved', label: '系统预留字段' }
  // { value: 'other_type', label: '其他类型' }, // 示例：添加更多类型
])

// 表格列定义
const columns = [
  { title: '字段名称', dataIndex: 'field_name', key: 'field_name', width: 150 },
  { title: '字段键', dataIndex: 'field_key', key: 'field_key', width: 150 },
  { title: '字段类型', dataIndex: 'field_type', key: 'field_type', width: 150 }, // 将由 formatFieldType 格式化
  { title: '备注', dataIndex: 'remark', key: 'remark', ellipsis: true }, // ellipsis: true 用于内容过长时显示省略号
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at', width: '180px' },
  { title: '更新时间', dataIndex: 'updated_at', key: 'updated_at', width: '180px' },
  { title: '操作', key: 'action', width: '150px', fixed: 'right' }
]

// 搜索表单响应式状态
const searchForm = reactive({
  field_name: '',
  field_key: '',
  field_type: undefined // 对应 a-select 的 allow-clear
})

// 表格数据和分页状态
const fields = ref([])
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})
const isLoading = ref(false) // 控制表格加载状态

// 计算属性：分页配置
const paginationConfig = computed(() => ({
  total: pagination.value.total,
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50', '100'], // 可选每页条数
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`
}))

// 加载模板字段数据
const loadFields = async (params = {}) => {
  isLoading.value = true
  const queryParams = {
    page: params.page || pagination.value.current,
    pageSize: params.pageSize || pagination.value.pageSize,
    // 只有当搜索表单中的值非空时才作为查询参数
    field_name: searchForm.field_name || undefined,
    field_key: searchForm.field_key || undefined,
    field_type: searchForm.field_type === null || searchForm.field_type === '' ? undefined : searchForm.field_type
  }

  try {
    // 使用新的HTTP API对象结构
    const response = await api.templateField.list(queryParams)
    if (response && response.success && response.data) {
      fields.value = response.data.list
      pagination.value = {
        ...pagination.value, // 保留其他可能的分页状态
        current: Number(response.data.pagination.page),
        pageSize: Number(response.data.pagination.pageSize),
        total: Number(response.data.pagination.total)
      }
    } else {
      message.error(response.message || '获取模板字段列表失败')
      fields.value = [] // 清空列表
      pagination.value.total = 0 // 重置总数
    }
  } catch (error) {
    console.error('获取模板字段列表时发生错误:', error)
    message.error('获取模板字段列表时发生错误: ' + (error.message || '未知错误'))
    fields.value = []
    pagination.value.total = 0
  } finally {
    isLoading.value = false
  }
}

// 格式化字段类型用于显示
const formatFieldType = typeValue => {
  if (typeValue === null || typeof typeValue === 'undefined') return '未指定'
  const typeOption = fieldTypeOptions.value.find(opt => opt.value === typeValue)
  return typeOption ? typeOption.label : String(typeValue) // 如果找不到匹配项，直接显示原始值
}

// 处理搜索
const handleSearch = () => {
  pagination.value.current = 1 // 搜索时重置到第一页
  loadFields()
}

// 重置搜索表单并重新加载数据
const resetSearchForm = () => {
  searchForm.field_name = ''
  searchForm.field_key = ''
  searchForm.field_type = undefined
  pagination.value.current = 1
  loadFields()
}

// 处理表格变化（分页、排序、筛选）
const handleTableChange = (pageInfo, filters, sorter) => {
  pagination.value.current = pageInfo.current
  pagination.value.pageSize = pageInfo.pageSize
  // 注意：sorter 参数可以用于后端排序，目前是前端排序
  // 如果需要后端排序，需要将 sorter.field 和 sorter.order 传给 loadFields
  loadFields()
}

// 跳转到新建模板字段页面
const handleAddNew = () => {
  router.push({ name: 'SystemTemplateFieldCreate' }) // 确保路由名称正确
}

// 跳转到编辑模板字段页面
const handleEdit = record => {
  router.push({ name: 'SystemTemplateFieldEdit', params: { id: record.id } }) // 确保路由名称正确
}

// 处理删除操作
const handleDelete = async id => {
  isLoading.value = true // 也可以用一个独立的 deletingId ref 来控制特定行的加载状态
  try {
    // 使用新的HTTP API对象结构
    const response = await api.templateField.delete(id)
    if (response && response.success) {
      message.success(response.message || '删除成功！')
      // 删除成功后，重新加载当前页数据
      // 如果希望回到第一页，可以设置 pagination.value.current = 1;
      loadFields()
    } else {
      message.error(response.message || '删除失败')
    }
  } catch (err) {
    message.error(err.message || '删除模板字段时发生错误')
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时加载初始数据
onMounted(() => {
  loadFields()
})

// 如果页面使用了 <keep-alive>，并且需要在每次激活时重新加载数据，取消下面的注释
/*
onActivated(() => {
  console.log('TemplateFieldList activated, re-fetching data.');
  loadFields();
});
*/
</script>

<style scoped>
.page-container {
}
</style>
