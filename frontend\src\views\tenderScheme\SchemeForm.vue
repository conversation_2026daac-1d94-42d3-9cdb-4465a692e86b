<template>
  <a-spin :spinning="isLoading || isExportingDocx">
    <div class="page-header">
      <a-page-header :title="pageTitle" @back="() => $router.go(-1)" />
      <div class="flex gap-x-[10px]">
        <a-button type="primary" @click="handleSubmit" :loading="isSubmitting" :disabled="isExportingDocx">{{ isEditMode ? '保存修改' : '创建方案' }}</a-button>
        <a-button @click="handleExportToDocx" :loading="isExportingDocx" :disabled="!showEditor || isLoading || isSubmitting || (!isEditMode && !formState.template_id)">
          导出为 DOCX
        </a-button>
        <a-button type="primary" @click="handleVersionHistory" :disabled="!isEditMode">版本历史</a-button>
      </div>
    </div>
    <div class="flex flex-1">
      <a-card :loading="isLoading" style="width: 100%" :bodyStyle="{ height: 'calc(100vh - 180px)', overflow: 'auto' }">
        <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" @finish="handleSubmit">
          <a-form-item label="方案名称" name="name">
            <a-input v-model:value="formState.name" placeholder="请输入方案名称" />
          </a-form-item>

          <a-form-item label="选择模板" name="template_id">
            <a-select
              v-model:value="formState.template_id"
              placeholder="请选择一个模板"
              :loading="isTemplatesLoading"
              :disabled="isEditMode"
              @change="handleTemplateChange"
              show-search
              option-filter-prop="children"
              :filter-option="(input, option) => option.children[0].children.toLowerCase().indexOf(input.toLowerCase()) >= 0"
            >
              <a-select-option v-for="template in templatesList" :key="template.id" :value="template.id">
                {{ template.name }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="3" />
          </a-form-item>

          <a-form-item label="方案内容" name="content">
            <div class="editor-risk-container">
              <div class="editor-container-wrapper">
                <TiptapEditor
                  v-if="showEditor"
                  ref="editorRef"
                  v-model="editorContent"
                  :min-height="'400px'"
                  :editable="true"
                  :fill-mode="isFillMode"
                  :show-toolbar="!isFillMode"
                  :risk-data="riskAssessmentData?.field_risks || []"
                />
                <div v-else class="editor-placeholder-wrapper">{{ editorPlaceholderText }}</div>
              </div>
            </div>
          </a-form-item>
        </a-form>
      </a-card>
      <!-- 风险面板 -->
      <RiskPanel
        v-if="isEditMode && showEditor"
        :risk-assessment-data="riskAssessmentData"
        :loading="riskAssessmentLoading"
        @scroll-to-field="handleScrollToField"
        @highlight-field="handleHighlightField"
        @refresh-assessment="handleRefreshRiskAssessment"
        @start-assessment="handleStartRiskAssessment"
      />
    </div>

    <!-- 版本历史组件 -->
    <VersionHistory
      v-model:visible="versionHistoryVisible"
      :history-versions="historyVersions"
      :template-content="currentTemplateContent"
      :current-field-values="formState.content"
    />

    <!-- 更新备注对话框 -->
    <a-modal
      v-model:open="updateRemarkModalVisible"
      title="填写更新备注"
      :confirm-loading="isSubmitting"
      @ok="handleUpdateRemarkSubmit"
      @cancel="handleUpdateRemarkCancel"
      :mask-closable="false"
      width="500px"
      ok-text="确认保存"
      cancel-text="取消"
    >
      <div style="margin-bottom: 16px">
        <a-alert message="检测到方案内容已发生变化" description="请填写本次更新的备注信息，说明具体修改了什么内容。" type="info" show-icon style="margin-bottom: 16px" />
      </div>
      <a-form ref="updateRemarkFormRef" :model="updateRemarkForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item label="更新备注" name="remark" :rules="[{ required: true, message: '请填写更新备注', trigger: 'blur' }]">
          <a-textarea
            v-model:value="updateRemarkForm.remark"
            placeholder="请详细说明本次修改的内容，方便后续查看历史版本时了解改动情况"
            :rows="4"
            :maxlength="500"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-spin>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import TiptapEditor from '@/components/TiptapEditor/index.vue'
import VersionHistory from '@/components/VersionHistory.vue'
import RiskPanel from '@/components/TiptapEditor/RiskPanel.vue'
import api from '@/api'
import webSocketService from '@/utils/webSocketService'
import { exportTiptapContentToDocx } from '@/utils/docxExporter'
import { mergeTemplateWithFieldValues, extractFieldValuesFromTiptapJSON, sanitizeTextNodesInTiptapJSON, getDefaultTiptapDoc } from '@/utils/templateUtils'

const route = useRoute()
const router = useRouter()

const formRef = ref()
const isLoading = ref(false)
const isSubmitting = ref(false)
const isTemplatesLoading = ref(false)
const isExportingDocx = ref(false)
const versionHistoryVisible = ref(false)
const historyVersions = ref([])

const schemeId = ref(route.params.id || null)
const isEditMode = computed(() => !!schemeId.value)
const pageTitle = computed(() => (isEditMode.value ? '编辑招标方案' : '新建招标方案'))

const formState = reactive({
  name: '',
  template_id: null,
  remark: '',
  content: {} // 现在 content 存储的是字段键值对对象 {}
})

// 存储原始内容，用于检查是否有变化
const originalContent = ref({})
const contentChanged = ref(false)

// 更新备注对话框相关
const updateRemarkModalVisible = ref(false)
const updateRemarkFormRef = ref()
const updateRemarkForm = reactive({
  remark: ''
})
const pendingUpdateData = ref(null) // 存储待提交的数据

const showEditor = ref(false)
// isFillMode 应该由 TiptapEditor 内部根据 editable 和其他属性决定是否只读模板、可写字段
// 或者我们可以将其传递给 TiptapEditor，并让其影响其内部行为
// 此处 isFillMode 主要控制 toolbar 的显隐和整体编辑器的行为模式
const isFillMode = ref(true) // 在方案表单，我们总是处于"填充"模式，模板结构只读

const templatesList = ref([])

const rules = {
  name: [{ required: true, message: '请输入方案名称', trigger: 'blur' }],
  template_id: [{ required: true, message: '请选择一个模板', trigger: 'change' }]
}

const editorPlaceholderText = computed(() => {
  if (isLoading.value && !isEditMode.value && !formState.template_id) return '请先选择一个模板...'
  if (isLoading.value) return '正在加载内容...'
  if (isEditMode.value && !showEditor.value) return '正在加载方案内容...'
  if (!formState.template_id && !isEditMode.value) return '请先选择一个模板'
  if (!showEditor.value && formState.template_id) return '模板已选择，正在准备编辑器...'
  return '编辑器加载中或模板内容为空'
})

const editorContent = ref(getDefaultTiptapDoc())
const currentTemplateContent = ref(null)
const editorRef = ref(null)

// 风险评估相关状态
const riskAssessmentData = ref(null)
const riskAssessmentLoading = ref(false)
const currentRiskAssessmentId = ref(null) // 存储当前方案的风险评估ID
// 风险提示相关状态已移至 TiptapEditor 组件内部

// 检查内容是否有变化的函数
const checkContentChanged = currentFieldValues => {
  if (!isEditMode.value) {
    return false
  }

  const originalContentStr = JSON.stringify(originalContent.value || {})
  const currentContentStr = JSON.stringify(currentFieldValues || {})

  return originalContentStr !== currentContentStr
}

// 获取版本历史数据
const handleVersionHistory = async () => {
  if (!isEditMode.value || !schemeId.value) {
    message.warning('请在编辑现有方案时查看版本历史')
    return
  }

  try {
    const response = await api.tenderScheme.getHistory({ scheme_id: schemeId.value })
    if (response && response.success) {
      if (response.data.length === 0) {
        message.info('当前方案暂无历史版本')
        return
      }
      historyVersions.value = response.data
      versionHistoryVisible.value = true
    } else {
      message.error(response.message || '获取历史版本失败')
    }
  } catch (error) {
    console.error('获取版本历史失败:', error)
    message.error('获取版本历史时发生错误')
  }
}

const fetchTemplates = async () => {
  isTemplatesLoading.value = true
  try {
    const response = await api.tenderSchemeTemplate.list({ page: 1, pageSize: 1000 })
    if (response && response.success && response.data && response.data.list) {
      templatesList.value = response.data.list
    } else {
      message.error(response.message || '获取模板列表失败')
      templatesList.value = []
    }
  } catch (error) {
    console.error('Error fetching templates:', error)
    message.error('获取模板列表时发生错误')
    templatesList.value = []
  } finally {
    isTemplatesLoading.value = false
  }
}

// 当选择的模板变化时 (仅在创建模式下)
const handleTemplateChange = async selectedTemplateId => {
  if (isEditMode.value || !selectedTemplateId) {
    formState.template_id = selectedTemplateId // 更新 template_id
    // 如果取消选择或在编辑模式下不应清空或重置内容，除非明确操作
    // editorContent.value = getDefaultTiptapDoc();
    // formState.content = {};
    // currentTemplateContent.value = null;
    // showEditor.value = false;
    return
  }

  isLoading.value = true
  showEditor.value = false // 先隐藏编辑器，直到内容准备好
  formState.content = {} // 重置字段值
  currentTemplateContent.value = null

  try {
    const templateResponse = await api.tenderSchemeTemplate.getById({ id: selectedTemplateId })
    if (templateResponse.success && templateResponse.data) {
      let rawTemplateContent = templateResponse.data.content
      if (typeof rawTemplateContent === 'string') {
        try {
          rawTemplateContent = JSON.parse(rawTemplateContent)
        } catch (e) {
          message.error('模板内容JSON解析失败。')
          editorContent.value = getDefaultTiptapDoc() // 设置为默认空文档
          currentTemplateContent.value = getDefaultTiptapDoc()
          isLoading.value = false
          return
        }
      }

      if (typeof rawTemplateContent === 'object' && rawTemplateContent.type === 'doc') {
        sanitizeTextNodesInTiptapJSON(rawTemplateContent) // 清理的是原始模板
        currentTemplateContent.value = rawTemplateContent

        formState.content = {}
        let mergedContent = mergeTemplateWithFieldValues(currentTemplateContent.value, formState.content)
        sanitizeTextNodesInTiptapJSON(mergedContent) // <--- 在此再次清理 merge 的结果
        editorContent.value = mergedContent

        showEditor.value = true
        isFillMode.value = true // 确定是填充模式
      } else {
        message.error('模板内容格式不正确。')
        editorContent.value = getDefaultTiptapDoc()
        currentTemplateContent.value = getDefaultTiptapDoc()
      }
    } else {
      message.error(templateResponse.message || '获取模板内容失败。')
      editorContent.value = getDefaultTiptapDoc()
      currentTemplateContent.value = getDefaultTiptapDoc()
    }
  } catch (error) {
    console.error('Error handling template change:', error)
    message.error('加载模板内容时发生错误。')
    editorContent.value = getDefaultTiptapDoc()
    currentTemplateContent.value = getDefaultTiptapDoc()
  } finally {
    isLoading.value = false
  }
}

const fetchSchemeData = async id => {
  if (!id) return
  isLoading.value = true
  showEditor.value = false // 先隐藏编辑器
  currentTemplateContent.value = null // 重置

  try {
    // 使用新的HTTP API对象结构
    const schemeResponse = await api.tenderScheme.getById({ id })
    if (schemeResponse && schemeResponse.success && schemeResponse.data) {
      const scheme = schemeResponse.data
      formState.name = scheme.name
      formState.template_id = scheme.template_id
      formState.remark = scheme.remark

      // 保存风险评估ID（如果存在）
      currentRiskAssessmentId.value = scheme.risk_assessment_id || null

      // scheme.content 应该是字段值对象
      let schemeFieldValues = scheme.content // 后端应该返回 {} 或 { key: "value"}
      if (typeof schemeFieldValues === 'string') {
        try {
          schemeFieldValues = JSON.parse(schemeFieldValues)
        } catch (e) {
          message.error('方案的字段值JSON解析失败，将视为空值。')
          schemeFieldValues = {}
        }
      }
      if (typeof schemeFieldValues !== 'object' || schemeFieldValues === null) {
        message.warn('方案的字段值不是一个有效的对象，将视为空值。')
        schemeFieldValues = {}
      }
      formState.content = schemeFieldValues // 存储字段值对象

      // 现在获取关联的模板内容
      if (formState.template_id) {
        const templateResponse = await api.tenderSchemeTemplate.getById({ id: formState.template_id })
        if (templateResponse.success && templateResponse.data) {
          let rawTemplateContent = templateResponse.data.content
          if (typeof rawTemplateContent === 'string') {
            try {
              rawTemplateContent = JSON.parse(rawTemplateContent)
            } catch (e) {
              message.error('关联模板的JSON解析失败。')
              editorContent.value = getDefaultTiptapDoc() // 设置为默认空文档
              isLoading.value = false
              return
            }
          }

          if (typeof rawTemplateContent === 'object' && rawTemplateContent.type === 'doc') {
            sanitizeTextNodesInTiptapJSON(rawTemplateContent) // 清理的是原始模板
            currentTemplateContent.value = rawTemplateContent
            let mergedContent = mergeTemplateWithFieldValues(currentTemplateContent.value, formState.content)
            sanitizeTextNodesInTiptapJSON(mergedContent) // <--- 在此再次清理 merge 的结果
            editorContent.value = mergedContent
            console.log(editorContent.value)

            showEditor.value = true
            isFillMode.value = true // 编辑模式也是填充模式

            // 在编辑模式下保存原始内容用于比较
            if (isEditMode.value) {
              originalContent.value = JSON.parse(JSON.stringify(formState.content))
            }
          } else {
            message.error('关联模板内容格式不正确。')
            editorContent.value = getDefaultTiptapDoc()
          }
        } else {
          message.error(templateResponse.message || '获取关联模板内容失败。')
          // 即使模板加载失败，也尝试显示一个空的编辑器，并保留已加载的字段值（如果有）
          editorContent.value = mergeTemplateWithFieldValues(getDefaultTiptapDoc(), formState.content) // 使用空模板结构
          showEditor.value = true

          // 在编辑模式下保存原始内容用于比较
          if (isEditMode.value) {
            originalContent.value = JSON.parse(JSON.stringify(formState.content))
          }
        }
      } else {
        message.error('方案缺少关联模板ID，无法加载编辑器内容。')
        // formState.content 已经是 schemeFieldValues，这里不需要再做什么特殊处理
        // 可以考虑显示一个提示，或者一个不带模板的非常基础的编辑器（如果需要支持无模板方案）
        editorContent.value = getDefaultTiptapDoc() // 显示空编辑器
        showEditor.value = true // 或者 false，并显示错误信息

        // 在编辑模式下保存原始内容用于比较
        if (isEditMode.value) {
          originalContent.value = JSON.parse(JSON.stringify(formState.content))
        }
      }
    } else {
      message.error(schemeResponse.message || '获取方案详情失败')
      router.push({ name: 'TenderSchemeProjectList' })
    }
  } catch (error) {
    console.error('Error fetching scheme data:', error)
    message.error('获取方案详情时发生严重错误。')
    editorContent.value = getDefaultTiptapDoc() // 发生错误时显示空编辑器
    showEditor.value = true

    // 在编辑模式下保存原始内容用于比较，即使发生错误
    if (isEditMode.value) {
      originalContent.value = JSON.parse(JSON.stringify(formState.content || {}))
    }
  } finally {
    isLoading.value = false
  }
}

// 实际提交更新的函数
const submitUpdate = async payload => {
  try {
    isSubmitting.value = true

    if (isEditMode.value) {
      if (!schemeId.value) {
        message.error('无法更新方案：缺少方案ID。')
        isSubmitting.value = false
        return
      }
      // payload.id = schemeId.value; // id 是通过URL参数传递给API的，不需要在body中
      const updateResponse = await api.tenderScheme.update({ id: Number(schemeId.value), ...payload })
      if (updateResponse && updateResponse.success) {
        message.success(updateResponse.message || '招标方案更新成功！')
        // 重置内容变化相关状态
        contentChanged.value = false
        updateRemarkForm.remark = ''
        pendingUpdateData.value = null
        // 可选：更新后重新获取数据或跳转
        fetchSchemeData(schemeId.value) // 重新加载以显示可能由后端处理过的数据
      } else {
        message.error(updateResponse.message || '更新招标方案失败')
      }
    } else {
      // 创建新方案
      const createResponse = await api.tenderScheme.create(payload)
      if (createResponse && createResponse.success && createResponse.data) {
        message.success(createResponse.message || '招标方案创建成功！')
        router.push({ name: 'TenderSchemeProjectList' }) // 创建成功后跳转到列表页
      } else {
        message.error(createResponse.message || '创建招标方案失败')
      }
    }
  } catch (error) {
    console.error('Error submitting update:', error)
    message.error('提交更新时发生错误。')
  } finally {
    isSubmitting.value = false
  }
}

// 处理更新备注对话框确认
const handleUpdateRemarkSubmit = async () => {
  try {
    // 先进行表单验证
    await updateRemarkFormRef.value.validate()

    if (!pendingUpdateData.value) {
      message.error('没有待提交的数据')
      return
    }

    // 添加更新备注到待提交数据
    const finalPayload = {
      ...pendingUpdateData.value,
      update_remark: updateRemarkForm.remark.trim()
    }

    updateRemarkModalVisible.value = false
    await submitUpdate(finalPayload)
  } catch (error) {
    // 表单验证失败，不执行提交
    console.log('更新备注表单验证失败:', error)
  }
}

// 处理更新备注对话框取消
const handleUpdateRemarkCancel = () => {
  updateRemarkModalVisible.value = false
  updateRemarkForm.remark = ''
  pendingUpdateData.value = null
  isSubmitting.value = false
  // 重置表单验证状态
  if (updateRemarkFormRef.value) {
    updateRemarkFormRef.value.resetFields()
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 从 editorContent (TiptapEditor的v-model) 提取最新的字段值
    const currentFieldValues = extractFieldValuesFromTiptapJSON(editorContent.value)

    // 检查内容是否有变化
    const hasContentChanged = checkContentChanged(currentFieldValues)
    contentChanged.value = hasContentChanged

    const payload = {
      name: formState.name,
      template_id: formState.template_id,
      remark: formState.remark,
      content: currentFieldValues // 发送提取出的字段值对象
    }

    // 如果是编辑模式且内容有变化，弹出更新备注对话框
    if (isEditMode.value && hasContentChanged) {
      pendingUpdateData.value = payload
      updateRemarkForm.remark = '' // 清空之前的输入
      updateRemarkModalVisible.value = true
      return
    }

    // 如果没有内容变化，直接提交
    await submitUpdate(payload)
  } catch (errorInfo) {
    // Ant Design form validation error
    if (errorInfo && errorInfo.errorFields && errorInfo.errorFields.length > 0) {
      console.log('Form validation failed:', errorInfo)
      // message.error('请检查表单输入项。'); // formRef.validate() 应该已经显示了错误
    } else {
      console.error('Error submitting form:', errorInfo)
      message.error('提交表单时发生错误。')
    }
  } finally {
    isSubmitting.value = false
  }
}

const handleExportToDocx = async () => {
  if (!showEditor.value || !editorContent.value || !editorContent.value.content || editorContent.value.content.length === 0) {
    message.warning('编辑器内容未加载或为空，无法导出。')
    return
  }
  if (isLoading.value || isSubmitting.value) {
    message.warning('请等待当前操作完成后再导出。')
    return
  }

  const schemeName = formState.name || '招标方案文档'
  isExportingDocx.value = true
  message.loading({
    content: '正在生成 DOCX 文件...',
    key: 'exportDocxMessage',
    duration: 0
  })

  try {
    const currentEditorJsonToExport = JSON.parse(JSON.stringify(editorContent.value))
    sanitizeTextNodesInTiptapJSON(currentEditorJsonToExport)

    await exportTiptapContentToDocx(currentEditorJsonToExport, `${schemeName}.docx`)
    message.success({
      content: 'DOCX 文件导出成功！',
      key: 'exportDocxMessage',
      duration: 3
    })
  } catch (error) {
    console.error('导出 DOCX 文件失败:', error)
    message.error({
      content: `导出 DOCX 文件失败: ${error.message || '未知错误'}`,
      key: 'exportDocxMessage',
      duration: 4
    })
  } finally {
    isExportingDocx.value = false
  }
}

// 风险评估相关方法
const handleStartRiskAssessment = async () => {
  if (!isEditMode.value || !schemeId.value) {
    message.warning('请在编辑现有方案时进行风险评估')
    return
  }

  try {
    riskAssessmentLoading.value = true

    // 清空当前风险评估数据，因为新的评估正在进行
    riskAssessmentData.value = null
    currentRiskAssessmentId.value = null

    const response = await api.riskAssessment.startAssessment({ document_type: 'tender_scheme', document_id: schemeId.value })

    if (response?.success) {
      message.success('风险评估已启动，评估完成后将自动显示结果')
      // 注意：这里不需要设置 riskAssessmentLoading.value = false，
      // 保持loading状态直到评估完成或失败
    } else {
      message.error(response?.message || '启动风险评估失败')
      riskAssessmentLoading.value = false
    }
  } catch (error) {
    console.error('启动风险评估失败:', error)
    message.error('启动风险评估时发生错误: ' + (error.message || '未知错误'))
    riskAssessmentLoading.value = false
  }
}

const handleRefreshRiskAssessment = async () => {
  await loadRiskAssessmentData()
}

const loadRiskAssessmentData = async () => {
  if (!isEditMode.value || !currentRiskAssessmentId.value) {
    riskAssessmentData.value = null
    return
  }

  try {
    riskAssessmentLoading.value = true

    // 直接使用风险评估ID获取评估信息
    const response = await api.riskAssessment.getAssessmentInfo({ risk_assessment_id: currentRiskAssessmentId.value })

    if (response?.success && response.data) {
      const assessment = response.data

      // 检查评估状态是否为完成
      if (['low', 'medium', 'high'].includes(assessment.status) && assessment.ai_result) {
        // 解析AI结果（如果是字符串格式）
        let aiResult = assessment.ai_result
        if (typeof aiResult === 'string') {
          try {
            aiResult = JSON.parse(aiResult)
          } catch (e) {
            console.error('解析AI评估结果失败:', e)
            riskAssessmentData.value = null
            return
          }
        }

        riskAssessmentData.value = aiResult

        // 更新编辑器的风险高亮
        if (editorRef.value && aiResult?.field_risks) {
          nextTick(() => {
            editorRef.value.updateRiskHighlights(aiResult.field_risks)
          })
        }
      } else {
        // 评估未完成或无结果
        riskAssessmentData.value = null
      }
    } else {
      riskAssessmentData.value = null
    }
  } catch (error) {
    console.error('加载风险评估数据失败:', error)
    riskAssessmentData.value = null
  } finally {
    riskAssessmentLoading.value = false
  }
}

const handleScrollToField = risk => {
  // 滚动到指定字段
  if (editorRef.value) {
    editorRef.value.scrollToField(risk.field_key)
  }
}

const handleHighlightField = (risk, highlight) => {
  // 临时高亮字段
  if (editorRef.value) {
    editorRef.value.highlightField(risk.field_key, highlight)
  }
}

// WebSocket事件监听
const setupRiskAssessmentListeners = () => {
  const handleRiskAssessmentCompleted = data => {
    const { documentType, documentId, taskId } = data
    if (documentType === 'tender_scheme' && documentId === Number(schemeId.value)) {
      message.success('风险评估已完成！')

      // 更新当前方案的风险评估ID
      currentRiskAssessmentId.value = taskId

      // 结束loading状态
      riskAssessmentLoading.value = false

      // 重新加载风险评估数据
      loadRiskAssessmentData()
    }
  }

  const handleRiskAssessmentFailed = data => {
    const { documentType, documentId } = data
    if (documentType === 'tender_scheme' && documentId === Number(schemeId.value)) {
      message.error('风险评估失败：' + data.error)
      riskAssessmentLoading.value = false
    }
  }

  webSocketService.on('risk_assessment_completed', handleRiskAssessmentCompleted)
  webSocketService.on('risk_assessment_failed', handleRiskAssessmentFailed)

  // 返回清理函数
  return () => {
    webSocketService.off('risk_assessment_completed', handleRiskAssessmentCompleted)
    webSocketService.off('risk_assessment_failed', handleRiskAssessmentFailed)
  }
}

onMounted(() => {
  fetchTemplates() // 获取模板列表用于下拉选择

  // 设置风险评估WebSocket监听器
  const cleanupRiskListeners = setupRiskAssessmentListeners()

  if (isEditMode.value && schemeId.value) {
    fetchSchemeData(schemeId.value).then(() => {
      // 方案数据加载完成后，加载风险评估数据
      loadRiskAssessmentData()
    })
  } else {
    // 新建模式
    showEditor.value = false // 初始不显示编辑器，等待模板选择
    isFillMode.value = true // 新建也是填充模式
    formState.content = {} // 初始字段值为空对象
    editorContent.value = getDefaultTiptapDoc() // 编辑器初始为空文档
    // 用户选择模板后，handleTemplateChange会加载模板并更新editorContent
  }

  // 组件卸载时清理事件监听器
  onUnmounted(() => {
    cleanupRiskListeners()
  })
})

// 监听 template_id 的变化，以便在新建模式下，如果用户通过某种方式（例如浏览器后退再前进）
// 恢复了 template_id 但编辑器未加载，则尝试加载。
watch(
  () => formState.template_id,
  (newTemplateId, oldTemplateId) => {
    if (!isEditMode.value && newTemplateId && newTemplateId !== oldTemplateId) {
      // 只有当编辑器尚未显示，或者当前模板内容与新选的模板不符时才重新加载
      // （更复杂的逻辑可以判断 currentTemplateContent.value 是否与新 template_id 对应）
      // 为简化，如果 template_id 变了就调用 handleTemplateChange
      // handleTemplateChange 内部有isLoading等控制，可以防止重复加载
      handleTemplateChange(newTemplateId)
    }
  }
)

// 监听编辑器内容变化，实时检查是否有内容修改
watch(
  () => editorContent.value,
  () => {
    if (isEditMode.value && showEditor.value && originalContent.value) {
      // 提取当前字段值并检查是否有变化
      const currentFieldValues = extractFieldValuesFromTiptapJSON(editorContent.value)
      const hasChanged = checkContentChanged(currentFieldValues)
      contentChanged.value = hasChanged
    }
  },
  { deep: true }
)
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-risk-container {
  display: flex;
  gap: 0;
  min-height: 400px;
}

.editor-container-wrapper {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  min-height: 400px;
  background-color: #fff;
}

.editor-placeholder-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 20px;
  color: #999;
  background-color: #f9f9f9;
  border: 1px dashed #ccc;
  border-radius: 2px;
  text-align: center;
}
</style>
