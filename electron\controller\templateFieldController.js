'use strict'

const TemplateFieldService = require('../service/templateFieldService')

/**
 * 模板字段池控制器
 * @class
 */
class TemplateFieldController {
  constructor(ctx) {
    this.templateFieldService = new TemplateFieldService()
  }

  /**
   * 创建模板字段
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async create(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body
    const { field_name, field_key, field_type, remark } = params

    if (!field_name || !field_key) {
      return { success: false, message: '字段名称和字段键名不能为空' }
    }

    try {
      const result = await this.templateFieldService.create({
        field_name,
        field_key,
        field_type,
        remark
      })
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取模板字段列表
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async list(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {}

    try {
      const result = await this.templateFieldService.list(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取单个模板字段
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async get(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的字段ID' }
    }

    try {
      const result = await this.templateFieldService.getById(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 更新模板字段
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async update(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    const { id, ...updateData } = params
    const fieldId = parseInt(id)

    if (isNaN(fieldId)) {
      return { success: false, message: '无效的字段ID' }
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return { success: false, message: '没有提供需要更新的数据' }
    }

    try {
      const result = await this.templateFieldService.update(fieldId, updateData)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 删除模板字段
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async delete(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的字段ID' }
    }

    try {
      const result = await this.templateFieldService.delete(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
}

TemplateFieldController.toString = () => '[class TemplateFieldController]'

module.exports = TemplateFieldController
