{"name": "ee", "version": "4.0.0", "scripts": {"dev": "vite --host --port 8080", "serve": "vite --host --port 8080", "build-staging": "vite build --mode staging", "build": "vite build", "preview": "vite preview"}, "type": "module", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@tiptap/extension-bubble-menu": "^2.23.1", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-font-family": "^2.14.0", "@tiptap/extension-highlight": "^2.23.1", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/extension-table": "^2.12.0", "@tiptap/extension-table-cell": "^2.12.0", "@tiptap/extension-table-header": "^2.12.0", "@tiptap/extension-table-row": "^2.12.0", "@tiptap/extension-text-align": "^2.12.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-underline": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@tiptap/vue-3": "^2.12.0", "@types/markdown-it": "^14.1.2", "ant-design-vue": "^4.2.6", "ant-design-x-vue": "^1.2.4", "axios": "^0.21.1", "docx": "^9.5.0", "docx-preview": "^0.3.5", "markdown-it": "^14.1.0", "pinia": "^2.2.6", "socket.io-client": "^4.4.1", "store2": "^2.13.2", "vue": "^3.5.12", "vue-router": "^4.0.14", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "@vue/compiler-sfc": "^3.2.33", "less": "^4.1.2", "less-loader": "^10.2.0", "postcss": "^8.4.13", "postcss-pxtorem": "^6.0.0", "terser": "^5.19.1", "unocss": "^66.1.3", "vite": "^5.4.11", "vite-plugin-compression": "^0.5.1"}}