export const arr = [
    {
        path: '/',
        name:'routers',
        component: "layouts/AppSider", 
        // meta: { requiresAuth: true }, // 主布局下的所有页面默认需要认证
        // redirect: '/tender-scheme', // 默认重定向到某个页面，例如 /dashboard 或 /framework
        children: [
          // --- 招标方案管理 --- 
          {
            path: '/tender-scheme',
            name: 'TenderSchemeModule', 
            component: 'layouts/Menu',
            props: { id: 'TenderSchemeModule' }, 
            // redirect: { name: 'TenderSchemeTemplateList' }, // 默认重定向到模板列表
            meta: { requiresAuth: true, title: '招标方案', antIcon: 'SolutionOutlined' }, 
            children: [
              {
                path: 'templates',
                name: 'TenderSchemeTemplateList',
                component: 'tenderScheme/TemplateList',
                meta: { title: '模板管理', antIcon: 'FileMarkdownOutlined' },
              },
              {
                path: 'templates/create',
                name: 'TenderSchemeTemplateCreate',
                component: 'tenderScheme/TemplateForm',
                meta: { title: '新建模板', parentPath: '/tender-scheme/templates', showInMenu: false, breadcrumb: [{ name: 'TenderSchemeTemplateList', title: '模版管理' }] },
              },
              {
                path: 'templates/edit/:id',
                name: 'TenderSchemeTemplateEdit',
                component: 'tenderScheme/TemplateForm',
                meta: { title: '编辑模板', parentPath: '/tender-scheme/templates', showInMenu: false, breadcrumb: [{ name: 'TenderSchemeTemplateList', title: '模版管理' }]  },
              },
              {
                path: 'projects',
                name: 'TenderSchemeProjectList',
                component: 'tenderScheme/SchemeList', 
                meta: { title: '方案制作', antIcon:'FileImageOutlined' }, 
              },
              {
                path: 'projects/create',
                name: 'TenderSchemeProjectCreate',
                component: 'tenderScheme/SchemeForm', 
                meta: { title: '新建方案', parentPath: '/tender-scheme/projects', showInMenu: false, breadcrumb: [{ name: 'TenderSchemeProjectList', title: '方案制作' }]  },
              },
              {
                path: 'projects/edit/:id',
                name: 'TenderSchemeProjectEdit',
                component: 'tenderScheme/SchemeForm', 
                meta: { title: '编辑方案', parentPath: '/tender-scheme/projects', showInMenu: false, breadcrumb: [{ name: 'TenderSchemeProjectList', title: '方案制作' }]  },
              },
              {
                path: 'projects/view/:id',
                name: 'TenderSchemeProjectView',
                component: 'tenderScheme/SchemeView',
                props: true,
                meta: { title: '查看方案', requiresAuth: true, showInMenu: false, breadcrumb: [{ name: 'TenderSchemeProjectList', title: '方案制作' }, { title: '查看' }] }
              }
            ]
          },
          // --- 招标文件管理 ---
          {
            path: '/bidding-document',
            name: 'BiddingDocumentModule', 
            component: 'layouts/Menu',
            props: { id: 'BiddingDocumentModule' },
            // redirect: { name: 'BiddingDocumentTemplateList' }, 
            // redirect: '/bidding-document/bidtemplates', 
            meta: { requiresAuth: true, title: '招标文件', antIcon: 'FileWordOutlined' },
            children: [
              {
                // path: 'templates', 
                path: 'bidtemplates', 
                name: 'BiddingDocumentTemplateList',
                component: 'biddingDocument/TemplateList', 
                meta: { title: '模板管理', requiresAuth: true, keepAlive: true, antIcon: 'FileMarkdownOutlined'}
              },
              {
                // path: 'templates/new',
                path: 'bidtemplates/new',
                name: 'BiddingDocumentTemplateNew',
                component:'biddingDocument/TemplateForm', 
                // meta: {showInMenu: false, title: '新建模板', requiresAuth: true, breadcrumb: [{ name: 'BiddingDocumentTemplateList', title: '模板管理' }, { title: '新建' }] }
                meta: {showInMenu: false, title: '新建模板', requiresAuth: true, breadcrumb: [{ name: 'BiddingDocumentTemplateList', title: '模板管理' }] }
              },
              {
                // path: 'templates/edit/:id',
                path: 'bidtemplates/edit/:id',
                name: 'BiddingDocumentTemplateEdit',
                component:'biddingDocument/TemplateForm', 
                props: true,
                // meta: { title: '编辑模板', requiresAuth: true, showInMenu: false, breadcrumb: [{ name: 'BiddingDocumentTemplateList', title: '模板管理' }, { title: '编辑' }] }
                meta: { title: '编辑模板', requiresAuth: true, showInMenu: false, breadcrumb: [{ name: 'BiddingDocumentTemplateList', title: '模板管理' }] }
              },
              {
                path: 'files',
                name: 'BiddingDocumentFileList',
                component:'biddingDocument/FileList', 
                meta: { title: '文件管理', requiresAuth: true, keepAlive: true, antIcon: 'FolderOutlined' }
              },
              {
                path: 'files/new',
                name: 'BiddingDocumentFileNew',
                component:'biddingDocument/FileForm', 
                // meta: { showInMenu: false, title: '新建文件', showInMenu: false, requiresAuth: true, breadcrumb: [{ name: 'BiddingDocumentFileList', title: '文件管理' }, { title: '新建' }] }
                meta: { showInMenu: false, title: '新建文件', showInMenu: false, requiresAuth: true, breadcrumb: [{ name: 'BiddingDocumentFileList', title: '文件管理' }] }
              },
              {
                path: 'files/edit/:id',
                name: 'BiddingDocumentFileEdit',
                component:'biddingDocument/FileForm', 
                props: true,
                // meta: { title: '编辑文件', requiresAuth: true, showInMenu: false, breadcrumb: [{ name: 'BiddingDocumentFileList', title: '文件管理' }, { title: '编辑' }] }
                meta: { title: '编辑文件', requiresAuth: true, showInMenu: false, breadcrumb: [{ name: 'BiddingDocumentFileList', title: '文件管理' }] }
              },
              {
                path: 'files/view/:id',
                name: 'BiddingDocumentFileView',
                component:'biddingDocument/FileView', 
                props: true,
                // meta: { title: '查看文件', requiresAuth: true, showInMenu: false, breadcrumb: [{ name: 'BiddingDocumentFileList', title: '文件管理' }, { title: '查看' }] }
                meta: { title: '查看文件', requiresAuth: true, showInMenu: false, breadcrumb: [{ name: 'BiddingDocumentFileList', title: '文件管理' }] }
              }
            ]
          },
          // --- 系统管理 (用户管理、角色管理) 
          {
            path: '/system',
            name: 'SystemManagement',
            component: 'layouts/Menu',
            props: { id: 'SystemManagement' },
            // redirect: { name: 'SystemUsers' }, 
            meta: { requiresAuth: true, title: '系统管理', antIcon: 'SettingOutlined' }, // 确保使用 antIcon
            children: [
              {
                path: '/system/users',
                name: 'SystemUsers',
                component: "system/UserManagement",
                meta: { requiresAuth: true, title: '用户管理', roles: ['superadmin'], antIcon: 'UserOutlined' } // 假设只有superadmin能管理用户
              },
              {
                path: '/system/roles',
                name: 'SystemRoles',
                component: 'system/RoleManagement', 
                meta: { requiresAuth: true, title: '角色管理', roles: ['superadmin'], antIcon: 'TeamOutlined' } // 假设只有superadmin能管理角色
              },
              {
                path: 'template-fields', 
                name: 'SystemTemplateFieldList',
                component: 'system/TemplateFieldList',
                meta: { title: '模板字段管理', requiresAuth: true, keepAlive: true, antIcon: 'TableOutlined'}, // keepAlive 根据需要设置
              },
              {
                path: 'template-fields/create', 
                name: 'SystemTemplateFieldCreate',
                component: 'system/TemplateFieldForm',
                meta: { title: '新建模板字段', requiresAuth: true, parentPath: '/system/template-fields', showInMenu: false, breadcrumb: [{ name: 'SystemTemplateFieldList', title: '模版字段管理' }]}
              },
              {
                path: 'template-fields/edit/:id', 
                name: 'SystemTemplateFieldEdit',
                component:'system/TemplateFieldForm',
                props: true, // 将路由参数作为 props 传递给组件
                meta: { title: '编辑模板字段', requiresAuth: true, parentPath: '/system/template-fields', showInMenu: false, breadcrumb: [{ name: 'SystemTemplateFieldList', title: '模版字段管理' }]}
              },
            ]
          }
        ]
      },
]