/**
 * 本地文件管理组合式函数
 * 提供本地文件选择、预览、关联等功能（仅用于招标文件表单）
 */

import { ref } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 本地文件管理组合式函数
 * @param {Object} options 配置选项
 * @param {Object} options.formState 表单状态对象
 * @param {boolean} options.isEditMode 是否为编辑模式
 * @param {Ref} options.entityId 实体ID的响应式引用
 * @returns {Object} 本地文件管理相关的状态和方法
 */
export function useLocalFileManager(options) {
  const { formState, isEditMode, entityId } = options
  
  // 本地文件相关状态
  const fileInputRef = ref(null)
  const selectedLocalFile = ref(null) // 存储选中的本地文件对象
  const selectedLocalFileName = ref('')
  const selectedLocalFilePath = ref('') // 存储本地文件路径（用于协议加载）
  const showLocalFileSelector = ref(false) // 是否显示本地文件选择器
  
  // 本地文件选项的特殊值，使用null避免外键约束
  const LOCAL_FILE_OPTION_VALUE = null
  
  /**
   * 选择本地文件选项的处理方法
   */
  const selectLocalFileOption = () => {
    // 设置 scheme_id 为本地文件的特殊值
    formState.scheme_id = LOCAL_FILE_OPTION_VALUE
    
    // 清空其他本地文件选择状态（如果有的话）
    selectedLocalFile.value = null
    selectedLocalFileName.value = ''
    selectedLocalFilePath.value = ''
    
    // 显示本地文件选择器
    showLocalFileSelector.value = true
    
    console.log('选择本地文件选项，显示文件选择器')
  }
  
  /**
   * 处理本地文件选择器的触发
   */
  const triggerFileSelector = () => {
    if (fileInputRef.value) {
      fileInputRef.value.click()
    }
  }
  
  /**
   * 处理本地文件选择变化
   * @param {Event} event 文件选择事件
   */
  const handleLocalFileChange = (event) => {
    const file = event.target.files[0]
    console.log('file', file)
    
    if (file) {
      // 检查文件类型
      if (!file.name.toLowerCase().endsWith('.docx')) {
        message.error('请选择 .docx 格式的文件')
        return
      }
      
      selectedLocalFile.value = file
      selectedLocalFileName.value = file.name
      
      console.log('本地文件已选择:', file.name)
      
      // 获取文件的完整路径（在 Electron 中可用）
      const filePath = file.path || file.webkitRelativePath || file.name
      selectedLocalFilePath.value = filePath
      console.log('文件路径信息:', filePath)
      
      message.success('本地文件选择成功，已在左侧显示预览')
    }
  }
  
  /**
   * 清空本地文件选择
   */
  const clearLocalFile = () => {
    selectedLocalFile.value = null
    selectedLocalFileName.value = ''
    selectedLocalFilePath.value = ''
    formState.scheme_id = null
    showLocalFileSelector.value = false
    
    // 清空文件输入框
    if (fileInputRef.value) {
      fileInputRef.value.value = ''
    }
    
    // 清空本地文件关联（仅在编辑模式下清除已保存的关联）
    if (isEditMode && entityId.value) {
      removeLocalFileAssociation(entityId.value)
    }
    
    message.success('本地文件关联已清除')
  }
  
  /**
   * 重置本地文件状态
   */
  const resetLocalFileState = () => {
    selectedLocalFile.value = null
    selectedLocalFileName.value = ''
    selectedLocalFilePath.value = ''
    showLocalFileSelector.value = false
    
    // 清空文件输入框
    if (fileInputRef.value) {
      fileInputRef.value.value = ''
    }
  }
  
  /**
   * 获取本地文件存储数组
   * @returns {Array} 本地文件数据数组
   */
  const getLocalFilesData = () => {
    const data = localStorage.getItem('local_files_data')
    return data ? JSON.parse(data) : []
  }
  
  /**
   * 保存本地文件存储数组
   * @param {Array} data 本地文件数据数组
   */
  const saveLocalFilesData = (data) => {
    localStorage.setItem('local_files_data', JSON.stringify(data))
  }
  
  /**
   * 保存本地文件关联
   * @param {string|number} documentId 文档ID
   * @param {string} fileName 文件名
   * @param {string} filePath 文件路径
   */
  const saveLocalFileAssociation = (documentId, fileName, filePath) => {
    if (documentId && fileName) {
      const localFiles = getLocalFilesData()
      
      // 检查是否已存在该文档的关联
      const existingIndex = localFiles.findIndex(item => item.id === documentId)
      
      const fileData = {
        id: documentId,
        fileName: fileName,
        filePath: filePath || fileName,
        createdAt: new Date().toISOString(),
        lastAccessed: new Date().toISOString()
      }
      
      if (existingIndex >= 0) {
        // 更新现有关联
        localFiles[existingIndex] = fileData
      } else {
        // 添加新关联
        localFiles.push(fileData)
      }
      
      saveLocalFilesData(localFiles)
      console.log('本地文件关联已保存:', fileData)
    }
  }
  
  /**
   * 移除本地文件关联
   * @param {string|number} documentId 文档ID
   */
  const removeLocalFileAssociation = (documentId) => {
    if (documentId) {
      const localFiles = getLocalFilesData()
      const filteredFiles = localFiles.filter(item => item.id !== documentId)
      saveLocalFilesData(filteredFiles)
      console.log('本地文件关联已移除:', documentId)
    }
  }
  
  /**
   * 恢复本地文件关联
   * @param {string|number} documentId 文档ID
   */
  const restoreLocalFileAssociation = (documentId) => {
    if (documentId) {
      const localFiles = getLocalFilesData()
      const fileData = localFiles.find(item => item.id === documentId)
      
      if (fileData) {
        selectedLocalFileName.value = fileData.fileName
        selectedLocalFilePath.value = fileData.filePath || fileData.fileName
        
        // 更新最后访问时间
        fileData.lastAccessed = new Date().toISOString()
        const index = localFiles.findIndex(item => item.id === documentId)
        if (index >= 0) {
          localFiles[index] = fileData
          saveLocalFilesData(localFiles)
        }
        
        console.log('本地文件关联已恢复:', fileData)
        return true
      }
    }
    return false
  }
  
  /**
   * 调试：查看本地文件存储状态
   * @returns {Array} 本地文件数据数组
   */
  const debugLocalFilesData = () => {
    const localFiles = getLocalFilesData()
    console.log('本地文件存储状态:', localFiles)
    return localFiles
  }
  
  // 将调试函数暴露到 window 对象（仅开发环境）
  if (process.env.NODE_ENV === 'development') {
    window.debugLocalFiles = debugLocalFilesData
  }
  
  return {
    // 状态
    fileInputRef,
    selectedLocalFile,
    selectedLocalFileName,
    selectedLocalFilePath,
    showLocalFileSelector,
    LOCAL_FILE_OPTION_VALUE,
    
    // 方法
    selectLocalFileOption,
    triggerFileSelector,
    handleLocalFileChange,
    clearLocalFile,
    resetLocalFileState,
    getLocalFilesData,
    saveLocalFilesData,
    saveLocalFileAssociation,
    removeLocalFileAssociation,
    restoreLocalFileAssociation,
    debugLocalFilesData
  }
}
