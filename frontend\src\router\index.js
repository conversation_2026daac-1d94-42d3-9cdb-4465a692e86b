import { createRouter, createWebHashHistory } from 'vue-router'
import routerMap from './routerMap'
import store from '../store'

const Router = createRouter({
  history: createWebHashHistory(),
  // routes: routerMap,
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { guest: true } // 标记为访客路由，已登录用户访问时可重定向
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('@/views/auth/Register.vue'),
      meta: { guest: true }
    },
  ],
})

Router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters['auth/isAuthenticated']
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const isGuestRoute = to.matched.some(record => record.meta.guest)

  if (requiresAuth && !isAuthenticated) {
    // 记录用户尝试访问的URL，登录后可以重定向回去
    // store.commit('auth/SET_INTENDED_URL', to.fullPath) // 需要在 auth store 中添加 mutation
    next({ name: 'Login' })
  } else if (isGuestRoute && isAuthenticated) {
    next({ path: '/' }) // 如果已登录，访问登录/注册页则重定向到首页
  } else {
    next() // 正常访问
  }
})

export default Router
