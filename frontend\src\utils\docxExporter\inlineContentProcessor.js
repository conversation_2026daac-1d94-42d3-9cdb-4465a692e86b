import * as docx from 'docx'

/**
 * 内联内容处理模块
 * 负责将 Tiptap 节点的内联内容转换为 docx.js 的 TextRun 数组
 */

/**
 * 颜色名称到十六进制值的映射表
 */
const COLOR_NAME_MAP = {
  // 基础颜色
  black: '000000',
  white: 'FFFFFF',
  red: 'FF0000',
  green: '008000',
  blue: '0000FF',
  yellow: 'FFFF00',
  cyan: '00FFFF',
  magenta: 'FF00FF',
  orange: 'FFA500',
  purple: '800080',
  pink: 'FFC0CB',
  brown: 'A52A2A',
  gray: '808080',
  grey: '808080',

  // 扩展颜色
  darkred: '8B0000',
  darkgreen: '006400',
  darkblue: '00008B',
  lightgray: 'D3D3D3',
  lightgrey: 'D3D3D3',
  darkgray: 'A9A9A9',
  darkgrey: 'A9A9A9',
  silver: 'C0C0C0',
  gold: 'FFD700',
  navy: '000080',
  maroon: '800000',
  olive: '808000',
  lime: '00FF00',
  aqua: '00FFFF',
  teal: '008080',
  fuchsia: 'FF00FF'
}

/**
 * 将颜色值转换为6位十六进制格式
 * @param {string} color - 颜色值（可能是颜色名称、十六进制值等）
 * @returns {string|null} 6位十六进制颜色值（不含#）或null
 */
function convertColorToHex(color) {
  if (!color || typeof color !== 'string') {
    return null
  }

  // 移除空格并转为小写
  const cleanColor = color.trim().toLowerCase()

  // 如果是颜色名称，从映射表中获取
  if (COLOR_NAME_MAP[cleanColor]) {
    return COLOR_NAME_MAP[cleanColor]
  }

  // 如果是十六进制值，处理格式
  if (cleanColor.startsWith('#')) {
    const hexValue = cleanColor.substring(1)

    // 3位十六进制转6位
    if (hexValue.length === 3) {
      return hexValue
        .split('')
        .map(char => char + char)
        .join('')
        .toUpperCase()
    }

    // 6位十六进制直接返回
    if (hexValue.length === 6 && /^[0-9A-Fa-f]{6}$/.test(hexValue)) {
      return hexValue.toUpperCase()
    }
  }

  // 如果是不带#的十六进制值
  if (/^[0-9A-Fa-f]{6}$/.test(cleanColor)) {
    return cleanColor.toUpperCase()
  }

  // 如果是3位不带#的十六进制值
  if (/^[0-9A-Fa-f]{3}$/.test(cleanColor)) {
    return cleanColor
      .split('')
      .map(char => char + char)
      .join('')
      .toUpperCase()
  }

  // RGB格式处理 rgb(255, 0, 0)
  const rgbMatch = cleanColor.match(/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/)
  if (rgbMatch) {
    const r = parseInt(rgbMatch[1])
    const g = parseInt(rgbMatch[2])
    const b = parseInt(rgbMatch[3])

    if (r >= 0 && r <= 255 && g >= 0 && g <= 255 && b >= 0 && b <= 255) {
      return ((r << 16) | (g << 8) | b).toString(16).padStart(6, '0').toUpperCase()
    }
  }

  console.warn('[DOCX Export] 无法识别的颜色格式:', color)
  return null
}

/**
 * 递归处理 Tiptap 节点的内联内容，将其转换为 docx.TextRun 数组。
 * 特别处理 customField 节点，提取其文本内容。
 * 现在也处理内联图片节点。
 * @param {Array<object> | undefined} contentNodes - Tiptap 节点的内联内容数组。
 * @param {NumberingManager} numberingManager - 用于处理嵌套在内联内容中的列表（如果支持）。
 * @returns {Array<TextRun | Object>} docx.js 的 TextRun 或异步图片对象数组。
 */
export function processInlineContent(contentNodes, numberingManager) {
  if (!contentNodes) return []

  return contentNodes.flatMap(inlineNode => {
    // 处理文本节点
    if (inlineNode.type === 'text') {
      console.log('[DOCX Export] Processing text node:', inlineNode.text, 'marks:', inlineNode.marks)

      const textRunOptions = { text: inlineNode.text || '' } // 确保 text 不为 undefined

      // 处理文本标记（粗体、斜体、下划线、删除线等）
      if (inlineNode.marks) {
        inlineNode.marks.forEach(mark => {
          console.log('[DOCX Export] Processing mark:', mark.type, mark.attrs)

          switch (mark.type) {
            case 'bold':
              textRunOptions.bold = true
              break
            case 'italic':
              textRunOptions.italics = true
              break
            case 'underline':
              textRunOptions.underline = {} // 简单的下划线
              break
            case 'strike':
              textRunOptions.strike = true // 删除线支持
              console.log('[DOCX Export] 应用删除线样式:', inlineNode.text)
              break
            case 'highlight':
              // 处理高亮标记
              if (mark.attrs && mark.attrs.color) {
                const highlightColor = convertColorToHex(mark.attrs.color)
                if (highlightColor) {
                  textRunOptions.shading = {
                    type: docx.ShadingType.SOLID,
                    color: highlightColor
                  }
                  console.log('[DOCX Export] 应用高亮:', mark.attrs.color, '-> DOCX高亮:', highlightColor)
                }
              } else {
                // 默认黄色高亮
                textRunOptions.shading = {
                  type: docx.ShadingType.SOLID,
                  color: 'FFFF00'
                }
              }
              break
            case 'textStyle':
              // 处理文本样式标记（字体、字号、颜色等）
              if (mark.attrs) {
                // 字体
                if (mark.attrs.fontFamily) {
                  textRunOptions.font = mark.attrs.fontFamily
                  console.log('[DOCX Export] 应用字体:', mark.attrs.fontFamily)
                }

                // 字号
                if (mark.attrs.fontSize) {
                  // 将pt单位转换为半点（docx.js使用半点作为单位）
                  const fontSize = mark.attrs.fontSize
                  if (fontSize.endsWith('pt')) {
                    const ptValue = parseFloat(fontSize.replace('pt', ''))
                    textRunOptions.size = Math.round(ptValue * 2) // 转换为半点
                    console.log('[DOCX Export] 应用字号:', fontSize, '-> 半点:', textRunOptions.size)
                  }
                }

                // 颜色
                if (mark.attrs.color) {
                  // 转换颜色值为6位十六进制格式
                  const color = convertColorToHex(mark.attrs.color)
                  if (color) {
                    textRunOptions.color = color
                    console.log('[DOCX Export] 应用颜色:', mark.attrs.color, '-> DOCX颜色:', color)
                  } else {
                    textRunOptions.color = '000000' // 默认黑色
                  }
                }
              }
              break
          }
        })
      }

      console.log('[DOCX Export] Final textRunOptions:', textRunOptions)
      return [new docx.TextRun(textRunOptions)]
    }

    // 处理自定义字段节点
    else if (inlineNode.type === 'customField') {
      console.log('[DOCX Export] Processing customField:', inlineNode)

      // 如果customField有content，处理填充内容并添加字段格式
      if (inlineNode.content && inlineNode.content.length > 0) {
        // 需要重新处理字段内容并添加字段特有格式
        // 由于无法修改已创建的 TextRun，我们直接在这里处理原始内容
        
        return inlineNode.content.flatMap(contentNode => {
          if (contentNode.type === 'text') {
            // 为字段内的文本添加字段特有格式（下划线、黑色）
            const textRunOptions = { 
              text: contentNode.text || '',
              underline: {}, // 字段内容添加下划线
              color: '000000' // 黑色字体
            }

            // 处理文本标记（粗体、斜体等），保留原有格式
            if (contentNode.marks) {
              contentNode.marks.forEach(mark => {
                switch (mark.type) {
                  case 'bold':
                    textRunOptions.bold = true
                    break
                  case 'italic':
                    textRunOptions.italics = true
                    break
                  case 'underline':
                    // 保持下划线（字段本身就有下划线）
                    textRunOptions.underline = {}
                    break
                  case 'strike':
                    textRunOptions.strike = true
                    break
                  case 'highlight':
                    // 处理字段内的高亮
                    if (mark.attrs && mark.attrs.color) {
                      const highlightColor = convertColorToHex(mark.attrs.color)
                      if (highlightColor) {
                        textRunOptions.shading = {
                          type: docx.ShadingType.SOLID,
                          color: highlightColor
                        }
                      }
                    } else {
                      textRunOptions.shading = {
                        type: docx.ShadingType.SOLID,
                        color: 'FFFF00'
                      }
                    }
                    break
                  case 'textStyle':
                    if (mark.attrs) {
                      // 字体
                      if (mark.attrs.fontFamily) {
                        textRunOptions.font = mark.attrs.fontFamily
                      }

                      // 字号
                      if (mark.attrs.fontSize) {
                        const fontSize = mark.attrs.fontSize
                        if (fontSize.endsWith('pt')) {
                          const ptValue = parseFloat(fontSize.replace('pt', ''))
                          textRunOptions.size = Math.round(ptValue * 2)
                        }
                      }

                      // 颜色优先级：自定义颜色 > 字段默认黑色
                      if (mark.attrs.color) {
                        const color = convertColorToHex(mark.attrs.color)
                        if (color) {
                          textRunOptions.color = color
                        }
                      }
                    }
                    break
                }
              })
            }

            console.log('[DOCX Export] 字段内容格式:', textRunOptions)
            return [new docx.TextRun(textRunOptions)]
          } else {
            // 递归处理其他类型的内容节点
            return processInlineContent([contentNode], numberingManager)
          }
        })
      } else {
        // 如果customField没有内容，显示字段占位符并添加格式
        const fieldName = inlineNode.attrs?.field_name || inlineNode.attrs?.field_key || '字段'
        const placeholderText = `【${fieldName}】`

        console.log('[DOCX Export] CustomField has no content, using formatted placeholder:', placeholderText)

        // 为空字段占位符添加格式：下划线、蓝色、斜体
        return [
          new docx.TextRun({
            text: placeholderText,
            underline: {}, // 下划线
            color: '1890FF', // 蓝色（占位符用蓝色以便区分）
            italics: true, // 斜体表示这是占位符
            bold: false
          })
        ]
      }
    }

    // 处理内联图片节点
    else if (inlineNode.type === 'image') {
      console.log('[DOCX Export] Processing inline image:', inlineNode)

      const imageSrc = inlineNode.attrs?.src
      let imageWidth = null
      let imageHeight = null

      if (inlineNode.attrs?.width) {
        imageWidth = parseInt(inlineNode.attrs.width)
      }

      if (inlineNode.attrs?.height) {
        imageHeight = parseInt(inlineNode.attrs.height)
      }

      if (imageSrc) {
        console.log('[DOCX Export] 处理内联图片:', imageSrc, { width: imageWidth, height: imageHeight })

        // 返回异步图片对象，稍后会被处理（所有图片都是内联的）
        return [
          {
            type: 'asyncImage',
            src: imageSrc,
            width: imageWidth,
            height: imageHeight
          }
        ]
      }
      return []
    }

    // 处理硬换行节点
    else if (inlineNode.type === 'hardBreak') {
      /**
       * hardBreak 在 Tiptap 和 DOCX 的含义
       * Tiptap 的 hardBreak：表示"软换行"，即在同一段落内换行（类似于 Shift+Enter），不会新起一个段落。
       *
       * 根据 docx.js 官方文档：https://docx.js.org/#/usage/text?id=break
       * 软换行应该使用 TextRun 的 break 属性，而不是单独的 Break 对象
       */
      return [
        new docx.TextRun({
          text: '',
          break: 1
        })
      ]
    }

    // 未支持的内联节点类型
    // console.warn("Unsupported inline Tiptap node type for DOCX export:", inlineNode.type);
    return []
  })
}
