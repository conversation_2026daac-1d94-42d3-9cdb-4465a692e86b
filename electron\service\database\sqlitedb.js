'use strict'

const { BasedbService } = require('./basedb')
const bcrypt = require('bcrypt') // 密码加密库

/**
 * SQLite 数据库服务类
 * 负责初始化和管理招标文件助手系统的所有数据库表
 *
 * 表结构说明：
 * 1. 用户管理模块：app_users（用户表）、app_roles（角色表）
 * 2. 招标方案模块：tender_scheme_templates（方案模板表）、tender_schemes（方案表）、tender_scheme_history（方案历史版本表）
 * 3. 招标文件模块：bidding_document_templates（文件模板表）、bidding_document_template_contents（模板段落内容表）、bidding_documents（文件表）、bidding_document_history（文件历史版本表）
 * 4. 系统管理模块：template_fields（模板字段池表）
 * 5. AI对话模块：ai_chat_sessions（会话表）、ai_chat_messages（消息表）
 * 6. 风险评估模块：risk_assessment（风险评估表）
 *
 * @class SqlitedbService
 * @extends BasedbService
 */
class SqlitedbService extends BasedbService {
  constructor() {
    const options = {
      dbname: 'sqlite-app.db' // 数据库文件名
    }
    super(options)

    // 密码加密配置
    this.saltRounds = 10 // bcrypt 加密轮数

    // 数据库表名常量定义
    this.tableNames = {
      // 用户管理相关表
      users: 'app_users', // 用户表
      roles: 'app_roles', // 角色表

      // 招标方案相关表
      tenderSchemeTemplates: 'tender_scheme_templates', // 招标方案模板表
      tenderSchemes: 'tender_schemes', // 招标方案表
      tenderSchemeHistory: 'tender_scheme_history', // 招标方案历史版本表

      // 招标文件相关表
      biddingDocumentTemplates: 'bidding_document_templates', // 招标文件模板表
      biddingDocumentTemplateContents: 'bidding_document_template_contents', // 招标文件模板段落内容表
      biddingDocuments: 'bidding_documents', // 招标文件表
      biddingDocumentHistory: 'bidding_document_history', // 招标文件历史版本表

      // 系统管理相关表
      templateFields: 'template_fields', // 模板字段池表

      // AI对话相关表
      aiChatSessions: 'ai_chat_sessions', // AI对话会话表
      aiChatMessages: 'ai_chat_messages', // AI对话消息表

      // 风险评估相关表
      riskAssessment: 'risk_assessment' // 风险评估表
    }

    // 为了向后兼容，保留原有的表名属性
    this.appUsersTableName = this.tableNames.users
    this.appRolesTableName = this.tableNames.roles
    this.tenderSchemeTemplatesTableName = this.tableNames.tenderSchemeTemplates
    this.tenderSchemesTableName = this.tableNames.tenderSchemes
    this.biddingDocumentTemplatesTableName = this.tableNames.biddingDocumentTemplates
    this.biddingDocumentsTableName = this.tableNames.biddingDocuments
    this.templateFieldsTableName = this.tableNames.templateFields
    this.templateContentsTableName = this.tableNames.biddingDocumentTemplateContents
    this.tenderSchemeHistoryTableName = this.tableNames.tenderSchemeHistory
    this.biddingDocumentHistoryTableName = this.tableNames.biddingDocumentHistory
    this.aiChatSessionsTableName = this.tableNames.aiChatSessions
    this.aiChatMessagesTableName = this.tableNames.aiChatMessages
    this.riskAssessmentTableName = this.tableNames.riskAssessment
  }

  /**
   * 数据库初始化方法
   * 按照依赖关系顺序创建所有表和索引，并插入默认数据
   */
  init() {
    console.log('[SqlitedbService] 开始初始化数据库...')

    // 初始化基础数据库连接
    this._init()

    // 检查数据库连接是否成功
    if (!this.db) {
      console.error('[SqlitedbService] 数据库连接失败，无法继续初始化')
      return
    }

    console.log('[SqlitedbService] 数据库连接成功，开始创建表结构...')

    try {
      // 使用事务确保所有表创建操作的原子性
      const initTransaction = this.db.transaction(() => {
        // 按照依赖关系顺序创建表
        this.createUserManagementTables() // 1. 用户管理相关表（基础表，无依赖）
        this.createTenderSchemeTables() // 2. 招标方案相关表（依赖用户表）
        this.createBiddingDocumentTables() // 3. 招标文件相关表（依赖用户表和方案表）
        this.createSystemManagementTables() // 4. 系统管理相关表（独立表）
        this.createAiChatTables() // 5. AI对话相关表（依赖用户表）
        this.createRiskAssessmentTables() // 6. 风险评估相关表（依赖用户表）

        // 创建所有索引
        this.createAllIndexes()

        // 插入默认数据
        this.insertDefaultData()
      })

      // 执行事务
      initTransaction()

      console.log('[SqlitedbService] 数据库初始化完成')
    } catch (error) {
      console.error('[SqlitedbService] 数据库初始化失败:', error)
      throw error
    }
  }

  /**
   * 创建用户管理相关表
   * 包括：角色表、用户表
   */
  createUserManagementTables() {
    // 1. 创建角色表（app_roles）
    const createRolesTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.roles} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 角色ID，主键自增
        name TEXT NOT NULL UNIQUE,               -- 角色名称，唯一约束
        description TEXT,                        -- 角色描述
        permissions TEXT,                        -- 权限列表，JSON格式存储
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime'))  -- 更新时间
      )
    `
    this.db.exec(createRolesTableSql)
    console.log(`[SqlitedbService] 角色表 (${this.tableNames.roles}) 已确保存在`)

    // 2. 创建用户表（app_users）
    const createUsersTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.users} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 用户ID，主键自增
        username TEXT NOT NULL UNIQUE,           -- 用户名，唯一约束
        password_hash TEXT NOT NULL,             -- 密码哈希值
        nickname TEXT,                           -- 用户昵称
        email TEXT UNIQUE,                       -- 邮箱地址，唯一约束
        role_id INTEGER,                         -- 关联的角色ID
        status TEXT DEFAULT 'active',            -- 用户状态：active(活跃)/inactive(非活跃)
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime')), -- 更新时间
        FOREIGN KEY (role_id) REFERENCES ${this.tableNames.roles}(id) ON DELETE SET NULL -- 外键约束，角色删除时设为NULL
      )
    `
    this.db.exec(createUsersTableSql)
    console.log(`[SqlitedbService] 用户表 (${this.tableNames.users}) 已确保存在`)
  }

  /**
   * 创建招标方案相关表
   * 包括：招标方案模板表、招标方案表、招标方案历史版本表
   */
  createTenderSchemeTables() {
    // 1. 创建招标方案模板表（tender_scheme_templates）
    const createTenderSchemeTemplatesTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.tenderSchemeTemplates} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 模板ID，主键自增
        name TEXT NOT NULL,                      -- 模板名称
        type INTEGER NOT NULL,                   -- 方案类型：1-服务类，2-工程类，3-物资类，4-加油站
        remark TEXT,                             -- 备注信息
        content TEXT,                            -- 模板内容，Tiptap JSON格式存储
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime'))  -- 更新时间
      )
    `
    this.db.exec(createTenderSchemeTemplatesTableSql)
    console.log(`[SqlitedbService] 招标方案模板表 (${this.tableNames.tenderSchemeTemplates}) 已确保存在`)

    // 2. 创建招标方案表（tender_schemes）
    const createTenderSchemesTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.tenderSchemes} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 方案ID，主键自增
        name TEXT NOT NULL,                      -- 方案名称
        template_id INTEGER,                     -- 关联的模板ID
        remark TEXT,                             -- 备注信息
        content TEXT,                            -- 方案内容，存储字段值对象的JSON字符串
        version_number INTEGER DEFAULT 1,        -- 当前版本号，累加递增
        update_remark TEXT DEFAULT '',          -- 更新备注，用户提交更新时填写
        updated_by INTEGER,                      -- 更新人ID
        share_status TEXT DEFAULT 'private',     -- 分享状态：private(私有)/public(公开)/specific_users(指定用户)
        share_users TEXT,                        -- 指定分享用户ID列表，逗号分隔
        created_by INTEGER,                      -- 创建人ID
        risk_assessment_id INTEGER,              -- 关联的风险评估ID
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime')), -- 更新时间
        FOREIGN KEY (template_id) REFERENCES ${this.tableNames.tenderSchemeTemplates}(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES ${this.tableNames.users}(id) ON DELETE SET NULL,
        FOREIGN KEY (updated_by) REFERENCES ${this.tableNames.users}(id) ON DELETE SET NULL,
        FOREIGN KEY (risk_assessment_id) REFERENCES ${this.tableNames.riskAssessment}(id) ON DELETE SET NULL
      )
    `
    this.db.exec(createTenderSchemesTableSql)
    console.log(`[SqlitedbService] 招标方案表 (${this.tableNames.tenderSchemes}) 已确保存在`)

    // 3. 创建招标方案历史版本表（tender_scheme_history）
    const createTenderSchemeHistoryTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.tenderSchemeHistory} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 历史记录ID，主键自增
        scheme_id INTEGER NOT NULL,              -- 关联的招标方案ID
        content TEXT NOT NULL,                   -- 历史版本的内容（更新前的字段值对象JSON字符串）
        version_number INTEGER NOT NULL,         -- 版本号（更新前的版本号）
        update_remark TEXT DEFAULT '',          -- 更新备注（更新前的备注）
        updated_by INTEGER,                      -- 更新人ID（更新前的更新人）
        risk_assessment_id INTEGER,              -- 该版本关联的风险评估ID，保留历史版本的评估记录
        created_at DATETIME,                     -- 版本创建时间（实际是主表更新前的updated_at）
        FOREIGN KEY (scheme_id) REFERENCES ${this.tableNames.tenderSchemes}(id) ON DELETE CASCADE,
        FOREIGN KEY (updated_by) REFERENCES ${this.tableNames.users}(id) ON DELETE SET NULL
      )
    `
    this.db.exec(createTenderSchemeHistoryTableSql)
    console.log(`[SqlitedbService] 招标方案历史版本表 (${this.tableNames.tenderSchemeHistory}) 已确保存在`)
  }

  /**
   * 创建招标文件相关表
   * 包括：招标文件模板表、招标文件模板段落内容表、招标文件表、招标文件历史版本表
   */
  createBiddingDocumentTables() {
    // 1. 创建招标文件模板表（bidding_document_templates）
    // 注意：需要先创建模板表，因为段落内容表依赖它
    const createBiddingDocumentTemplatesTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.biddingDocumentTemplates} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 模板ID，主键自增
        name TEXT NOT NULL,                      -- 模板名称
        parent_id INTEGER,                       -- 父模板ID，用于层级关系（最多五级）
        file_type TEXT,                          -- 文件类型或模板分类
        remark TEXT,                             -- 备注，如用途说明
        paragraph_refs TEXT,                     -- 段落引用JSON数组，格式：[{"paragraphId":1,"templateId":2}]
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime')), -- 更新时间
        FOREIGN KEY (parent_id) REFERENCES ${this.tableNames.biddingDocumentTemplates}(id) ON DELETE CASCADE
      )
    `
    this.db.exec(createBiddingDocumentTemplatesTableSql)
    console.log(`[SqlitedbService] 招标文件模板表 (${this.tableNames.biddingDocumentTemplates}) 已确保存在`)

    // 2. 创建招标文件模板段落内容表（bidding_document_template_contents）
    const createTemplateContentsTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.biddingDocumentTemplateContents} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 段落ID，主键自增
        content_data TEXT NOT NULL,              -- Tiptap JSON段落内容
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime'))  -- 更新时间
      )
    `
    this.db.exec(createTemplateContentsTableSql)
    console.log(`[SqlitedbService] 招标文件模板段落内容表 (${this.tableNames.biddingDocumentTemplateContents}) 已确保存在`)

    // 3. 创建招标文件表（bidding_documents）
    const createBiddingDocumentsTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.biddingDocuments} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 文件ID，主键自增
        name TEXT NOT NULL,                      -- 文件名称
        template_id INTEGER,                     -- 关联的招标文件模板ID
        scheme_id INTEGER,                       -- 关联的招标方案ID（可选）
        remark TEXT,                             -- 备注信息
        content TEXT,                            -- 文件具体内容（字段值对象的JSON字符串）
        version_number INTEGER DEFAULT 1,        -- 当前版本号，累加递增
        update_remark TEXT DEFAULT '',          -- 更新备注，用户提交更新时填写
        updated_by INTEGER,                      -- 更新人ID
        share_status TEXT DEFAULT 'private',     -- 分享状态：private(私有)/public(公开)/specific_users(指定用户)
        share_users TEXT,                        -- 指定分享用户ID列表，逗号分隔
        created_by INTEGER,                      -- 创建人ID
        risk_assessment_id INTEGER,              -- 关联的风险评估ID
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime')), -- 更新时间
        FOREIGN KEY (template_id) REFERENCES ${this.tableNames.biddingDocumentTemplates}(id) ON DELETE SET NULL,
        FOREIGN KEY (scheme_id) REFERENCES ${this.tableNames.tenderSchemes}(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES ${this.tableNames.users}(id) ON DELETE SET NULL,
        FOREIGN KEY (updated_by) REFERENCES ${this.tableNames.users}(id) ON DELETE SET NULL,
        FOREIGN KEY (risk_assessment_id) REFERENCES ${this.tableNames.riskAssessment}(id) ON DELETE SET NULL
      )
    `
    this.db.exec(createBiddingDocumentsTableSql)
    console.log(`[SqlitedbService] 招标文件表 (${this.tableNames.biddingDocuments}) 已确保存在`)

    // 4. 创建招标文件历史版本表（bidding_document_history）
    const createBiddingDocumentHistoryTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.biddingDocumentHistory} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 历史记录ID，主键自增
        document_id INTEGER NOT NULL,            -- 关联的招标文件ID
        content TEXT NOT NULL,                   -- 历史版本的内容（更新前的字段值对象JSON字符串）
        version_number INTEGER NOT NULL,         -- 版本号（更新前的版本号）
        update_remark TEXT DEFAULT '',          -- 更新备注（更新前的备注）
        updated_by INTEGER,                      -- 更新人ID（更新前的更新人）
        risk_assessment_id INTEGER,              -- 该版本关联的风险评估ID，保留历史版本的评估记录
        created_at DATETIME,                     -- 版本创建时间（实际是主表更新前的updated_at）
        FOREIGN KEY (document_id) REFERENCES ${this.tableNames.biddingDocuments}(id) ON DELETE CASCADE,
        FOREIGN KEY (updated_by) REFERENCES ${this.tableNames.users}(id) ON DELETE SET NULL
      )
    `
    this.db.exec(createBiddingDocumentHistoryTableSql)
    console.log(`[SqlitedbService] 招标文件历史版本表 (${this.tableNames.biddingDocumentHistory}) 已确保存在`)
  }

  /**
   * 创建系统管理相关表
   * 包括：模板字段池表
   */
  createSystemManagementTables() {
    // 创建模板字段池表（template_fields）
    const createTemplateFieldsTableSql = `
      CREATE TABLE IF NOT EXISTS ${this.tableNames.templateFields} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 字段ID，主键自增
        field_name TEXT NOT NULL,                -- 字段名称（显示名）
        field_key TEXT NOT NULL UNIQUE,          -- 字段标识符（唯一键）
        field_type TEXT DEFAULT 'custom',        -- 字段类型：default(默认)/custom(自定义)
        remark TEXT,                             -- 备注信息
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime'))  -- 更新时间
      )
    `
    this.db.exec(createTemplateFieldsTableSql)
    console.log(`[SqlitedbService] 模板字段池表 (${this.tableNames.templateFields}) 已确保存在`)
  }

  /**
   * 创建AI对话相关表
   * 包括：AI对话会话表、AI对话消息表
   */
  createAiChatTables() {
    // 1. 创建AI对话会话表（ai_chat_sessions）
    const createAiChatSessionsTableSql = `
      CREATE TABLE  IF NOT EXISTS ${this.tableNames.aiChatSessions} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 会话ID，主键自增
        user_id INTEGER NOT NULL,                -- 用户ID
        title TEXT,                              -- 会话标题（自动生成或用户设置）
        status TEXT DEFAULT 'active',            -- 会话状态：active(活跃)/deleted(已删除)
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime')), -- 更新时间
        FOREIGN KEY (user_id) REFERENCES ${this.tableNames.users}(id) ON DELETE CASCADE
      )
    `
    this.db.exec(createAiChatSessionsTableSql)
    console.log(`[SqlitedbService] AI对话会话表 (${this.tableNames.aiChatSessions}) 已确保存在`)

    // 2. 创建AI对话消息表（ai_chat_messages）
    const createAiChatMessagesTableSql = `
      CREATE TABLE  IF NOT EXISTS ${this.tableNames.aiChatMessages} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,    -- 消息ID，主键自增
        session_id INTEGER NOT NULL,            -- 关联的会话ID
        role TEXT NOT NULL,                     -- 消息角色：user(用户)/assistant(助手)/system(系统)
        content TEXT NOT NULL,                  -- 消息内容
        timestamp DATETIME DEFAULT (datetime('now','localtime')), -- 消息时间戳
        tokens_used INTEGER DEFAULT 0,         -- 本条消息使用的token数量
        model_name TEXT,                        -- 使用的AI模型名称
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 创建时间
        FOREIGN KEY (session_id) REFERENCES ${this.tableNames.aiChatSessions}(id) ON DELETE CASCADE
      )
    `
    this.db.exec(createAiChatMessagesTableSql)
    console.log(`[SqlitedbService] AI对话消息表 (${this.tableNames.aiChatMessages}) 已确保存在`)
  }

  /**
   * 创建风险评估相关表
   * 包括：风险评估表
   */
  createRiskAssessmentTables() {
    // 创建风险评估表（risk_assessment）
    const createRiskAssessmentTableSql = `
        CREATE TABLE  IF NOT EXISTS ${this.tableNames.riskAssessment} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,                    -- 评估任务主键ID，唯一标识每次风险评估
        document_type TEXT NOT NULL,                             -- 文档类型：tender_scheme(招标方案)/bidding_document(招标文件)
        user_id INTEGER NOT NULL,                                -- 发起评估的用户ID，关联app_users表
        status TEXT DEFAULT 'pending',                           -- 评估状态：pending(待处理)/processing(处理中)/low(低风险)/medium(中风险)/high(高风险)/failed(失败)/cancelled(已取消)
        started_at DATETIME,                                     -- 开始处理评估的具体时间戳
        completed_at DATETIME,                                   -- 评估完成的具体时间戳，用于计算处理耗时
        content_sent TEXT,                                       -- 实际发送给AI模型的文档内容，保留原始输入用于问题排查
        ai_result TEXT,                                          -- AI返回的评估结果JSON字符串，包含风险等级、理由、字段索引等
        tokens_used INTEGER DEFAULT 0,                          -- 本次评估消耗的AI tokens数量，用于成本统计
        error_message TEXT,                                      -- 评估失败时的详细错误信息
        created_at DATETIME DEFAULT (datetime('now','localtime')), -- 评估任务创建时间
        updated_at DATETIME DEFAULT (datetime('now','localtime')), -- 评估任务最后更新时间
        FOREIGN KEY (user_id) REFERENCES ${this.tableNames.users}(id) ON DELETE CASCADE  -- 用户删除时级联删除相关评估记录
      )
    `
    this.db.exec(createRiskAssessmentTableSql)
    console.log(`[SqlitedbService] 风险评估表 (${this.tableNames.riskAssessment}) 已确保存在`)
  }

  /**
   * 创建所有数据库索引
   * 为提高查询性能，为关键字段创建索引
   */
  createAllIndexes() {
    console.log('[SqlitedbService] 开始创建数据库索引...')

    // 定义所有需要创建的索引
    const indexes = [
      // 招标方案历史版本表索引
      { name: 'idx_scheme_history_scheme_id', table: this.tableNames.tenderSchemeHistory, columns: 'scheme_id', description: '招标方案历史版本按方案ID查询索引' },
      {
        name: 'idx_scheme_history_version',
        table: this.tableNames.tenderSchemeHistory,
        columns: 'scheme_id, version_number',
        description: '招标方案历史版本按方案ID和版本号复合索引'
      },

      // 招标文件历史版本表索引
      {
        name: 'idx_document_history_document_id',
        table: this.tableNames.biddingDocumentHistory,
        columns: 'document_id',
        description: '招标文件历史版本按文件ID查询索引'
      },
      {
        name: 'idx_document_history_version',
        table: this.tableNames.biddingDocumentHistory,
        columns: 'document_id, version_number',
        description: '招标文件历史版本按文件ID和版本号复合索引'
      },

      // AI对话会话表索引
      { name: 'idx_ai_chat_sessions_user_id', table: this.tableNames.aiChatSessions, columns: 'user_id', description: 'AI对话会话按用户ID查询索引' },
      { name: 'idx_ai_chat_sessions_status', table: this.tableNames.aiChatSessions, columns: 'status', description: 'AI对话会话按状态查询索引' },

      // AI对话消息表索引
      { name: 'idx_ai_chat_messages_session_id', table: this.tableNames.aiChatMessages, columns: 'session_id', description: 'AI对话消息按会话ID查询索引' },
      { name: 'idx_ai_chat_messages_timestamp', table: this.tableNames.aiChatMessages, columns: 'timestamp', description: 'AI对话消息按时间戳查询索引' },
      { name: 'idx_ai_chat_messages_role', table: this.tableNames.aiChatMessages, columns: 'role', description: 'AI对话消息按角色查询索引' },

      // 风险评估表索引
      { name: 'idx_risk_assessment_user_id', table: this.tableNames.riskAssessment, columns: 'user_id', description: '风险评估按用户ID查询索引' },
      { name: 'idx_risk_assessment_status', table: this.tableNames.riskAssessment, columns: 'status', description: '风险评估按状态查询索引' },
      { name: 'idx_risk_assessment_document_type', table: this.tableNames.riskAssessment, columns: 'document_type', description: '风险评估按文档类型查询索引' },
      { name: 'idx_risk_assessment_created_at', table: this.tableNames.riskAssessment, columns: 'created_at', description: '风险评估按创建时间查询索引' }
    ]

    // 批量创建索引
    indexes.forEach(index => {
      const createIndexSql = `CREATE INDEX IF NOT EXISTS ${index.name} ON ${index.table}(${index.columns})`
      try {
        this.db.exec(createIndexSql)
        console.log(`[SqlitedbService] 创建索引 ${index.name} 成功 - ${index.description}`)
      } catch (error) {
        console.error(`[SqlitedbService] 创建索引 ${index.name} 失败:`, error)
      }
    })

    console.log('[SqlitedbService] 数据库索引创建完成')
  }

  /**
   * 插入系统默认数据
   * 包括默认角色和管理员用户
   */
  insertDefaultData() {
    console.log('[SqlitedbService] 开始插入默认数据...')

    try {
      // 1. 插入默认角色
      this.insertDefaultRoles()

      // 2. 插入默认管理员用户
      this.insertDefaultAdminUser()

      console.log('[SqlitedbService] 默认数据插入完成')
    } catch (error) {
      console.error('[SqlitedbService] 插入默认数据失败:', error)
    }
  }

  /**
   * 插入默认角色数据
   */
  insertDefaultRoles() {
    // 检查是否已有角色数据
    const roleCount = this.db.prepare(`SELECT COUNT(id) as count FROM ${this.tableNames.roles}`).get().count

    if (roleCount === 0) {
      const insertRoleStmt = this.db.prepare(`INSERT INTO ${this.tableNames.roles} (name, description, permissions) VALUES (@name, @description, @permissions)`)

      // 默认角色配置
      const defaultRoles = [
        {
          name: 'superadmin',
          description: '超级管理员',
          permissions: JSON.stringify(['all']) // 拥有所有权限
        },
        {
          name: 'ordinary_user',
          description: '普通用户',
          permissions: JSON.stringify([]) // 基础权限
        }
      ]

      // 批量插入角色
      defaultRoles.forEach(role => {
        insertRoleStmt.run(role)
      })

      console.log('[SqlitedbService] 默认角色数据插入成功')
    }
  }

  /**
   * 插入默认管理员用户
   */
  insertDefaultAdminUser() {
    // 检查是否已有用户数据
    const userCount = this.db.prepare(`SELECT COUNT(id) as count FROM ${this.tableNames.users}`).get().count

    if (userCount === 0) {
      // 获取超级管理员角色ID
      const superadminRole = this.db.prepare(`SELECT id FROM ${this.tableNames.roles} WHERE name = ?`).get('superadmin')

      if (superadminRole) {
        // 创建默认管理员账户
        const defaultAdminPassword = 'admin123'
        const hashedPassword = bcrypt.hashSync(defaultAdminPassword, this.saltRounds)

        const insertAdminStmt = this.db.prepare(`
          INSERT INTO ${this.tableNames.users} 
          (username, password_hash, nickname, email, role_id, status) 
          VALUES (@username, @password_hash, @nickname, @email, @role_id, @status)
        `)

        insertAdminStmt.run({
          username: 'admin',
          password_hash: hashedPassword,
          nickname: '超级管理员',
          email: '<EMAIL>',
          role_id: superadminRole.id,
          status: 'active'
        })

        console.log('[SqlitedbService] 默认管理员用户创建成功 (用户名: admin, 密码: admin123)')
      }
    }
  }

  /**
   * 获取数据目录路径
   * @returns {Promise<string>} 数据目录路径
   */
  async getDataDir() {
    const dir = this.storage.getDbDir()
    return dir
  }

  /**
   * 设置自定义数据目录
   * @param {string} dir - 自定义目录路径
   * @returns {Promise<void>}
   */
  async setCustomDataDir(dir) {
    if (!dir) {
      return
    }
    this.changeDataDir(dir)
    this.init()
    return
  }
}

// 类的字符串表示
SqlitedbService.toString = () => '[class SqlitedbService]'

// 导出服务类和单例实例
module.exports = {
  SqlitedbService,
  sqlitedbService: new SqlitedbService()
}
