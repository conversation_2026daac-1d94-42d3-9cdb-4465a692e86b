import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import TableOfContentsNodeView from './TableOfContentsNodeView.vue'

/**
 * 目录扩展 TableOfContentsExtension
 * 该扩展用于在 Tiptap 编辑器中插入目录占位符，在DOCX导出时会生成真正的目录。
 */
export const TableOfContentsExtension = Node.create({
  name: 'tableOfContents',
  group: 'block', // 块级元素
  inline: false,
  atom: true, // 原子节点，不可编辑内容
  selectable: true, // 可选中
  
  addAttributes() {
    return {
      // 目录标题
      title: {
        default: '目录',
        parseHTML: element => element.getAttribute('data-toc-title'),
        renderHTML: attributes => ({
          'data-toc-title': attributes.title
        })
      },
      // 包含的标题级别（1-6）
      levels: {
        default: '1,2,3',
        parseHTML: element => element.getAttribute('data-toc-levels'),
        renderHTML: attributes => ({
          'data-toc-levels': attributes.levels
        })
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-table-of-contents]'
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    // 由于使用了 NodeView，这里只需要提供基本的 HTML 结构用于识别节点类型
    // 实际的渲染工作由 TableOfContentsNodeView.vue 组件处理
    return [
      'div', 
      mergeAttributes(HTMLAttributes, { 
        'data-table-of-contents': ''
      })
    ]
  },

  addNodeView() {
    return VueNodeViewRenderer(TableOfContentsNodeView)
  },

  addCommands() {
    return {
      insertTableOfContents: (options = {}) => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: {
            title: options.title || '目录',
            levels: options.levels || '1,2,3'
          }
        })
      }
    }
  }
})

export default TableOfContentsExtension 