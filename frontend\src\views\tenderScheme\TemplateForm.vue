<template>
  <!-- <div class="page-container form-container"> -->
    <!-- :bordered="false" -->
    <a-page-header :title="isEditMode ? '编辑招标方案模版' : '新建招标方案模版'" @back="handleCancel" />
    <a-card>
      <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" layout="horizontal">
        <a-form-item label="模板名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入模板名称" />
        </a-form-item>

        <a-form-item label="方案类型" name="type">
          <a-select v-model:value="formState.type" placeholder="请选择方案类型">
            <a-select-option v-for="item in schemeTypes" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>

        <a-form-item label="模板内容" name="content">
          <tiptap-editor v-model="formState.content" placeholder="请输入模板内容..." min-height="300px" :editable="true" :show-toolbar="true" :fill-mode="false" />
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
          <a-space>
            <a-button type="primary" @click="handleSubmit" :loading="isLoading" :disabled="isExportingDocx">提交</a-button>
            <a-button @click="handleExportToDocx" :loading="isExportingDocx" :disabled="isLoading || !formState.content">导出为 DOCX</a-button>
            <a-button @click="handleCancel" :disabled="isLoading || isExportingDocx">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>
  <!-- </div> -->
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import api from '@/api'
import TiptapEditor from '@/components/TiptapEditor/index.vue'
import { exportTiptapContentToDocx } from '@/utils/docxExporter'

const route = useRoute()
const router = useRouter()
const formRef = ref()

const isLoading = ref(false)
const isExportingDocx = ref(false)
const templateId = ref(route.params.id || null)
const isEditMode = computed(() => !!templateId.value)

const schemeTypes = ref([
  { value: 1, label: '服务类' },
  { value: 2, label: '工程类' },
  { value: 3, label: '物资类' },
  { value: 4, label: '加油站' }
])

const rules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择方案类型', trigger: 'change' }]
}

const getDefaultContent = () => ({
  type: 'doc',
  content: [
    {
      type: 'paragraph'
    }
  ]
})

const formState = reactive({
  name: '',
  type: undefined,
  remark: '',
  content: getDefaultContent()
})

const fetchTemplateDetails = async id => {
  isLoading.value = true
  try {
    const response = await api.tenderSchemeTemplate.getById({ id })
    if (response && response.success && response.data) {
      const templateDetails = response.data
      formState.name = templateDetails.name
      formState.type = templateDetails.type
      formState.remark = templateDetails.remark || ''

      let newContent = templateDetails.content
      if (typeof newContent === 'string') {
        try {
          newContent = JSON.parse(newContent)
        } catch (e) {
          console.error('解析模板内容JSON失败:', e)
          message.error('模板内容格式错误，已重置。')
          newContent = getDefaultContent()
        }
      }
      if (typeof newContent !== 'object' || newContent === null || newContent.type !== 'doc') {
        if (newContent) {
          message.warn('模板内容结构不符合预期，已部分重置。')
        }
        newContent = getDefaultContent()
      }
      formState.content = newContent
    } else {
      message.error(response?.message || '未找到模板详情')
      router.push({ name: 'TenderSchemeTemplateList' })
    }
  } catch (error) {
    console.error('获取模板详情失败:', error)
    message.error('获取模板详情时发生错误: ' + error.message)
  } finally {
    isLoading.value = false
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    isLoading.value = true
    console.log('[TemplateForm] handleSubmit formState:', formState)

    const payload = { ...formState }

    const currentId = isEditMode.value ? templateId.value : undefined

    if (currentId) {
      payload.id = currentId
      console.log('[TemplateForm] Updating template with payload:', payload)
      const response = await api.tenderSchemeTemplate.update(payload)
      if (response && response.success) {
        message.success(response.message || '模板更新成功')
        router.push({ name: 'TenderSchemeTemplateList' })
      } else {
        message.error(response?.message || '模板更新失败')
      }
    } else {
      if ('id' in payload) {
        delete payload.id
      }
      console.log('[TemplateForm] Creating new template with payload:', payload)
      const response = await api.tenderSchemeTemplate.create(payload)
      if (response && response.success) {
        message.success(response.message || '模板创建成功')
        router.push({ name: 'TenderSchemeTemplateList' })
      } else {
        message.error(response?.message || '模板创建失败')
      }
    }
  } catch (errorInfo) {
    if (errorInfo.name === 'AxiosError' || (errorInfo.response && errorInfo.response.data)) {
      const serverMessage = errorInfo.response?.data?.message || errorInfo.message || '操作失败'
      message.error(serverMessage)
    } else if (errorInfo.errors && errorInfo.errorFields) {
      console.log('表单验证失败:', errorInfo)
      message.error('表单校验失败，请检查输入项')
    } else {
      message.error(errorInfo.message || '发生未知错误')
      console.error('handleSubmit error:', errorInfo)
    }
  } finally {
    isLoading.value = false
  }
}

const handleExportToDocx = async () => {
  if (!formState.content || !formState.content.content) {
    message.error('没有可导出的内容')
    return
  }

  isExportingDocx.value = true
  try {
    const fileName = formState.name || '招标方案模板'
    await exportTiptapContentToDocx(formState.content, fileName)
    message.success('导出成功')
  } catch (error) {
    console.error('导出DOCX失败:', error)
    message.error('导出失败: ' + error.message)
  } finally {
    isExportingDocx.value = false
  }
}

const handleCancel = () => {
  router.push({ name: 'TenderSchemeTemplateList' })
}

watch(
  () => route.params.id,
  newId => {
    templateId.value = newId || null
    formRef.value?.resetFields()
    formState.name = ''
    formState.type = undefined
    formState.remark = ''
    formState.content = getDefaultContent()
    if (newId) {
      fetchTemplateDetails(newId)
    }
  },
  { immediate: true }
)

onMounted(() => {})
</script>

<style lang="less" scoped>
.form-container {
  padding: 16px;
}
</style>
