'use strict'

// const Controller = require('ee-core').Controller; // 移除对 ee-core Controller 的依赖
const TenderSchemeService = require('../service/tenderSchemeService')

/**
 * 招标方案控制器
 * @class
 */
class TenderSchemeController {
  // 不再继承 Controller
  constructor(ctx) {
    // super(ctx); // 移除 super 调用
    this.tenderSchemeService = new TenderSchemeService()
  }

  /**
   * 创建招标方案
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async create(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    // 从请求头获取用户ID
    const userId = ctx && ctx.headers ? ctx.headers['x-user-id'] : null
    if (userId) {
      params.created_by = parseInt(userId)
      params.updated_by = parseInt(userId)
    }

    try {
      const result = await this.tenderSchemeService.create(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取招标方案列表
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async list(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {}

    // 从请求头获取用户ID
    const userId = ctx && ctx.headers ? ctx.headers['x-user-id'] : null
    if (userId) {
      params.current_user_id = parseInt(userId)
    }

    try {
      const result = await this.tenderSchemeService.list(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取单个招标方案
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async get(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的方案ID' }
    }

    try {
      const result = await this.tenderSchemeService.getById(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 更新招标方案
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async update(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    const { id, ...updateData } = params
    const schemeId = parseInt(id)

    if (isNaN(schemeId)) {
      return { success: false, message: '无效的方案ID' }
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return { success: false, message: '没有提供需要更新的数据' }
    }

    // 从请求头获取用户ID
    const userId = ctx && ctx.headers ? ctx.headers['x-user-id'] : null
    if (userId) {
      updateData.updated_by = parseInt(userId)
    }

    try {
      const result = await this.tenderSchemeService.update(schemeId, updateData)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 删除招标方案
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async delete(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的方案ID' }
    }

    try {
      const result = await this.tenderSchemeService.delete(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取招标方案历史记录
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async getHistory(args, ctx) {
    let schemeId
    if (args && args.scheme_id) {
      schemeId = parseInt(args.scheme_id)
    } else {
      schemeId = parseInt(ctx.query?.scheme_id || ctx.request?.body?.scheme_id)
    }

    if (isNaN(schemeId)) {
      return { success: false, message: '无效的方案ID' }
    }

    try {
      const result = await this.tenderSchemeService.getHistory(schemeId)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
}

TenderSchemeController.toString = () => '[class TenderSchemeController]'

module.exports = TenderSchemeController
