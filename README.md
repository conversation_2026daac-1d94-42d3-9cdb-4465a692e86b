# 招标文件助手

框架：[Electron-Egg](https://www.kaka996.com/)一个入门简单、跨平台、企业级桌面软件开发框架

#### 安装依赖

```bash
# 根目录，安装 electron 依赖
npm i
# 构建 sqlite（如果不需要请删除 demo 中 ./service/database 文件 和 导入代码）
npm run re-sqlite

# 进入【前端目录】安装 frontend 依赖
cd frontend
npm i
```

详细说明：[sqlite 安装](https://www.kaka996.com/pages/49e723/)

#### 启动

```bash
npm run dev
```

#### 构建

构建分为 前端构建、主进程构建、代码加密、go/python 语言构建，打包可执行程序。

```bash
# 构建前端/主进程，并加密
npm run build

# 生成可执行程序 win64
npm run build-w
```

```json
"scripts": {
  // 构建前端/主进程，并加密
  "build": "npm run build-frontend && npm run build-electron && ee-bin encrypt",
  // 构建前端
  "build-frontend": "ee-bin build --cmds=frontend && ee-bin move --flag=frontend_dist",
  // 构建主进程
  "build-electron": "ee-bin build --cmds=electron",
  // 加密
  "encrypt": "ee-bin encrypt",
  // 构建go
  "build-go-w": "ee-bin move --flag=go_static,go_config,go_package && ee-bin build --cmds=go_w",
  "build-go-m": "ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_m",
  "build-go-l": "ee-bin move --flag=go_static,go_config,go_package,go_images && ee-bin build --cmds=go_l",
  // 构建python
  "build-python": "ee-bin build --cmds=python && ee-bin move --flag=python_dist",
  // 生成可执行程序
  "build-w": "ee-bin build --cmds=win64",
  "build-we": "ee-bin build --cmds=win_e",
  "build-m": "ee-bin build --cmds=mac",
  "build-m-arm64": "ee-bin build --cmds=mac_arm64",
  "build-l": "ee-bin build --cmds=linux",
}
```
