'use strict';

const { app: electronApp, screen } = require('electron');
const { logger } = require('ee-core/log');
const { getConfig } = require('ee-core/config');
const { getMainWindow } = require('ee-core/electron');
// const sqlitedbService = require('ee-core/services/sqlitedb');
const { sqlitedbService } = require('../service/database/sqlitedb');
// 导入WebSocket服务
const { webSocketService } = require('../service/webSocketService');

class Lifecycle {

  /**
   * core app have been loaded
   */
  async ready() {
    logger.info('[lifecycle] core app loaded (ready)');

    // 在这里初始化应用程序级服务
    logger.info('[lifecycle] Initializing services...');
    try {
      // trayService.create(); // 暂时注释掉其他服务，聚焦数据库问题
      // logger.info('[lifecycle] trayService created.');

      // securityService.create();
      // logger.info('[lifecycle] securityService created.');

      // autoUpdaterService.create();
      // logger.info('[lifecycle] autoUpdaterService created.');

      logger.info('[lifecycle] About to initialize sqlitedbService...');
      await sqlitedbService.init(); // 如果 init 是异步的，使用 await (当前不是异步的，但await无害)
      logger.info('[lifecycle] sqlitedbService.init() call completed.');
      logger.info('[lifecycle] Checking sqlitedbService.db AFTER init in lifecycle:', sqlitedbService.db ? 'DB instance exists' : 'DB instance is UNDEFINED');

      // 启动WebSocket服务
      logger.info('[lifecycle] Starting WebSocket service...');
      webSocketService.start(8081); // 启动WebSocket服务，端口8081（避免与前端开发服务器冲突）
      logger.info('[lifecycle] WebSocket service started on port 8081');

      logger.info('[lifecycle] All relevant services (DB, WebSocket) initialized.');
    } catch (error) {
      logger.error('[lifecycle] Error during service initialization:', error, error.stack);
    }
  }

  /**
   * electron app ready
   */
  async electronAppReady() {
    logger.info('[lifecycle] electron-app-ready');

    // When double clicking the icon, display the opened window
    electronApp.on('second-instance', () => {
      const win = getMainWindow();
      if (win.isMinimized()) {
        win.restore();
      }
      win.show();
      win.focus();
    });
  }

  /**
   * main window have been loaded
   */
  async windowReady() {
    logger.info('[lifecycle] window-ready');

    const win = getMainWindow();

    // The window is centered and scaled proportionally
    // Obtain the size information of the main screen, calculate the width and height of the window as a percentage of the screen, 
    // and calculate the coordinates of the upper left corner when the window is centered
    const mainScreen = screen.getPrimaryDisplay();
    const { width, height } = mainScreen.workAreaSize;
    const windowWidth = Math.floor(width * 0.6);
    const windowHeight = Math.floor(height * 0.8);
    const x = Math.floor((width - windowWidth) / 2);
    const y = Math.floor((height - windowHeight) / 2);
    win.setBounds({ x, y, width: windowWidth, height: windowHeight })

    // Delayed loading, no white screen
    const { windowsOption } = getConfig();
    if (windowsOption.show == false) {
      win.once('ready-to-show', () => {
        win.show();
        win.focus();
      })
    }
  }

  /**
   * before app close
   */  
  async beforeClose() {
    logger.info('[lifecycle] before-close');
    
    // 关闭WebSocket服务
    try {
      logger.info('[lifecycle] Stopping WebSocket service...');
      webSocketService.stop();
      logger.info('[lifecycle] WebSocket service stopped');
    } catch (error) {
      logger.error('[lifecycle] Error stopping WebSocket service:', error);
    }
  }
}
Lifecycle.toString = () => '[class Lifecycle]';

module.exports = {
  Lifecycle
};