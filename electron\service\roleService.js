'use strict'

// const Service = require('ee-core').Service; // 移除这一行
const { sqlitedbService } = require('./database/sqlitedb') // 确保路径正确

/**
 * 角色服务
 * @class
 */
class RoleService {
  // 移除 extends Service

  constructor(ctx) {
    // super(ctx); // 移除这一行
    this.ctx = ctx // 可以选择保留 ctx 供服务内部使用，如果需要的话
    this.db = sqlitedbService.db // 直接访问 better-sqlite3 实例
    this.tableName = sqlitedbService.appRolesTableName
  }

  /**
   * 获取所有角色
   * @returns {Promise<Array<Object>>}
   */
  async list() {
    const stmt = this.db.prepare(`SELECT * FROM ${this.tableName} ORDER BY id ASC`)
    const roles = stmt.all()
    // 解析 permissions 字符串为 JSON 对象
    return {
      success: true,
      data: roles.map(role => ({
        ...role,
        permissions: role.permissions ? JSON.parse(role.permissions) : []
      }))
    }
  }

  /**
   * 根据ID获取角色
   * @param {number} id - 角色ID
   * @returns {Promise<Object|null>}
   */
  async getById(id) {
    const stmt = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE id = ?`)
    const role = stmt.get(id)
    if (role) {
      return {
        ...role,
        permissions: role.permissions ? JSON.parse(role.permissions) : []
      }
    }
    return null
  }

  async getByIds(id) {
    // 将id从JSON字符串转换为数组
    const ids = JSON.parse(id)

    // 确保ids是一个数组
    if (!Array.isArray(ids)) {
      throw new Error('role_id 不是有效的数组格式')
    }

    // 如果ids是一个数组，使用IN语句查询多个角色
    const placeholders = ids.map(() => '?').join(',') // 生成占位符 '?, ?, ?'
    const stmt = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE id IN (${placeholders})`)

    const roles = stmt.all(...ids) // 使用角色ID数组来获取多个角色

    // 如果查询到角色数据，处理每个角色的权限
    if (roles.length > 0) {
      return roles.map(role => ({
        ...role,
        permissions: role.permissions ? JSON.parse(role.permissions) : []
      }))
    }
    return null // 如果没有找到角色
  }

  /**
   * 根据名称获取角色
   * @param {string} name - 角色名称
   * @returns {Promise<Object|null>}
   */
  async getByName(name) {
    const stmt = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE name = ?`)
    const role = stmt.get(name)
    if (role) {
      return {
        ...role,
        permissions: role.permissions ? JSON.parse(role.permissions) : []
      }
    }
    return null
  }

  /**
   * 创建新角色
   * @param {Object} roleData - 角色数据
   * @param {string} roleData.name - 角色名称
   * @param {string} [roleData.description] - 角色描述
   * @param {Array<string>} [roleData.permissions] - 权限列表
   * @returns {Promise<Object>}
   */
  async create(_params) {
    // <--- 无参数，依赖 ctx
    // const { ctx } = this;
    // const { name, description, permissions } = ctx.request.body;
    const { name, description, permissions } = _params

    const permissionsJson = permissions ? JSON.stringify(permissions) : '[]'

    const stmt = this.db.prepare(`
      INSERT INTO ${this.tableName} (name, description, permissions) 
      VALUES (?, ?, ?)
    `)

    try {
      // 插入
      const result = stmt.run(name, description, permissionsJson)

      const newRole = {
        id: result.lastInsertRowid, // 获取新插入行的ID
        name,
        description,
        permissions: permissions ? permissions : [] // 确保返回的 permissions 是一个对象
      }

      return newRole
    } catch (error) {
      throw new Error(`Failed to create new role: ${error.message}`)
    }

    // try {
    //   const newRole = await this.roleService.create({ name, description, permissions });
    //   ctx.body = { success: true, data: newRole };
    //   ctx.status = 201;
    //   return ctx.body;
    // } catch (error) {
    //   ctx.body = { success: false, message: error.message };
    // }
  }

  /**
   * 更新角色
   * @param {number} id - 角色ID
   * @param {Object} roleData - 需要更新的角色数据
   * @param {string} [roleData.name] - 角色名称
   * @param {string} [roleData.description] - 角色描述
   * @param {Array<string>} [roleData.permissions] - 权限列表
   * @returns {Promise<Object|null>}
   */
  async update(id, roleData) {
    const existingRole = await this.getById(id)
    if (!existingRole) return null

    const { name = existingRole.name, description = existingRole.description, permissions } = roleData
    const permissionsJson = permissions ? JSON.stringify(permissions) : JSON.stringify(existingRole.permissions)

    const stmt = this.db.prepare(
      `UPDATE ${this.tableName} 
       SET name = @name, description = @description, permissions = @permissions, updated_at = datetime('now','localtime') 
       WHERE id = @id`
    )
    const result = stmt.run({ id, name, description, permissions: permissionsJson })
    if (result.changes > 0) {
      const updatedRole = await this.getById(roleData.id)
      return { success: true, data: updatedRole }
    }
    return null // 未更新任何行
  }

  /**
   * 删除角色
   * @param {number} id - 角色ID
   * @returns {Promise<boolean>} - 是否成功删除
   */
  async delete(id) {
    // 注意：删除角色前应考虑是否有用户仍关联此角色
    // 根据纲要，用户与角色是多对一，如果角色被删除，用户的 role_id 可能需要处理（例如设为NULL或默认角色）
    // CREATE TABLE 中定义了 ON DELETE SET NULL，所以数据库层面会自动处理
    const stmt = this.db.prepare(`DELETE FROM ${this.tableName} WHERE id = ?`)
    const result = stmt.run(id)
    return result.changes > 0
  }
}

module.exports = RoleService
