<template>
  <a-layout id="app-layout-sider" class="app-layout-container">
    <a-layout-sider v-model:collapsed="collapsed" theme="light" class="layout-sider" width="200">
      <div class="logo">
        <img class="pic-logo" src="~@/assets/logo.png" alt="Logo" />
      </div>
      <div class="menu-toggle-wrapper">
        <a-button type="text" class="menu-toggle-btn" @click="toggleCollapsed">
          <MenuUnfoldOutlined v-if="collapsed" />
          <MenuFoldOutlined v-else />
        </a-button>
      </div>
      <a-menu class="menu-item" theme="light" mode="inline" :selectedKeys="[selectedKey]" @click="menuHandle" v-model:openKeys="openKeys" :inline-collapsed="collapsed">
        <a-sub-menu v-for="menuItem in mainMenu" :key="menuItem.name">
          <template v-if="menuItem.children" #title>
            <icon-font v-if="menuItem.meta.icon" :type="menuItem.meta.icon" />
            <component v-else-if="menuItem.meta.antIcon" :is="menuItem.meta.antIcon" />
            <span>{{ menuItem.meta.title }}</span>
          </template>
          <a-menu-item v-for="itemmenu in menuItem.children" :key="itemmenu.name">
              <icon-font v-if="itemmenu.meta.icon" :type="itemmenu.meta.icon" />
              <component v-else-if="itemmenu.meta.antIcon" :is="itemmenu.meta.antIcon" />
              <span>{{ itemmenu.meta.title }}</span>
          </a-menu-item>
        </a-sub-menu>
      </a-menu>
      <info :collapsed="collapsed" />
    </a-layout-sider>
    
    <a-layout class="main-layout">
      <a-layout-content class="layout-content">
        <router-view />
      </a-layout-content>
    </a-layout>
    
    <!-- AI聊天组件作为右侧边栏 -->
    <AiChat />
  </a-layout>
</template>

<script setup>
  import { ref, reactive, computed, onMounted, watch } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  // import constantRouterMap from '@/router/routerMap'; // 导入路由配置
  import constantRouterMap from '@/router/index.js'; // 导入路由配置
  import { 
    TableOutlined, 
    TeamOutlined, 
    UserOutlined, 
    FileImageOutlined, 
    FolderOutlined, 
    FileMarkdownOutlined, 
    SolutionOutlined, 
    FileWordOutlined, 
    SettingOutlined, 
    AppstoreOutlined, 
    DesktopOutlined, 
    ExperimentOutlined, 
    GatewayOutlined,
    MenuFoldOutlined,
    MenuUnfoldOutlined
  } from '@ant-design/icons-vue'; // 导入一些Ant Design图标
  import { Modal, message } from 'ant-design-vue';
  import info from '@/layouts/components/UserInfo.vue'
  import AiChat from '@/layouts/components/AiChat/index.vue'
  const router = useRouter();
  const route = useRoute();

// 菜单缩起状态管理
const collapsed = ref(false); // 默认不折叠，可以根据需要调整
const selectedKey = ref('');
const selectedKeyName = ref('')
const openKeys = ref([])
// 记录展开前的openKeys状态，用于展开时恢复
const preOpenKeys = ref([])

// 手动切换缩起展开状态
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value;
  // 如果缩起，清空openKeys；如果展开，恢复之前的openKeys
  openKeys.value = collapsed.value ? [] : preOpenKeys.value;
};

// // 监听openKeys变化，记录展开前的状态
// watch(
//   () => openKeys.value,
//   (newVal, oldVal) => {
//     if (!collapsed.value && oldVal) {
//       preOpenKeys.value = oldVal;
//     }
//   }
// );

// // 监听路由变化，当路径包含bidding-document/files/edit/时自动缩起菜单
// watch(
//   () => route.path,
//   (currentPath) => {
//     // 如果路由路径包含bidding-document/files/edit/且菜单处于展开状态，则缩起菜单
//     if (currentPath.includes('bidding-document/files/edit/') && !collapsed.value) {
//       collapsed.value = true;
//       openKeys.value = [];
//     }
//   },
//   { immediate: true }
// );

// 从路由配置动态生成主菜单
const mainMenu = computed(() => {
  const mainLayoutRoute = constantRouterMap.getRoutes().find(r => r.path === '/');
  if (mainLayoutRoute && mainLayoutRoute.children) {
    return mainLayoutRoute.children.filter(
      child => child.meta && child.meta.title && !child.meta.hidden // meta.title 存在且 meta.hidden 不为 true
    ).map(child => ({
      name: child.name,
      path: child.path,
      children: child.children.filter(child => child.meta && child.meta.showInMenu !== false ),
      meta: {
        ...child.meta,
        // 尝试匹配 Ant Design 图标，如果路由元信息中有antIcon字符串
        antIcon: child.meta.antIcon ? getAntIconComponent(child.meta.antIcon) : null
      }
    }));
  }
  return [];
});

// 辅助函数，根据字符串名称获取Ant Design图标组件
// 注意：这里只列举了几个示例，实际项目中可能需要更完善的映射或动态导入
function getAntIconComponent(iconName) {
  const icons = {
    TableOutlined, // 模版字段
    UserOutlined, // 用户管理
    TeamOutlined, // 角色管理
    FileImageOutlined, // 方案制作
    FileMarkdownOutlined,// 模版管理
    FolderOutlined, // 文件管理
    SolutionOutlined, // 招标方案
    FileWordOutlined, // 招标文件
    SettingOutlined, // 系统设置
    AppstoreOutlined, // 框架功能
    DesktopOutlined,  // 系统交互
    ExperimentOutlined, // 界面效果
    GatewayOutlined, // 跨语言调用
  };
  return icons[iconName];
}

// 更新 selectedKey 当路由变化时
watch(
  () => route.name,
  (routeName) => {
    // 尝试找到当前路由对应的顶级菜单项
    const topLevelRoute = mainMenu.value.find(item => {
      if (routeName === item.name) return true;
      // 检查子路由
      // const mainRoute = constantRouterMap.find(r => r.name === item.name);
      const mainRoute = constantRouterMap.getRoutes().find(r => r.name === item.name);
      if (mainRoute && mainRoute.children) {
        return mainRoute.children.some(child => checkSubRoute(child, routeName));
      }
      return false;
    });
    // console.log("1",topLevelRoute)
    if (topLevelRoute) {
      openKeys.value = [topLevelRoute.name]
      if(topLevelRoute.children){
        let arr = topLevelRoute.children.filter(child => child.name == selectedKeyName.value);
        // console.log("2",arr)
        if(arr.length > 0){
          selectedKey.value = arr[0].name;
        }else{
          selectedKey.value = topLevelRoute.children[0].name;
        }
        // console.log("3",openKeys.value)
      }
    } else {
      // 如果当前路由不是任何主菜单项或其子项，则尝试从路径匹配
      // 这对于 /tender-scheme/templates/edit/1 这样的路由很重要
      const currentTopPath = '/' + route.path.split('/')[1]; // 获取路径的第一部分, e.g., /tender-scheme
      const matchedMainMenu = mainMenu.value.find(item => item.path === currentTopPath );
      if (matchedMainMenu) {
        selectedKey.value = matchedMainMenu.name;
      } else if (mainMenu.value.length > 0 && !selectedKey.value) {
        // 如果没有匹配到，并且selectedKey为空，默认选择第一个 (或者重定向的那个)
        const defaultRouteName = router.resolve(constantRouterMap.getRoutes().find(r => r.path === '/').redirect)?.name;
        const defaultMenu = mainMenu.value.find(m => m.name === defaultRouteName);
        if (defaultMenu) {
          selectedKey.value = defaultMenu.name;
        } else if (mainMenu.value.length > 0) {
          selectedKey.value = mainMenu.value[0].name;
        }
      }}
    },
    { immediate: true }
  );

  // 递归检查子路由名称 (辅助 watch)
  function checkSubRoute(routeItem, targetName) {
    if (routeItem.name === targetName) return true;
    if (routeItem.children) {
      return routeItem.children.some(child => checkSubRoute(child, targetName));
    }
    return false;
  }

  function menuHandle(e) {
    if (e && e.key) {
      // selectedKey.value = e.key; // Ant Design Menu的key现在是路由的name
      selectedKeyName.value = e.key
      // console.log("4",selectedKey.value)
      router.push({ name: e.key });
    }
  }

  // 初始化时，根据当前路由设置选中的菜单项
  onMounted(() => {
    // 确保 selectedKey 已经通过 watch 设置
    if (!selectedKey.value && mainMenu.value.length > 0) {
      const defaultRouteNameFromRedirect = router.resolve(constantRouterMap.find(r => r.path === '/').redirect)?.name;
      const defaultMenu = mainMenu.value.find(m => m.name === defaultRouteNameFromRedirect);
      if (defaultMenu) {
        selectedKey.value = defaultMenu.name;
         if (route.name !== defaultMenu.name && !route.matched.some(m => m.name === defaultMenu.name) ) {
            // 如果当前路由不是默认路由，也不在其子路由下，则跳转到默认路由
            const currentMainLayoutRoute = constantRouterMap.getRoutes().find(r => r.path === '/');
            if (currentMainLayoutRoute && currentMainLayoutRoute.redirect) {
                 router.push(currentMainLayoutRoute.redirect);
            }
        }
      } else {
        selectedKey.value = mainMenu.value[0].name; // Fallback to first item
        if (route.name !== mainMenu.value[0].name && !route.matched.some(m => m.name === mainMenu.value[0].name)) {
          router.push({ name: mainMenu.value[0].name });
        }
      }
    } else if (selectedKey.value && route.name !== selectedKey.value && !route.matched.some(m => m.name === selectedKey.value)) {
      // 如果 onMounted 时，当前路由和 selectedKey 不匹配（例如直接访问深层链接），则跳转
      // router.push({ name: selectedKey.value }); // 这行会导致循环跳转或不必要的跳转，注释掉，依赖watch处理
    }
  });

</script>

<style lang="less" scoped>
.app-layout-container {
  display: flex;
  height: 100vh;
  width: 100%;
}

.main-layout {
  flex: 1;
  min-width: 0; /* 确保内容可以被挤压 */
}

.layout-content {
  padding: 16px;
  background-color: #f0f2f5;
  height: 100%;
  overflow: auto;
}

/* 确保左侧菜单栏的样式 */
.layout-sider {
  // flex-shrink: 0;
  width: 200px !important;
  border-right: 1px solid #e8e8e8;
  background: #fff;
  transition: all 0.2s;
  
  // 缩起状态下的宽度调整
  &.ant-layout-sider-collapsed {
    width: 80px !important;
    min-width: 80px !important;
  }
}

/* 响应式处理 */
@media (max-width: 768px) {
  .app-layout-container {
    position: relative;
  }
}

#app-layout-sider {
  .personal-menu {
    // min-width: 140px;
    min-width: 200px;

    .ant-menu-item {
      display: flex;
      align-items: center;
      font-size: 15px;
      padding: 8px 18px;

      .anticon {
        margin-right: 8px;
        font-size: 16px;
        color: #1890ff;
      }
    }
  }

  .logo {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    padding: 5px;
    transition: all 0.2s;
    
    .pic-logo {
      max-height: 40px;
      max-width: 100%;
      transition: all 0.2s;
    }
  }
  
  // 缩起状态下logo的调整
  .ant-layout-sider-collapsed .logo {
    .pic-logo {
      max-height: 30px;
    }
  }

  // 缩起展开按钮样式
  .menu-toggle-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.2s;
    
    .menu-toggle-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 32px;
      height: 32px;
      border-radius: 4px;
      color: #666;
      transition: all 0.3s;
      
      &:hover {
        background-color: #f5f5f5;
        color: #1890ff;
      }
      
      .anticon {
        font-size: 16px;
      }
    }
  }
  
  // 缩起状态下的按钮样式调整
  .ant-layout-sider-collapsed .menu-toggle-wrapper {
    padding: 8px 4px;
    
    .menu-toggle-btn {
      width: 28px;
      height: 28px;
      
      .anticon {
        font-size: 14px;
      }
    }
  }

  .menu-item {
    .ant-menu-item {
      margin-top: 0px;
      margin-bottom: 0px;
    }

    .ant-menu-item span {
      vertical-align: middle;
    }

    .anticon {
      margin-right: 8px;
      vertical-align: middle;
    }
  }
}
</style>