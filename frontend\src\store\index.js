import { createStore } from 'vuex';
import auth from './modules/auth';
// import tenderSchemeTemplate from './modules/tenderSchemeTemplate'; // 已移除
// import tenderScheme from './modules/tenderScheme'; // 将被移除

export default createStore({
  modules: {
    auth,
    // tenderSchemeTemplate, // 已移除
    // tenderScheme, // 将被移除
    // 在这里可以添加其他 Vuex 模块
  },
  // strict: process.env.NODE_ENV !== 'production', // 可选：严格模式，确保状态更改只能由mutation进行
}); 