<template>
  <div>
    <!-- class="page-container" -->
    <a-card title="招标方案模板">
      <!-- <a-page-header title="招标方案模板" /> -->
      <!-- <a-card :bordered="false"> -->
      <!-- 搜索与操作区域 -->
      <div class="flex justify-between mb-[16px]">
        <a-form layout="inline" :model="searchForm" class="gap-y-[10px]">
          <a-form-item label="模板名称">
            <a-input v-model:value="searchForm.name" placeholder="请输入模板名称" @pressEnter="handleSearch" />
          </a-form-item>
          <a-form-item label="方案类型">
            <a-select v-model:value="searchForm.type" placeholder="请选择方案类型" style="width: 180px" allow-clear @change="handleSearch">
              <a-select-option v-for="item in schemeTypes" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
            <a-button style="margin-left: 8px" @click="resetSearchForm">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-form-item>
        </a-form>
        <a-button type="primary" @click="handleAddNew">
          <template #icon><PlusOutlined /></template>
          新建模板
        </a-button>
      </div>
      <!-- 表格区域 -->
      <a-table row-key="id" :columns="columns" :data-source="templates" :pagination="paginationConfig" :loading="isLoading" @change="handleTableChange">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            {{ formatSchemeType(record.type) }}
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="handleEdit(record)">编辑</a-button>
              <a-popconfirm title="确定删除此模板吗?" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record.id)">
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
      <!-- </a-card> -->
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { SearchOutlined, ReloadOutlined, PlusOutlined } from '@ant-design/icons-vue'
import api from '@/api'

// 从项目纲要中获取方案类型
const schemeTypes = ref([
  { value: 1, label: '服务类' },
  { value: 2, label: '工程类' },
  { value: 3, label: '物资类' },
  { value: 4, label: '加油站' }
])

const router = useRouter()

// 表格列定义
const columns = [
  { title: '模板名称', dataIndex: 'name', key: 'name' },
  { title: '方案类型', dataIndex: 'type', key: 'type', width: '150px' },
  { title: '备注', dataIndex: 'remark', key: 'remark' },
  { title: '创建时间', dataIndex: 'created_at', key: 'created_at', width: '180px' },
  { title: '更新时间', dataIndex: 'updated_at', key: 'updated_at', width: '180px' },
  { title: '操作', key: 'action', width: '150px', fixed: 'right' }
]

// 搜索表单
const searchForm = reactive({
  name: '',
  type: undefined
})

// 本地状态管理
const templates = ref([])
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})
const isLoading = ref(false)

// 分页配置
const paginationConfig = computed(() => ({
  total: pagination.value.total,
  current: pagination.value.current,
  pageSize: pagination.value.pageSize,
  showSizeChanger: true,
  showQuickJumper: true,
  pageSizeOptions: ['10', '20', '50', '100'],
  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条 / 共 ${total} 条`
}))

// 加载数据
const loadTemplates = async (params = {}) => {
  isLoading.value = true
  const queryParams = {
    page: params.page || pagination.value.current,
    pageSize: params.pageSize || pagination.value.pageSize,
    name: searchForm.name || undefined,
    type: searchForm.type === null || searchForm.type === '' ? undefined : searchForm.type
  }
  try {
    const response = await api.tenderSchemeTemplate.list(queryParams)
    if (response && response.success && response.data) {
      templates.value = response.data.list
      pagination.value = {
        ...pagination.value,
        current: response.data.pagination.page,
        pageSize: response.data.pagination.pageSize,
        total: response.data.pagination.total
      }
    } else {
      message.error(response.message || '获取模板列表失败')
      templates.value = []
      pagination.value.total = 0
    }
  } catch (error) {
    console.error('获取模板列表失败:', error)
    message.error('获取模板列表时发生错误: ' + error.message)
    templates.value = []
    pagination.value.total = 0
  } finally {
    isLoading.value = false
  }
}

// 格式化方案类型显示
const formatSchemeType = typeValue => {
  const type = schemeTypes.value.find(t => t.value === typeValue)
  return type ? type.label : '未知类型'
}

// 搜索操作
const handleSearch = () => {
  pagination.value.current = 1
  loadTemplates()
}

// 重置搜索表单
const resetSearchForm = () => {
  searchForm.name = ''
  searchForm.type = undefined
  pagination.value.current = 1
  loadTemplates()
}

// 表格变化处理（分页、排序等）
const handleTableChange = (pageInfo, filters, sorter) => {
  pagination.value.current = pageInfo.current
  pagination.value.pageSize = pageInfo.pageSize
  loadTemplates()
}

// 新建模板
const handleAddNew = () => {
  router.push({ name: 'TenderSchemeTemplateCreate' })
}

// 编辑模板
const handleEdit = record => {
  router.push({ name: 'TenderSchemeTemplateEdit', params: { id: record.id } })
}

// 删除模板
const handleDelete = async id => {
  isLoading.value = true
  try {
    const response = await api.tenderSchemeTemplate.delete({ id })
    if (response && response.success) {
      message.success(response.message || '删除成功！')
      loadTemplates()
    } else {
      message.error(response.message || '删除失败')
    }
  } catch (err) {
    message.error(err.message || '删除模板失败')
  } finally {
    isLoading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadTemplates()
})

// 如果使用了 keep-alive，当组件被激活时可能需要重新加载数据
// onActivated(() => {
//   console.log('TemplateList activated, consider re-fetching data if necessary');
//   loadTemplates();
// });
</script>

<style scoped>
/* 可以在这里添加更多自定义样式 */
</style>
