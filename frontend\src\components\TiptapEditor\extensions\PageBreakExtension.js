import { Node, mergeAttributes } from '@tiptap/core'

/**
 * 分页符扩展 PageBreakExtension
 * 该扩展用于在 Tiptap 编辑器中插入分页符，在DOCX导出时会转换为真正的分页符。
 */
export const PageBreakExtension = Node.create({
  name: 'pageBreak',
  group: 'block', // 块级元素
  inline: false,
  atom: true, // 原子节点，不可编辑内容
  selectable: true, // 可选中
  
  addAttributes() {
    return {
      // 可以添加一些属性，比如分页符类型等
      breakType: {
        default: 'page',
        parseHTML: element => element.getAttribute('data-break-type'),
        renderHTML: attributes => ({
          'data-break-type': attributes.breakType
        })
      }
    }
  },

  parseHTML() {
    return [
      {
        tag: 'div[data-page-break]'
      }
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return [
      'div', 
      mergeAttributes(HTMLAttributes, { 
        'data-page-break': '',
        'style': 'page-break-before: always; border-top: 2px dashed #ccc; margin: 16px 0; padding: 8px 0; text-align: center; color: #666; font-size: 12px;'
      }),
      '--- 分页符 ---'
    ]
  },

  addCommands() {
    return {
      insertPageBreak: () => ({ commands }) => {
        return commands.insertContent({
          type: this.name,
          attrs: { breakType: 'page' }
        })
      }
    }
  }
})

export default PageBreakExtension 