'use strict'

const BiddingDocumentTemplateService = require('../service/biddingDocumentTemplateService')

/**
 * 招标文件模板控制器
 * @class
 */
class BiddingDocumentTemplateController {
  constructor(ctx) {
    this.biddingDocumentTemplateService = new BiddingDocumentTemplateService()
  }

  /**
   * 创建招标文件模板
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async create(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    try {
      const result = await this.biddingDocumentTemplateService.create(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取招标文件模板列表
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async list(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {}

    try {
      const result = await this.biddingDocumentTemplateService.list(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取招标文件模板树形结构
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async getTree(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {}

    try {
      // 提取parentId参数，默认为null表示获取顶级模板
      const parentId = params.parentId ? parseInt(params.parentId, 10) : null
      const result = this.biddingDocumentTemplateService.getTree(parentId)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取单个招标文件模板
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async getById(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的模板ID' }
    }

    try {
      const result = await this.biddingDocumentTemplateService.getById(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 更新招标文件模板
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async update(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    const { id, ...updateData } = params
    const templateId = parseInt(id)

    if (isNaN(templateId)) {
      return { success: false, message: '无效的模板ID' }
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return { success: false, message: '没有提供需要更新的数据' }
    }

    try {
      const result = await this.biddingDocumentTemplateService.update(templateId, updateData)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 删除招标文件模板
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async delete(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的模板ID' }
    }

    try {
      const result = await this.biddingDocumentTemplateService.delete(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
}

BiddingDocumentTemplateController.toString = () => '[class BiddingDocumentTemplateController]'

module.exports = BiddingDocumentTemplateController
