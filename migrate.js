#!/usr/bin/env node

/**
 * 数据库迁移工具
 * 用于将数据库从 v1.0 迁移到 v2.0
 * 
 * 使用方法：
 * node migrate.js [数据库文件路径]
 * 
 * 示例：
 * node migrate.js data/sqlite-app.db
 */

const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');

class DatabaseMigrator {
  constructor() {
    this.dbPath = null;
    this.db = null;
    this.migrationSqlPath = path.join(__dirname, 'migration_v1_to_v2.sql');
  }

  /**
   * 初始化迁移器
   * @param {string} dbPath - 数据库文件路径
   */
  async init(dbPath) {
    this.dbPath = dbPath;
    
    // 检查数据库文件是否存在
    if (!fs.existsSync(dbPath)) {
      throw new Error(`数据库文件不存在: ${dbPath}`);
    }

    // 检查迁移脚本是否存在
    if (!fs.existsSync(this.migrationSqlPath)) {
      throw new Error(`迁移脚本不存在: ${this.migrationSqlPath}`);
    }

    console.log(`✓ 数据库文件: ${dbPath}`);
    console.log(`✓ 迁移脚本: ${this.migrationSqlPath}`);
  }

  /**
   * 检查数据库版本
   */
  async checkDatabaseVersion() {
    this.db = new Database(this.dbPath);
    
    try {
      // 检查是否存在版本表
      const versionTableExists = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='db_version'
      `).get();

      if (versionTableExists) {
        const version = this.db.prepare('SELECT version FROM db_version ORDER BY migration_date DESC LIMIT 1').get();
        return version ? version.version : 'unknown';
      }

      // 检查是否存在旧的风险评估表
      const oldRiskTablesExist = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND (name='risk_assessment_tasks' OR name='risk_assessment_results')
      `).all();

      // 检查是否存在新的风险评估表
      const newRiskTableExists = this.db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='risk_assessment'
      `).get();

      if (oldRiskTablesExist.length > 0 && !newRiskTableExists) {
        return 'v1.0';
      } else if (newRiskTableExists) {
        return 'v2.0';
      }

      return 'unknown';
    } catch (error) {
      console.error('检查数据库版本时出错:', error);
      return 'error';
    }
  }

  /**
   * 备份数据库文件
   */
  async backupDatabase() {
    const backupPath = `${this.dbPath}.backup.${Date.now()}`;
    
    try {
      fs.copyFileSync(this.dbPath, backupPath);
      console.log(`✓ 数据库已备份到: ${backupPath}`);
      return backupPath;
    } catch (error) {
      throw new Error(`备份数据库失败: ${error.message}`);
    }
  }

  /**
   * 执行迁移
   */
  async performMigration() {
    try {
      // 读取迁移脚本
      const migrationSql = fs.readFileSync(this.migrationSqlPath, 'utf8');
      
      console.log('开始执行数据库迁移...');
      
      // 执行迁移脚本
      this.db.exec(migrationSql);
      
      console.log('✓ 数据库迁移执行完成');
      
      // 验证迁移结果
      await this.validateMigration();
      
    } catch (error) {
      throw new Error(`迁移执行失败: ${error.message}`);
    }
  }

  /**
   * 验证迁移结果
   */
  async validateMigration() {
    try {
      // 检查新表是否存在
      const requiredTables = [
        'risk_assessment',
        'tender_schemes', 
        'bidding_documents',
        'tender_scheme_history',
        'bidding_document_history',
        'bidding_document_template_contents'
      ];

      for (const tableName of requiredTables) {
        const tableExists = this.db.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name=?
        `).get(tableName);

        if (!tableExists) {
          throw new Error(`必需的表 ${tableName} 不存在`);
        }
      }

      // 检查数据完整性
      const stats = this.db.prepare(`
        SELECT 
          (SELECT COUNT(*) FROM risk_assessment) as risk_count,
          (SELECT COUNT(*) FROM tender_schemes) as scheme_count,
          (SELECT COUNT(*) FROM bidding_documents) as document_count,
          (SELECT COUNT(*) FROM tender_scheme_history) as scheme_history_count,
          (SELECT COUNT(*) FROM bidding_document_history) as document_history_count,
          (SELECT COUNT(*) FROM bidding_document_template_contents) as template_content_count
      `).get();

      console.log('\n✓ 迁移验证通过');
      console.log('数据统计:');
      console.log(`  - 风险评估记录: ${stats.risk_count}`);
      console.log(`  - 招标方案记录: ${stats.scheme_count}`);
      console.log(`  - 招标文件记录: ${stats.document_count}`);
      console.log(`  - 方案历史版本: ${stats.scheme_history_count}`);
      console.log(`  - 文件历史版本: ${stats.document_history_count}`);
      console.log(`  - 模板段落内容: ${stats.template_content_count}`);

      // 检查版本信息
      const version = this.db.prepare('SELECT * FROM db_version ORDER BY migration_date DESC LIMIT 1').get();
      if (version) {
        console.log(`✓ 当前数据库版本: ${version.version} (${version.migration_date})`);
      }

    } catch (error) {
      throw new Error(`迁移验证失败: ${error.message}`);
    }
  }

  /**
   * 关闭数据库连接
   */
  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }

  /**
   * 执行完整的迁移流程
   * @param {string} dbPath - 数据库文件路径
   */
  async migrate(dbPath) {
    let backupPath = null;
    
    try {
      // 1. 初始化
      await this.init(dbPath);
      
      // 2. 检查版本
      const currentVersion = await this.checkDatabaseVersion();
      console.log(`当前数据库版本: ${currentVersion}`);
      
      if (currentVersion === 'v2.0') {
        console.log('✓ 数据库已经是最新版本，无需迁移');
        return;
      }
      
      if (currentVersion !== 'v1.0') {
        throw new Error(`不支持从版本 ${currentVersion} 迁移，请检查数据库状态`);
      }
      
      // 3. 备份数据库
      backupPath = await this.backupDatabase();
      
      // 4. 执行迁移
      await this.performMigration();
      
      console.log('\n🎉 数据库迁移成功完成！');
      console.log(`备份文件已保存在: ${backupPath}`);
      
    } catch (error) {
      console.error('\n❌ 迁移失败:', error.message);
      
      if (backupPath && fs.existsSync(backupPath)) {
        console.log(`\n您可以使用以下命令恢复备份:`);
        console.log(`cp "${backupPath}" "${dbPath}"`);
      }
      
      process.exit(1);
      
    } finally {
      this.close();
    }
  }
}

// 命令行入口
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('数据库迁移工具 v2.0');
    console.log('');
    console.log('使用方法:');
    console.log('  node migrate.js <数据库文件路径>');
    console.log('');
    console.log('示例:');
    console.log('  node migrate.js data/sqlite-app.db');
    console.log('  node migrate.js ./data/sqlite-app.db');
    process.exit(1);
  }
  
  const dbPath = args[0];
  const migrator = new DatabaseMigrator();
  
  console.log('='.repeat(50));
  console.log('数据库迁移工具 v1.0 → v2.0');
  console.log('='.repeat(50));
  
  await migrator.migrate(dbPath);
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('程序执行失败:', error);
    process.exit(1);
  });
}

module.exports = DatabaseMigrator; 