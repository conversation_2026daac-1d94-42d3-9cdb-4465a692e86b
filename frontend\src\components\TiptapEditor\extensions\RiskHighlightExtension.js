import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from 'prosemirror-state'
import { Decoration, DecorationSet } from 'prosemirror-view'

/**
 * 根据风险等级获取样式
 * @param {string} riskLevel - 风险等级：高、中、低
 * @returns {string} CSS样式字符串
 */
const getRiskStyle = riskLevel => {
  const styles = {
    高: 'background-color: rgba(255, 77, 79, 0.2); border: 1px solid #ff4d4f; border-radius: 4px;',
    中: 'background-color: rgba(255, 165, 0, 0.2); border: 1px solid #ffa500; border-radius: 4px;',
    低: 'background-color: rgba(255, 255, 0, 0.2); border: 1px solid #ffff00; border-radius: 4px;'
  }
  return styles[riskLevel] || styles['中']
}

/**
 * 创建风险装饰
 * @param {Object} doc - ProseMirror文档对象
 * @param {Array} riskData - 风险数据数组
 * @returns {DecorationSet} 装饰集合
 */
const createRiskDecorations = (doc, riskData) => {
  try {
    const decorations = []

    if (!doc || !Array.isArray(riskData)) {
      return DecorationSet.empty
    }

    // 遍历文档查找customField节点
    doc.descendants((node, pos) => {
      try {
        if (node.type.name === 'customField' && node.attrs && node.attrs.field_key) {
          // 查找该字段是否有风险
          const fieldRisk = riskData.find(risk => risk && risk.field_key === node.attrs.field_key)

          if (fieldRisk) {
            // 创建高亮装饰
            const decoration = Decoration.node(pos, pos + node.nodeSize, {
              class: `risk-highlight risk-level-${fieldRisk.risk_level || '中'}`,
              'data-risk-level': fieldRisk.risk_level || '中',
              'data-field-key': fieldRisk.field_key,
              'data-risk-reason': fieldRisk.reason || '',
              'data-risk-suggestion': fieldRisk.suggestion || '',
              style: getRiskStyle(fieldRisk.risk_level || '中')
            })

            decorations.push(decoration)
          }
        }
      } catch (nodeError) {
        console.error('Error processing node for risk decoration:', nodeError)
      }
    })

    return DecorationSet.create(doc, decorations)
  } catch (error) {
    console.error('Error creating risk decorations:', error)
    return DecorationSet.empty
  }
}

/**
 * 查找指定位置的风险数据
 * @param {Object} doc - ProseMirror文档对象
 * @param {number} pos - 位置
 * @param {Array} riskDataArray - 风险数据数组
 * @returns {Object|null} 风险数据对象或null
 */
const findRiskAtPosition = (doc, pos, riskDataArray) => {
  try {
    let riskData = null

    if (!doc || typeof pos !== 'number' || !Array.isArray(riskDataArray)) {
      return null
    }

    doc.nodesBetween(pos, pos, node => {
      try {
        if (node.type.name === 'customField' && node.attrs && node.attrs.field_key) {
          const fieldKey = node.attrs.field_key
          // 从传入的风险数据中查找匹配的字段
          const foundRisk = riskDataArray.find(risk => risk && risk.field_key === fieldKey)
          if (foundRisk) {
            riskData = foundRisk
          }
        }
      } catch (nodeError) {
        console.error('Error processing node in findRiskAtPosition:', nodeError)
      }
    })

    return riskData
  } catch (error) {
    console.error('Error finding risk at position:', error)
    return null
  }
}

/**
 * 风险高亮扩展
 * 用于在Tiptap编辑器中高亮显示有风险的customField
 */
export const RiskHighlightExtension = Extension.create({
  name: 'riskHighlight',

  addOptions() {
    return {
      riskData: [], // 风险数据数组
      onRiskClick: () => {} // 点击风险时的回调
    }
  },

  addProseMirrorPlugins() {
    const extension = this
    const pluginKey = new PluginKey('riskHighlight')

    return [
      new Plugin({
        key: pluginKey,
        state: {
          init() {
            return {
              decorations: DecorationSet.empty,
              riskData: extension.options.riskData || []
            }
          },
          apply: (tr, oldState) => {
            // 当文档发生变化时，重新映射装饰
            let decorations = oldState.decorations.map(tr.mapping, tr.doc)
            let riskData = oldState.riskData

            // 如果有新的风险数据，重新创建装饰
            if (tr.getMeta('updateRiskHighlights')) {
              riskData = tr.getMeta('updateRiskHighlights') || []
              decorations = createRiskDecorations(tr.doc, riskData)
            }

            return {
              decorations,
              riskData
            }
          }
        },
        props: {
          decorations(state) {
            const pluginState = pluginKey.getState(state)
            return pluginState ? pluginState.decorations : DecorationSet.empty
          },
          handleClick: (view, pos, event) => {
            try {
              // 检查点击位置是否在风险高亮区域
              const pluginState = pluginKey.getState(view.state)
              const riskData = findRiskAtPosition(view.state.doc, pos, pluginState?.riskData || [])
              if (riskData && typeof extension.options.onRiskClick === 'function') {
                extension.options.onRiskClick(riskData, event)
                return true
              }
            } catch (error) {
              console.error('Risk highlight click error:', error)
            }
            return false
          }
        }
      })
    ]
  },

  addCommands() {
    return {
      updateRiskHighlights:
        riskData =>
        ({ tr, dispatch }) => {
          if (dispatch) {
            tr.setMeta('updateRiskHighlights', riskData)
            dispatch(tr)
          }
          return true
        }
    }
  }
})

export default RiskHighlightExtension
