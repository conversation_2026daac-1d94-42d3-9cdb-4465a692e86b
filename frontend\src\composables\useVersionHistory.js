/**
 * 版本历史组合式函数
 * 提供版本历史相关的状态管理和API调用功能
 */

import { ref } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 版本历史组合式函数
 * @param {Object} options 配置选项
 * @param {Object} options.api API对象
 * @param {string} options.entityType 实体类型 ('biddingDocument' | 'tenderScheme')
 * @param {Ref} options.entityId 实体ID的响应式引用
 * @param {boolean} options.isEditMode 是否为编辑模式
 * @returns {Object} 版本历史相关的状态和方法
 */
export function useVersionHistory(options) {
  const { api, entityType, entityId, isEditMode } = options
  
  // 版本历史状态
  const versionHistoryVisible = ref(false)
  const historyVersions = ref([])
  const isLoadingHistory = ref(false)
  
  /**
   * 获取版本历史数据
   */
  const handleVersionHistory = async () => {
    if (!isEditMode || !entityId.value) {
      const entityName = entityType === 'biddingDocument' ? '文件' : '方案'
      message.warning(`请在编辑现有${entityName}时查看版本历史`)
      return
    }
    
    try {
      isLoadingHistory.value = true
      
      let response
      if (entityType === 'biddingDocument') {
        response = await api.biddingDocument.getHistory({ document_id: entityId.value })
      } else if (entityType === 'tenderScheme') {
        response = await api.tenderScheme.getHistory({ scheme_id: entityId.value })
      } else {
        throw new Error('不支持的实体类型')
      }
      
      if (response && response.success) {
        if (response.data.length === 0) {
          const entityName = entityType === 'biddingDocument' ? '文件' : '方案'
          message.info(`当前${entityName}暂无历史版本`)
          return
        }
        historyVersions.value = response.data
        versionHistoryVisible.value = true
      } else {
        message.error(response.message || '获取历史版本失败')
      }
    } catch (error) {
      console.error('获取版本历史失败:', error)
      message.error('获取版本历史时发生错误: ' + (error.message || '未知错误'))
    } finally {
      isLoadingHistory.value = false
    }
  }
  
  /**
   * 关闭版本历史对话框
   */
  const closeVersionHistory = () => {
    versionHistoryVisible.value = false
    historyVersions.value = []
  }
  
  /**
   * 重置版本历史状态
   */
  const resetVersionHistory = () => {
    versionHistoryVisible.value = false
    historyVersions.value = []
    isLoadingHistory.value = false
  }
  
  /**
   * 获取指定版本的详细信息
   * @param {string|number} versionId 版本ID
   * @returns {Promise<Object|null>} 版本详细信息
   */
  const getVersionDetail = async (versionId) => {
    if (!versionId) {
      console.error('版本ID不能为空')
      return null
    }
    
    try {
      let response
      if (entityType === 'biddingDocument') {
        response = await api.biddingDocument.getVersionDetail({ version_id: versionId })
      } else if (entityType === 'tenderScheme') {
        response = await api.tenderScheme.getVersionDetail({ version_id: versionId })
      } else {
        throw new Error('不支持的实体类型')
      }
      
      if (response && response.success) {
        return response.data
      } else {
        message.error(response.message || '获取版本详情失败')
        return null
      }
    } catch (error) {
      console.error('获取版本详情失败:', error)
      message.error('获取版本详情时发生错误: ' + (error.message || '未知错误'))
      return null
    }
  }
  
  /**
   * 恢复到指定版本
   * @param {string|number} versionId 版本ID
   * @param {Function} onSuccess 成功回调函数
   * @returns {Promise<boolean>} 是否成功
   */
  const restoreToVersion = async (versionId, onSuccess) => {
    if (!versionId) {
      console.error('版本ID不能为空')
      return false
    }
    
    try {
      let response
      if (entityType === 'biddingDocument') {
        response = await api.biddingDocument.restoreVersion({ 
          document_id: entityId.value, 
          version_id: versionId 
        })
      } else if (entityType === 'tenderScheme') {
        response = await api.tenderScheme.restoreVersion({ 
          scheme_id: entityId.value, 
          version_id: versionId 
        })
      } else {
        throw new Error('不支持的实体类型')
      }
      
      if (response && response.success) {
        const entityName = entityType === 'biddingDocument' ? '文件' : '方案'
        message.success(`${entityName}已恢复到指定版本`)
        
        // 关闭版本历史对话框
        closeVersionHistory()
        
        // 执行成功回调
        if (typeof onSuccess === 'function') {
          onSuccess(response.data)
        }
        
        return true
      } else {
        message.error(response.message || '恢复版本失败')
        return false
      }
    } catch (error) {
      console.error('恢复版本失败:', error)
      message.error('恢复版本时发生错误: ' + (error.message || '未知错误'))
      return false
    }
  }
  
  /**
   * 比较两个版本的差异
   * @param {string|number} version1Id 版本1 ID
   * @param {string|number} version2Id 版本2 ID
   * @returns {Promise<Object|null>} 版本差异信息
   */
  const compareVersions = async (version1Id, version2Id) => {
    if (!version1Id || !version2Id) {
      console.error('版本ID不能为空')
      return null
    }
    
    try {
      let response
      if (entityType === 'biddingDocument') {
        response = await api.biddingDocument.compareVersions({ 
          version1_id: version1Id, 
          version2_id: version2Id 
        })
      } else if (entityType === 'tenderScheme') {
        response = await api.tenderScheme.compareVersions({ 
          version1_id: version1Id, 
          version2_id: version2Id 
        })
      } else {
        throw new Error('不支持的实体类型')
      }
      
      if (response && response.success) {
        return response.data
      } else {
        message.error(response.message || '比较版本失败')
        return null
      }
    } catch (error) {
      console.error('比较版本失败:', error)
      message.error('比较版本时发生错误: ' + (error.message || '未知错误'))
      return null
    }
  }
  
  return {
    // 状态
    versionHistoryVisible,
    historyVersions,
    isLoadingHistory,
    
    // 方法
    handleVersionHistory,
    closeVersionHistory,
    resetVersionHistory,
    getVersionDetail,
    restoreToVersion,
    compareVersions
  }
}
