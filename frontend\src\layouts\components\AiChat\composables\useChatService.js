import { ref, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import api from '@/api'

// 导入API工具函数
import { getCurrentUserId, getApiBaseUrl } from '@/api/utils'

/**
 * AI聊天服务组合式函数
 * 负责处理所有聊天相关的业务逻辑
 */
export function useChatService() {
  // 响应式数据
  const currentSession = ref(null) // 当前活跃会话
  const messages = ref([]) // 当前会话的消息列表
  const sessions = ref([]) // 用户的所有会话
  const sending = ref(false) // 是否正在发送消息
  const streamingMessage = ref('') // 流式响应的消息内容
  const autoScroll = ref(true) // 是否自动滚动
  const inputMessage = ref('') // 输入框内容

  // 分页相关
  const hasMoreMessages = ref(false)
  const loadingMore = ref(false)
  const messageOffset = ref(0)
  const messageLimit = ref(20)

  // 流式响应控制
  const abortController = ref(null) // 用于取消流式请求

  /**
   * 创建新会话
   */
  const createNewSession = async title => {
    try {
      const userId = getCurrentUserId()
      if (!userId) {
        message.error('请先登录')
        return null
      }

      const response = await api.aiChat.createSession({
        userId,
        title: title || `新对话 ${new Date().toLocaleString()}`
      })

      if (response.success) {
        const newSession = response.data
        sessions.value.unshift(newSession)
        currentSession.value = newSession
        messages.value = []
        messageOffset.value = 0
        hasMoreMessages.value = false

        message.success('新会话创建成功')
        return newSession
      }
    } catch (error) {
      console.error('[createNewSession] 创建会话失败:', error)
      message.error('创建会话失败，请重试')
      return null
    }
  }

  /**
   * 加载会话列表
   */
  const loadSessions = async () => {
    try {
      const userId = getCurrentUserId()
      if (!userId) return

      const response = await api.aiChat.getSessionList({
        userId,
        page: 1,
        pageSize: 50,
        status: 'active'
      })

      if (response.success) {
        sessions.value = response.data.list || []
      }
    } catch (error) {
      console.error('[loadSessions] 加载会话列表失败:', error)
      message.error('加载会话列表失败')
    }
  }

  /**
   * 切换会话
   */
  const switchSession = async session => {
    currentSession.value = session
    messages.value = []
    messageOffset.value = 0
    await loadMessages()
  }

  /**
   * 加载消息列表
   */
  const loadMessages = async () => {
    if (!currentSession.value) return

    try {
      const response = await api.aiChat.getMessages({
        sessionId: currentSession.value.id,
        limit: messageLimit.value,
        offset: messageOffset.value
      })

      if (response.success) {
        const newMessages = response.data || []

        if (messageOffset.value === 0) {
          messages.value = newMessages
        } else {
          messages.value = [...newMessages, ...messages.value]
        }

        hasMoreMessages.value = newMessages.length === messageLimit.value
      }
    } catch (error) {
      console.error('[loadMessages] 加载消息失败:', error)
      message.error('加载消息失败')
    }
  }

  /**
   * 加载更多消息
   */
  const loadMoreMessages = async () => {
    if (!hasMoreMessages.value || loadingMore.value) return

    loadingMore.value = true
    messageOffset.value += messageLimit.value

    try {
      await loadMessages()
    } finally {
      loadingMore.value = false
    }
  }

  /**
   * 发送消息
   */
  const sendMessage = async () => {
    if (!inputMessage.value.trim() || sending.value) return

    const userMessage = inputMessage.value.trim()
    const tempUserMessage = {
      id: Date.now(),
      role: 'user',
      content: userMessage,
      timestamp: new Date().toISOString()
    }

    try {
      sending.value = true
      inputMessage.value = ''

      // 创建AbortController用于取消请求
      abortController.value = new AbortController()

      // 添加用户消息
      messages.value.push(tempUserMessage)
      await nextTick()

      // 创建AI消息占位符
      const tempAiMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: '',
        timestamp: new Date().toISOString(),
        isStreaming: true
      }
      messages.value.push(tempAiMessage)
      await nextTick()

      // 使用流式API
      const baseUrl = getApiBaseUrl()
      const streamUrl = `${baseUrl}/controller/aiChatController/streamChat`
      const userId = getCurrentUserId()

      if (!userId) {
        throw new Error('用户未登录')
      }

      // 发起流式请求（支持取消）
      const response = await fetch(streamUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-User-ID': userId.toString()
        },
        body: JSON.stringify({
          sessionId: currentSession.value.id,
          message: userMessage
        }),
        signal: abortController.value.signal // 添加取消信号
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      // 检查是否是SSE响应
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('text/event-stream')) {
        throw new Error('服务器未返回流式响应')
      }

      // 处理流式响应
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          // 检查是否被取消
          if (abortController.value.signal.aborted) {
            break
          }

          // 解码数据块
          const chunk = decoder.decode(value, { stream: true })
          buffer += chunk

          // 处理完整的事件
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.trim() === '') continue

            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim()

              // 检查是否是结束信号
              if (data === '[DONE]') {
                // 最后一次更新流式状态
                const aiMsgIndex = messages.value.findIndex(msg => msg.id === tempAiMessage.id)
                if (aiMsgIndex > -1) {
                  const finalMessage = {
                    ...tempAiMessage,
                    isStreaming: false
                  }
                  messages.value.splice(aiMsgIndex, 1, finalMessage)
                }
                tempAiMessage.isStreaming = false
                break
              }

              try {
                const chunk = JSON.parse(data)

                // 处理连接确认和错误消息
                if (chunk.type === 'connection') continue
                if (chunk.type === 'error') {
                  throw new Error(chunk.message)
                }

                // 更新消息内容
                if (chunk.content) {
                  // 找到AI消息在数组中的索引
                  const aiMsgIndex = messages.value.findIndex(msg => msg.id === tempAiMessage.id)
                  if (aiMsgIndex > -1) {
                    // 创建新的消息对象，累积内容
                    const newContent = tempAiMessage.content + chunk.content
                    const updatedMessage = {
                      ...tempAiMessage,
                      content: newContent,
                      timestamp: new Date().toISOString()
                    }

                    // 使用splice强制触发响应式更新
                    messages.value.splice(aiMsgIndex, 1, updatedMessage)

                    // 更新本地引用
                    tempAiMessage.content = newContent
                    tempAiMessage.timestamp = updatedMessage.timestamp
                  }

                  await nextTick()
                }

                // 处理完成消息
                if (chunk.isComplete && chunk.message) {
                  // 找到AI消息在数组中的索引
                  const aiMsgIndex = messages.value.findIndex(msg => msg.id === tempAiMessage.id)
                  if (aiMsgIndex > -1) {
                    // 创建最终的消息对象
                    const finalMessage = {
                      ...tempAiMessage,
                      id: chunk.message.id,
                      timestamp: chunk.message.timestamp,
                      isStreaming: false
                    }

                    // 使用splice更新消息
                    messages.value.splice(aiMsgIndex, 1, finalMessage)

                    // 更新本地引用
                    tempAiMessage.id = chunk.message.id
                    tempAiMessage.timestamp = chunk.message.timestamp
                    tempAiMessage.isStreaming = false
                  }
                }
              } catch (parseError) {
                console.warn('[sendMessage] 解析流式数据失败:', parseError)
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      // 确保流式状态已清除
      tempAiMessage.isStreaming = false
    } catch (error) {
      // 如果是用户主动取消，不显示错误
      if (error.name === 'AbortError') {
        console.log('[sendMessage] 用户取消了请求')

        // 移除未完成的AI消息
        const aiMsgIndex = messages.value.findIndex(msg => msg.id === tempAiMessage?.id)
        if (aiMsgIndex > -1) {
          messages.value.splice(aiMsgIndex, 1)
        }

        // 恢复输入框内容
        inputMessage.value = userMessage
        return
      }

      console.error('[sendMessage] 发送消息失败:', error)

      // 移除失败的消息
      const userMsgIndex = messages.value.findIndex(msg => msg.id === tempUserMessage.id)
      if (userMsgIndex > -1) {
        messages.value.splice(userMsgIndex, 1)
      }

      const aiMsgIndex = messages.value.findIndex(msg => msg.id === tempAiMessage?.id)
      if (aiMsgIndex > -1) {
        messages.value.splice(aiMsgIndex, 1)
      }

      // 恢复输入框内容
      inputMessage.value = userMessage

      message.error(`发送失败: ${error.message}`)
    } finally {
      sending.value = false
      abortController.value = null
    }
  }

  /**
   * 取消消息发送
   */
  const cancelMessage = () => {
    if (abortController.value) {
      abortController.value.abort()
      message.info('已取消发送')
    }
  }

  /**
   * 更新会话标题
   */
  const updateSessionTitle = async (sessionId, title) => {
    try {
      const response = await api.aiChat.updateSession({
        sessionId,
        title
      })

      if (response.success) {
        // 更新本地会话列表
        const sessionIndex = sessions.value.findIndex(session => session.id === sessionId)
        if (sessionIndex > -1) {
          sessions.value[sessionIndex].title = title
        }

        // 更新当前会话
        if (currentSession.value && currentSession.value.id === sessionId) {
          currentSession.value.title = title
        }

        message.success('会话标题更新成功')
        return true
      }
    } catch (error) {
      console.error('[updateSessionTitle] 更新会话标题失败:', error)
      message.error('更新会话标题失败')
      return false
    }
  }

  /**
   * 删除会话
   */
  const deleteSession = async session => {
    try {
      const userId = getCurrentUserId()
      if (!userId) {
        message.error('请先登录')
        return false
      }
      let sessionId = session.id

      const response = await api.aiChat.deleteSession({
        sessionId,
        userId
      })

      if (response.success) {
        // 从会话列表中移除
        const sessionIndex = sessions.value.findIndex(session => session.id === sessionId)
        if (sessionIndex > -1) {
          sessions.value.splice(sessionIndex, 1)
        }

        // 如果删除的是当前会话，切换到其他会话或清空
        if (currentSession.value && currentSession.value.id === sessionId) {
          if (sessions.value.length > 0) {
            await switchSession(sessions.value[0])
          } else {
            currentSession.value = null
            messages.value = []
          }
        }

        message.success('会话删除成功')
        return true
      }
    } catch (error) {
      console.error('[deleteSession] 删除会话失败:', error)
      message.error('删除会话失败')
      return false
    }
  }

  /**
   * 初始化聊天服务
   */
  const initChatService = async () => {
    await loadSessions()
    if (sessions.value.length > 0) {
      await switchSession(sessions.value[0])
    }
  }

  // 返回组合式函数的状态和方法
  return {
    // 响应式状态
    currentSession,
    messages,
    sessions,
    sending,
    streamingMessage,
    autoScroll,
    inputMessage,
    hasMoreMessages,
    loadingMore,

    // 方法
    createNewSession,
    loadSessions,
    switchSession,
    loadMessages,
    loadMoreMessages,
    sendMessage,
    cancelMessage,
    updateSessionTitle,
    deleteSession,
    initChatService
  }
}
