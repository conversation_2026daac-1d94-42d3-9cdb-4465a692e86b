<template>
  <div class="page-container">
    <a-card :bordered="false" :loading="loading">
      <!-- 文件信息头部 -->
      <div class="file-header">
        <div class="file-title">
          <h2>{{ fileData.name || '招标文件详情' }}</h2>
          <a-tag v-if="fileData.template_name" color="blue">{{ fileData.template_name }}</a-tag>
          <a-tag v-if="fileData.scheme_name" color="green">{{ fileData.scheme_name }}</a-tag>
        </div>
        <div class="file-actions">
          <a-button type="primary" @click="handleEdit" :disabled="loading">
            <template #icon><EditOutlined /></template>编辑
          </a-button>
          <a-button @click="handleExportWord" :loading="exportLoading">
            <template #icon><FileWordOutlined /></template>导出Word
          </a-button>
          <a-button @click="goBack">
            <template #icon><ArrowLeftOutlined /></template>返回列表
          </a-button>
        </div>
      </div>

      <!-- 文件基本信息 -->
      <a-descriptions :column="2" bordered style="margin-bottom: 24px;">
        <a-descriptions-item label="文件ID">{{ fileData.id }}</a-descriptions-item>
        <a-descriptions-item label="文件名称">{{ fileData.name }}</a-descriptions-item>
        <a-descriptions-item label="使用模板">{{ fileData.template_name || '-' }}</a-descriptions-item>
        <a-descriptions-item label="关联方案">{{ fileData.scheme_name || '-' }}</a-descriptions-item>
        <a-descriptions-item label="创建时间">{{ fileData.created_at }}</a-descriptions-item>
        <a-descriptions-item label="更新时间">{{ fileData.updated_at }}</a-descriptions-item>
        <a-descriptions-item label="备注" :span="2">{{ fileData.remark || '-' }}</a-descriptions-item>
      </a-descriptions>

      <!-- 文件内容展示 -->
      <div class="file-content">
        <h3>文件内容</h3>
        <div class="content-viewer">
          <tiptap-editor
            v-if="fileData.content && editorVisible"
            ref="tiptapEditorRef"
            v-model="fileData.content"
            :editable="false"
            :fill-mode="false"
            :show-toolbar="false"
            min-height="500px"
            placeholder="暂无内容"
          />
          <div v-else-if="!fileData.content" class="empty-content">
            <a-empty description="暂无文件内容" />
          </div>
          <div v-else class="loading-content">
            <a-spin size="large" />
            <p>内容加载中...</p>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { EditOutlined, FileWordOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
import api from '@/api';
import TiptapEditor from '@/components/TiptapEditor/index.vue';

const route = useRoute();
const router = useRouter();

const loading = ref(false);
const exportLoading = ref(false);
const editorVisible = ref(false);
const tiptapEditorRef = ref(null);

const fileData = ref({
  id: null,
  name: '',
  template_id: null,
  template_name: '',
  scheme_id: null,
  scheme_name: '',
  remark: '',
  content: null,
  created_at: '',
  updated_at: '',
});

// 获取文件详情
const fetchFileDetails = async (id) => {
  loading.value = true;
  try {
    const response = await api.biddingDocument.getById(id);
    if (response.success && response.data) {
      fileData.value = { ...response.data };
      
      // 处理content数据格式
      if (fileData.value.content) {
        if (typeof fileData.value.content === 'string') {
          try {
            fileData.value.content = JSON.parse(fileData.value.content);
          } catch (e) {
            console.error('解析文件内容失败:', e);
            fileData.value.content = null;
          }
        }
      }
      
      // 确保编辑器在数据加载后显示
      nextTick(() => {
        editorVisible.value = true;
      });
    } else {
      message.error('获取文件详情失败: ' + (response.message || '未知错误'));
      goBack();
    }
  } catch (error) {
    console.error('获取文件详情错误:', error);
    message.error('获取文件详情时发生错误: ' + error.message);
    goBack();
  } finally {
    loading.value = false;
  }
};

// 编辑文件
const handleEdit = () => {
  router.push({ 
    name: 'BiddingDocumentFileEdit', 
    params: { id: fileData.value.id } 
  });
};

// 导出Word文档
const handleExportWord = async () => {
  exportLoading.value = true;
  try {
    // TODO: 实现Word导出功能
    message.info('Word导出功能正在开发中...');
    
    // 示例代码：
    // const response = await exportBiddingDocumentToWord(fileData.value.id);
    // if (response.success) {
    //   // 下载文件
    //   const blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
    //   const url = window.URL.createObjectURL(blob);
    //   const link = document.createElement('a');
    //   link.href = url;
    //   link.download = `${fileData.value.name}.docx`;
    //   link.click();
    //   window.URL.revokeObjectURL(url);
    //   message.success('Word文档导出成功');
    // } else {
    //   message.error('导出失败: ' + response.message);
    // }
  } catch (error) {
    console.error('导出Word错误:', error);
    message.error('导出Word时发生错误: ' + error.message);
  } finally {
    exportLoading.value = false;
  }
};

// 返回列表
const goBack = () => {
  router.push({ name: 'BiddingDocumentFileList' });
};

onMounted(() => {
  const fileId = route.params.id;
  if (fileId) {
    fetchFileDetails(fileId);
  } else {
    message.error('缺少文件ID参数');
    goBack();
  }
});
</script>

<style lang="less" scoped>
.page-container {
  padding: 16px;
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;

  .file-title {
    h2 {
      margin: 0 0 8px 0;
      color: #262626;
    }
  }

  .file-actions {
    display: flex;
    gap: 8px;
  }
}

.file-content {
  h3 {
    margin-bottom: 16px;
    color: #262626;
  }

  .content-viewer {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    overflow: hidden;

    .empty-content {
      padding: 40px;
      text-align: center;
    }

    .loading-content {
      padding: 40px;
      text-align: center;
      
      p {
        margin-top: 16px;
        color: #999;
      }
    }
  }
}

// 覆盖编辑器样式，使其在查看模式下更美观
:deep(.ProseMirror) {
  padding: 16px !important;
  min-height: 500px !important;
  background-color: #fafafa;
  
  &:focus {
    outline: none;
    box-shadow: none;
  }
}
</style> 