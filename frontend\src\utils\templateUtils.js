/**
 * 模板工具函数
 * 用于处理模板内容与字段值的合并操作
 */

/**
 * 将字段值合并到模板内容中
 * @param {object} templateDocJson - 模板的 Tiptap JSON 对象
 * @param {object} fieldValuesObject - 字段值对象 { field_key: [...content nodes] }
 * @returns {object} - 合并后的 Tiptap JSON 对象
 * 
 * 注意：
 * - fieldValuesObject 中的值应该是内容节点数组
 * - 空字段使用空数组 []
 * - 保留所有样式信息（marks、attributes等）
 */
export function mergeTemplateWithFieldValues(templateDocJson, fieldValuesObject) {
  if (!templateDocJson || !templateDocJson.content) return templateDocJson
  if (!fieldValuesObject || typeof fieldValuesObject !== 'object') return templateDocJson

  // 深拷贝模板内容，避免修改原始模板
  const newDoc = JSON.parse(JSON.stringify(templateDocJson))

  function traverse(nodes) {
    if (!Array.isArray(nodes)) return
    
    for (let i = 0; i < nodes.length; i++) {
      const node = nodes[i]
      
      // 处理自定义字段节点
      if (node.type === 'customField' && node.attrs && node.attrs.field_key) {
        const fieldKey = node.attrs.field_key
        
        if (fieldValuesObject.hasOwnProperty(fieldKey)) {
          const value = fieldValuesObject[fieldKey]
          
          // 期望 value 是节点数组
          if (Array.isArray(value)) {
            node.content = value
          } else {
            // 如果不是数组，设置为空内容
            console.warn(`[mergeTemplateWithFieldValues] 字段 ${fieldKey} 的值不是数组:`, value)
            node.content = []
          }
        } else {
          // 字段值不存在，设置为空内容
          node.content = []
        }
      }
      
      // 递归处理子节点
      if (node.content) {
        traverse(node.content)
      }
    }
  }

  traverse(newDoc.content)
  return newDoc
}

/**
 * 从Tiptap JSON中提取所有自定义字段的值（包含样式）
 * @param {object} editorDocJson - 编辑器当前的Tiptap Document JSON
 * @returns {object} - 字段值对象 { field_key: [...content nodes] }
 * 
 * 注意：
 * - 返回的是字段内容节点数组，包含所有样式信息（marks、attributes等）
 * - 空字段返回空数组 []
 * - 这种格式可以完整保留用户设置的所有样式
 */
export function extractFieldValuesFromTiptapJSON(editorDocJson) {
  const fieldValues = {}
  
  if (!editorDocJson || typeof editorDocJson !== 'object' || editorDocJson.type !== 'doc') {
    console.error('[extractFieldValuesFromTiptapJSON] Invalid editorDocJson:', editorDocJson)
    return fieldValues
  }

  function traverseAndExtract(node) {
    if (node.type === 'customField' && node.attrs && node.attrs.field_key) {
      const fieldKey = node.attrs.field_key
      // 直接保存内容节点数组，保留所有样式信息
      fieldValues[fieldKey] = node.content || []
    }
    
    if (node.content && Array.isArray(node.content)) {
      node.content.forEach(traverseAndExtract)
    }
  }

  if (editorDocJson.content && Array.isArray(editorDocJson.content)) {
    editorDocJson.content.forEach(traverseAndExtract)
  }

  return fieldValues
}

/**
 * 清理Tiptap JSON中的文本节点，确保text属性不为undefined，并移除空文本节点
 * @param {object} tiptapJson - Tiptap JSON对象
 */
export function sanitizeTextNodesInTiptapJSON(tiptapJson) {
  if (!tiptapJson || typeof tiptapJson !== 'object') return

  function traverse(node) {
    // 处理文本节点
    if (node.type === 'text') {
      if (node.text === undefined || node.text === null) {
        node.text = ''
      }
    }

    // 处理包含content的节点
    if (node.content && Array.isArray(node.content)) {
      // 递归处理子节点
      node.content.forEach(traverse)

      // 移除空文本节点
      node.content = node.content.filter(childNode => {
        // 如果是文本节点且内容为空，则移除
        if (childNode.type === 'text' && (!childNode.text || childNode.text === '')) {
          return false
        }
        return true
      })
    }
  }

  if (tiptapJson.content && Array.isArray(tiptapJson.content)) {
    tiptapJson.content.forEach(traverse)
  }
}

/**
 * 获取默认的空Tiptap文档结构
 * @returns {object} - 默认的Tiptap文档JSON
 */
export function getDefaultTiptapDoc() {
  return { type: 'doc', content: [{ type: 'paragraph', content: [] }] }
}
