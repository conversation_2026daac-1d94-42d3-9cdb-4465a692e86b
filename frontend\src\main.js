import * as AntIcon from '@ant-design/icons-vue'
import Antd from 'ant-design-vue'
import { createApp } from 'vue'
import App from './App.vue'
import './assets/global.less'
import 'virtual:uno.css'
import components from './components/global'

import Router from './router/index'
import store from './store'

import './utils/perssion.js'
// 导入WebSocket服务
import webSocketService from './utils/webSocketService.js'

const app = createApp(App)
app.config.productionTip = false

// components
for (const i in components) {
  app.component(i, components[i])
}

// icon
for (const i in AntIcon) {
  const whiteList = ['createFromIconfontCN', 'getTwoToneColor', 'setTwoToneColor', 'default']
  if (!whiteList.includes(i)) {
    app.component(i, AntIcon[i])
  }
}

app.use(Antd).use(Router).use(store).mount('#app')

// 应用启动后初始化WebSocket连接
app.config.globalProperties.$webSocket = webSocketService

// 监听用户登录状态，自动连接/断开WebSocket
store.watch(
  state => state.auth.user,
  (newUser, oldUser) => {
    if (newUser) {
      // 用户登录后连接WebSocket
      console.log('用户已登录，连接WebSocket服务器')
      webSocketService.connect()
    } else if (oldUser) {
      // 用户登出后断开WebSocket
      console.log('用户已登出，断开WebSocket连接')
      webSocketService.close()
    }
  }
)

// 如果用户已经登录，立即连接WebSocket
if (store.state.auth.user) {
  console.log('检测到用户已登录，连接WebSocket服务器')
  webSocketService.connect()
}
