import { processAsyncImageNode } from './imageProcessor.js'

/**
 * 异步处理器模块
 * 负责递归处理文档中的异步图片节点，将其转换为真正的 ImageRun
 */

/**
 * 递归处理异步图片节点的函数
 * 遍历文档元素树，找到所有 asyncImage 对象并将其转换为 ImageRun
 * @param {object} element - 文档元素
 * @returns {Promise<object>} 处理后的文档元素
 */
export async function processElementWithAsyncImages(element) {
  if (!element) return element

  // 如果是异步图片对象，直接处理（所有图片都是内联的）
  if (element.type === 'asyncImage') {
    return await processAsyncImageNode(element)
  }

  // 递归处理 children 属性
  if (element.children && Array.isArray(element.children)) {
    const processedChildren = []
    for (const child of element.children) {
      processedChildren.push(await processElementWithAsyncImages(child))
    }
    element.children = processedChildren
  }

  // 递归处理 root 属性（兼容 docx.js 内部结构）
  if (element.root && Array.isArray(element.root)) {
    const processedRoot = []
    for (const child of element.root) {
      processedRoot.push(await processElementWithAsyncImages(child))
    }
    element.root = processedRoot
  }

  return element
}

/**
 * 批量处理文档元素数组中的异步图片
 * @param {Array<object>} elements - 文档元素数组
 * @returns {Promise<Array<object>>} 处理后的文档元素数组
 */
export async function processDocumentElements(elements) {
  const processedElements = []

  for (const element of elements) {
    console.log({ element: element })
    const processedElement = await processElementWithAsyncImages(element)
    processedElements.push(processedElement)
  }

  return processedElements
}
