'use strict';

// 递归地对对象或数组中的字符串值进行 unescape
function deepUnescape(obj) {
  if (typeof obj === 'string') {
    // 基础的 unescape，可以根据需要替换为更强大的库如 he.decode
    // 这里仅作示例，实际可能不需要，取决于数据如何存储和查询
    return obj.replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&amp;/g, '&')
              .replace(/&quot;/g, '"')
              .replace(/&#039;/g, "'");
  } else if (Array.isArray(obj)) {
    return obj.map(deepUnescape);
  } else if (typeof obj === 'object' && obj !== null) {
    const newObj = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        newObj[key] = deepUnescape(obj[key]);
      }
    }
    return newObj;
  }
  return obj;
}

// 递归地对对象或数组中的字符串值进行 HTML escape
function deepEscape(obj) {
  if (typeof obj === 'string') {
    return obj.replace(/&/g, '&amp;')
              .replace(/</g, '&lt;')
              .replace(/>/g, '&gt;')
              .replace(/"/g, '&quot;')
              .replace(/'/g, '&#039;');
  } else if (Array.isArray(obj)) {
    return obj.map(deepEscape);
  } else if (typeof obj === 'object' && obj !== null) {
    const newObj = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        newObj[key] = deepEscape(obj[key]);
      }
    }
    return newObj;
  }
  return obj;
}

module.exports = {
  deepUnescape,
  deepEscape,
}; 