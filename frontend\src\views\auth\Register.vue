<template>
  <div class="register-container">
    <a-card title="用户注册" style="width: 400px;">
      <a-form :model="formState" @finish="handleRegister" layout="vertical">
        <a-form-item
          label="用户名"
          name="username"
          :rules="[{ required: true, message: '请输入用户名!' }]"
        >
          <a-input v-model:value="formState.username" />
        </a-form-item>

        <a-form-item
          label="昵称 (可选)"
          name="nickname"
        >
          <a-input v-model:value="formState.nickname" />
        </a-form-item>

        <a-form-item
          label="邮箱 (可选)"
          name="email"
          :rules="[{ type: 'email', message: '请输入有效的邮箱地址' }]"
        >
          <a-input v-model:value="formState.email" />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码!' }]"
        >
          <a-input-password v-model:value="formState.password" />
        </a-form-item>

        <a-form-item
          label="确认密码"
          name="confirmPassword"
          :dependencies="['password']"
          :rules="[
            { required: true, message: '请确认您的密码!' },
            { validator: validateConfirmPassword },
          ]"
        >
          <a-input-password v-model:value="formState.confirmPassword" />
        </a-form-item>
        <a-form-item label="主机服务IP(默认127.0.0.1本机服务)" name="serverIP" :rules="[{ required: true, message: '主机服务IP!' }]">
          <a-input v-model:value="IP" placeholder="请输入主机服务IP" @input="onIPInput" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading" block>
            注册
          </a-button>
        </a-form-item>

        <a-alert v-if="error" :message="error" type="error" show-icon />

        <div style="text-align: center; margin-top: 16px;">
          已有账户? <router-link to="/login">立即登录</router-link>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { useStore } from 'vuex';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
// 引入新的HTTP API（虽然当前主要用Vuex，但为了一致性和未来可能的直接调用）
import api from '@/api';

const store = useStore();
const router = useRouter();
const IP = ref('127.0.0.1');

// 初始化IP地址
if(localStorage.getItem('IP')) {
  IP.value = localStorage.getItem('IP');
}

const formState = reactive({
  username: '',
  nickname: '',
  email: '',
  password: '',
  confirmPassword: '',
});

const loading = ref(false);
const error = ref(null);

// 确认密码验证函数
const validateConfirmPassword = async (_rule, value) => {
  if (value === formState.password) {
    return Promise.resolve();
  }
  return Promise.reject(new Error('两次输入的密码不匹配!'));
};

// IP输入处理函数
const onIPInput = (e) => {
  // 只保留数字和英文点，去除空格
  IP.value = e.target.value.replace(/\s+/g, '').replace(/[^0-9.]/g, '');
};

// 注册处理函数
const handleRegister = async () => {
  loading.value = true;
  error.value = null;
  try {
    // 保存IP地址到localStorage
    localStorage.setItem('IP', IP.value);
    
    // 从表单状态中提取注册所需数据
    const { username, password, nickname, email } = formState;
    await store.dispatch('auth/register', { username, password, nickname, email });
    message.success('注册成功! 请登录。');
    router.push('/login');
  } catch (err) {
    error.value = err.message || '注册时发生错误，请重试。';
    message.error(error.value);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f0f2f5;
}
</style> 