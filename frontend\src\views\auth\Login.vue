<template>
  <div class="login-container">
    <a-card title="用户登录" style="width: 400px">
      <a-form :model="formState" @finish="handleLogin" layout="vertical">
        <a-form-item label="用户名" name="username" :rules="[{ required: true, message: '请输入用户名!' }]">
          <a-input v-model:value="formState.username" placeholder="默认为 admin" />
        </a-form-item>

        <a-form-item label="密码" name="password" :rules="[{ required: true, message: '请输入密码!' }]">
          <a-input-password v-model:value="formState.password" placeholder="默认为 admin123" />
        </a-form-item>
        <a-form-item label="主机服务IP(默认127.0.0.1本机服务)" name="password" :rules="[{ required: true, message: '主机服务IP!' }]">
          <a-input v-model:value="IP" placeholder="请输入主机服务IP" @input="onIPInput" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading" block>登录</a-button>
        </a-form-item>

        <a-alert v-if="error" :message="error" type="error" show-icon />

        <div style="text-align: center; margin-top: 16px">
          还没有账户?
          <router-link to="/register">立即注册</router-link>
        </div>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

const store = useStore()
const router = useRouter()
const formState = reactive({
  username: 'admin', // 默认填充
  password: 'admin123' // 默认填充
})
const loading = ref(false)
const error = ref(null)
const IP = ref('127.0.0.1')
if (localStorage.getItem('IP')) IP.value = localStorage.getItem('IP')
const handleLogin = async () => {
  loading.value = true
  error.value = null
  try {
    localStorage.setItem('IP', IP.value)
    let result = await store.dispatch('auth/login', formState)

    if (result.success) {
      message.success('登录成功!')
      router.push({ path: '/' })
    } else {
      message.error(result.message)
    }
  } catch (err) {
    error.value = err.message || '登录时发生错误，请重试。'
    message.error(error.value)
  } finally {
    loading.value = false
  }
}

const onIPInput = e => {
  IP.value = e.target.value.replace(/\s+/g, '').replace(/[^0-9.]/g, '')
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f0f2f5;
  /* Ant Design 背景色 */
  :deep(.ant-card-body) {
    background-image: url('@/assets/login.png');
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
  }
}
</style>
