'use strict'

// const Service = require('ee-core').Service; // 移除这一行
const bcrypt = require('bcrypt')
const { sqlitedbService } = require('./database/sqlitedb') // 确保路径正确
const RoleService = require('./roleService') // 引入 RoleService

/**
 * 用户服务
 * @class
 */
class UserService {
  // 移除 extends Service

  constructor(ctx) {
    // super(ctx); // 移除这一行
    this.ctx = ctx // 可以选择保留 ctx 供服务内部使用
    this.db = sqlitedbService.db
    this.userTable = sqlitedbService.appUsersTableName
    this.roleTable = sqlitedbService.appRolesTableName
    this.roleService = new RoleService(ctx) // 实例化 RoleService
  }

  /**
   * 注册新用户
   * @param {Object} userData
   * @param {string} userData.username
   * @param {string} userData.password
   * @param {string} [userData.nickname]
   * @param {string} [userData.email]
   * @returns {Promise<Object>} 新用户信息 (不含密码哈希)
   */
  async register(userData) {
    // const { username, password, nickname = '', email = '' } = userData;
    const { username, password, nickname = '', email = null, role_id } = userData

    // 检查用户名是否存在
    const existingUser = await this.getByUsername(username)
    if (existingUser) {
      throw new Error('用户名已存在')
    }

    // 检查邮箱是否存在 (如果提供了邮箱)
    if (email) {
      const userByEmail = await this.getByEmail(email)
      if (userByEmail) {
        throw new Error('邮箱已被注册')
      }
    }

    // 获取普通用户角色ID
    // const ordinaryUserRole = await this.roleService.getByName('ordinary_user');
    // if (!ordinaryUserRole) {
    //   throw new Error('默认角色"ordinary_user"未找到，请先初始化角色数据');
    // }

    // 获取角色ID
    const ordinaryUserRole = await this.roleService.getById(role_id)
    if (!ordinaryUserRole) {
      throw new Error('未找到角色信息，请先初始化角色数据')
    }

    const hashedPassword = await bcrypt.hash(password, 10)

    // const stmt = this.db.prepare(
    //   `INSERT INTO ${this.userTable} (username, password_hash, nickname, email, role_id, status, created_at, updated_at)
    //    VALUES (@username, @password_hash, @nickname, @email, @role_id, 'active', datetime('now','localtime'), datetime('now','localtime'))`
    // );
    // 邮箱可以为空
    const stmt = this.db.prepare(
      `INSERT INTO ${this.userTable} (username, password_hash, nickname, email, role_id, status, created_at, updated_at)
      VALUES (@username, @password_hash, @nickname, COALESCE(NULLIF(@email, ''), NULL), @role_id, 'active', datetime('now','localtime'), datetime('now','localtime'))`
    )

    try {
      const result = stmt.run({
        username,
        password_hash: hashedPassword,
        nickname,
        email,
        role_id: ordinaryUserRole.id
      })
      return await this.getById(result.lastInsertRowid)
    } catch (error) {
      // 处理唯一约束冲突等错误
      if (error.message.includes('UNIQUE constraint failed: app_users.username')) {
        throw new Error('用户名已存在')
      }
      if (email && error.message.includes('UNIQUE constraint failed: app_users.email')) {
        throw new Error('邮箱已被注册')
      }
      throw error // 其他错误
    }
  }

  /**
   * 用户登录
   * @param {string} username
   * @param {string} password
   * @returns {Promise<Object|null>} 用户信息 (不含密码哈希) 或 null
   */
  async login(username, password) {
    const user = await this.getByUsername(username, true) // true 表示获取完整用户对象包括密码哈希
    if (!user || !user.password_hash) {
      return null // 用户不存在
    }

    const isMatch = await bcrypt.compare(password, user.password_hash)
    if (!isMatch) {
      return null // 密码错误
    }

    // 移除密码哈希后返回
    const { password_hash, ...userWithoutPassword } = user
    return { success: true, data: userWithoutPassword }
  }

  /**
   * 根据ID获取用户
   * @param {number} id
   * @param {boolean} [includePasswordHash=false] - 是否包含密码哈希 (仅内部使用)
   * @returns {Promise<Object|null>}
   */
  async getById(id, includePasswordHash = false) {
    const stmt = this.db.prepare(
      `SELECT u.*, r.name as role_name 
       FROM ${this.userTable} u 
       LEFT JOIN ${this.roleTable} r ON u.role_id = r.id 
       WHERE u.id = ?`
    )
    const user = stmt.get(id)
    if (user && !includePasswordHash) {
      const { password_hash, ...userWithoutPassword } = user
      return userWithoutPassword
    }
    return user || null
  }

  /**
   * 根据用户名获取用户
   * @param {string} username
   * @param {boolean} [includePasswordHash=false] - 是否包含密码哈希 (仅内部使用)
   * @returns {Promise<Object|null>}
   */
  async getByUsername(username, includePasswordHash = false) {
    const stmt = this.db.prepare(
      `SELECT u.*, r.name as role_name 
       FROM ${this.userTable} u 
       LEFT JOIN ${this.roleTable} r ON u.role_id = r.id 
       WHERE u.username = ?`
    )
    const user = stmt.get(username)
    if (user && !includePasswordHash) {
      const { password_hash, ...userWithoutPassword } = user
      return userWithoutPassword
    }
    return user || null
  }

  /**
   * 根据邮箱获取用户
   * @param {string} email
   * @param {boolean} [includePasswordHash=false]
   * @returns {Promise<Object|null>}
   */
  async getByEmail(email, includePasswordHash = false) {
    if (!email) return null
    const stmt = this.db.prepare(
      `SELECT u.*, r.name as role_name 
       FROM ${this.userTable} u 
       LEFT JOIN ${this.roleTable} r ON u.role_id = r.id 
       WHERE u.email = ?`
    )
    const user = stmt.get(email)
    if (user && !includePasswordHash) {
      const { password_hash, ...userWithoutPassword } = user
      return userWithoutPassword
    }
    return user || null
  }

  /**
   * 获取用户列表 (分页)
   * @param {Object} params
   * @param {number} [params.page=1]
   * @param {number} [params.pageSize=10]
   * @returns {Promise<Object>} { list, total }
   */
  async list(params = {}) {
    const { page = 1, pageSize = 10 } = params
    const offset = (page - 1) * pageSize

    const countStmt = this.db.prepare(`SELECT COUNT(id) as total FROM ${this.userTable}`)
    const { total } = countStmt.get()

    const listStmt = this.db.prepare(
      `SELECT u.id, u.username, u.nickname, u.email, u.role_id, u.status, u.created_at, u.updated_at, r.name as role_name 
        FROM ${this.userTable} u
        LEFT JOIN ${this.roleTable} r ON u.role_id = r.id
        ORDER BY u.id ASC
        LIMIT @pageSize OFFSET @offset`
    )
    const users = listStmt.all({ pageSize, offset })
    return { list: users, total }
  }

  /**
   * 更新用户信息
   * @param {number} id 用户ID
   * @param {Object} data 更新数据
   * @param {string} [data.nickname]
   * @param {string} [data.email]
   * @param {string} [data.password] 新密码
   * @param {number} [data.role_id]
   * @param {string} [data.status]
   * @returns {Promise<Object|null>} 更新后的用户信息
   */
  async update(id, data) {
    const user = await this.getById(id, true) // 获取包含密码哈希的原始用户信息
    if (!user) {
      throw new Error('用户不存在')
    }

    let { nickname, email, password, role_id, status } = data

    const fieldsToUpdate = {}
    if (nickname !== undefined) fieldsToUpdate.nickname = nickname
    if (email !== undefined) {
      // 检查邮箱唯一性
      if (email !== user.email) {
        const existingByEmail = await this.getByEmail(email)
        if (existingByEmail && existingByEmail.id !== id) {
          throw new Error('该邮箱已被其他用户注册')
        }
      }
      fieldsToUpdate.email = email
    }
    if (password) {
      fieldsToUpdate.password_hash = await bcrypt.hash(password, 10)
    }
    if (role_id !== undefined) fieldsToUpdate.role_id = role_id
    if (status !== undefined) fieldsToUpdate.status = status

    if (Object.keys(fieldsToUpdate).length === 0) {
      return user // 没有需要更新的字段
    }

    fieldsToUpdate.updated_at = this.db.prepare("SELECT datetime('now','localtime') as dt").get().dt

    const setClauses = Object.keys(fieldsToUpdate)
      .map(key => `${key} = @${key}`)
      .join(', ')
    const stmt = this.db.prepare(`UPDATE ${this.userTable} SET ${setClauses} WHERE id = @id`)

    try {
      stmt.run({ ...fieldsToUpdate, id })
      return await this.getById(id)
    } catch (error) {
      if (email && error.message.includes('UNIQUE constraint failed: app_users.email')) {
        throw new Error('该邮箱已被其他用户注册 (并发冲突)')
      }
      throw error
    }
  }

  /**
   * 删除用户
   * @param {number} id 用户ID
   * @returns {Promise<boolean>} 是否成功删除
   */
  async delete(id) {
    // 防止删除 admin 用户 (如果需要)
    // const userToDelete = await this.getById(id);
    // if (userToDelete && userToDelete.username === 'admin') {
    //   throw new Error('不能删除默认管理员账户');
    // }
    const stmt = this.db.prepare(`DELETE FROM ${this.userTable} WHERE id = ?`)
    const result = stmt.run(id)
    return result.changes > 0
  }
}

module.exports = UserService
