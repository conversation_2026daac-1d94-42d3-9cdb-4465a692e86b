<template>
  <a-modal :open="visible" title="会话管理" width="600px" :footer="null" class="session-modal" @cancel="handleCancel">
    <div class="session-management">
      <!-- 搜索栏 -->
      <a-input-search v-model:value="searchText" placeholder="搜索会话..." class="session-search" />

      <!-- 会话列表 -->
      <div class="session-list">
        <div
          v-for="session in filteredSessions"
          :key="session.id"
          class="session-item"
          :class="{ active: currentSessionId === session.id }"
          @click="handleSwitchSession(session)"
        >
          <div class="session-content">
            <div class="session-header">
              <span class="session-name">{{ session.title }}</span>
              <span class="session-time">{{ formatTime(session.updated_at) }}</span>
            </div>
            <div class="session-preview">
              {{ getSessionPreview(session) }}
            </div>
          </div>
          <div class="session-actions">
            <a-tooltip title="重命名">
              <EditOutlined @click.stop="handleEditSession(session)" />
            </a-tooltip>
            <a-tooltip title="删除">
              <DeleteOutlined @click.stop="handleDeleteSession(session)" />
            </a-tooltip>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <a-empty v-if="filteredSessions.length === 0" description="暂无会话记录">
        <a-button type="primary" @click="handleCreateSession">
          <PlusOutlined />
          创建新会话
        </a-button>
      </a-empty>

      <!-- 底部操作栏 -->
      <div class="session-footer">
        <a-button type="primary" @click="handleCreateSession">
          <PlusOutlined />
          新建会话
        </a-button>
        <a-button @click="handleRefresh">
          <ReloadOutlined />
          刷新
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Modal } from 'ant-design-vue'
import { EditOutlined, DeleteOutlined, PlusOutlined, ReloadOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  sessions: {
    type: Array,
    default: () => []
  },
  currentSessionId: {
    type: [String, Number],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'switch-session', 'edit-session', 'delete-session', 'create-session', 'refresh'])

// 响应式数据
const searchText = ref('')

// 计算属性
const filteredSessions = computed(() => {
  if (!searchText.value) return props.sessions
  return props.sessions.filter(session => session.title.toLowerCase().includes(searchText.value.toLowerCase()))
})

// 方法
const handleCancel = () => {
  emit('update:visible', false)
}

const handleSwitchSession = session => {
  emit('switch-session', session)
}

const handleEditSession = session => {
  emit('edit-session', session)
}

const handleDeleteSession = session => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除会话"${session.title}"吗？此操作不可撤销。`,
    onOk: () => emit('delete-session', session)
  })
}

const handleCreateSession = () => {
  emit('create-session')
}

const handleRefresh = () => {
  emit('refresh')
}

const getSessionPreview = session => {
  // 这里可以加载会话的最后一条消息作为预览
  return '点击查看会话内容...'
}

const formatTime = timestamp => {
  if (!timestamp) return ''

  try {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚'
    }

    // 小于1小时
    if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    }

    // 小于24小时
    if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`
    }

    // 小于7天
    if (diff < 604800000) {
      return `${Math.floor(diff / 86400000)}天前`
    }

    // 超过7天显示具体日期
    return date.toLocaleDateString()
  } catch (error) {
    return timestamp
  }
}
</script>

<style lang="less" scoped>
.session-modal {
  :deep(.ant-modal-body) {
    padding: 0;
  }

  .session-management {
    .session-search {
      padding: 20px 20px 0;
    }

    .session-list {
      max-height: 400px;
      overflow-y: auto;
      padding: 16px 0;

      .session-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        cursor: pointer;
        transition: background 0.2s;

        &:hover {
          background: #f5f5f5;
        }

        &.active {
          background: #e6f7ff;
          border-left: 3px solid #1890ff;
        }

        .session-content {
          flex: 1;

          .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;

            .session-name {
              font-weight: 500;
              color: #262626;
            }

            .session-time {
              font-size: 12px;
              color: #8c8c8c;
            }
          }

          .session-preview {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
          }
        }

        .session-actions {
          display: flex;
          gap: 8px;
          opacity: 0;
          transition: opacity 0.2s;

          .anticon {
            color: #8c8c8c;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;

            &:hover {
              color: #1890ff;
              background: #f0f7ff;
            }
          }
        }

        &:hover .session-actions {
          opacity: 1;
        }
      }
    }

    .session-footer {
      padding: 16px 20px;
      border-top: 1px solid #f0f0f0;
      display: flex;
      gap: 12px;
      justify-content: space-between;
    }
  }
}
</style>
