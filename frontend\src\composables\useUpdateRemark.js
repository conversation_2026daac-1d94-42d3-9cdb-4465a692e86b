/**
 * 更新备注管理组合式函数
 * 提供更新备注对话框相关的状态管理和逻辑
 */

import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 更新备注管理组合式函数
 * @param {Object} options 配置选项
 * @param {Function} options.submitCallback 提交回调函数
 * @param {Function} options.checkContentChanged 检查内容是否变化的函数
 * @param {boolean} options.isEditMode 是否为编辑模式
 * @returns {Object} 更新备注相关的状态和方法
 */
export function useUpdateRemark(options) {
  const { submitCallback, checkContentChanged, isEditMode } = options
  
  // 更新备注对话框相关状态
  const updateRemarkModalVisible = ref(false)
  const updateRemarkFormRef = ref()
  const updateRemarkForm = reactive({
    remark: ''
  })
  const pendingUpdateData = ref(null) // 存储待提交的数据
  
  // 内容变化状态
  const contentChanged = ref(false)
  
  /**
   * 更新备注表单验证规则
   */
  const updateRemarkRules = {
    remark: [
      { required: true, message: '请填写更新备注', trigger: 'blur' },
      { min: 5, message: '更新备注至少需要5个字符', trigger: 'blur' },
      { max: 500, message: '更新备注不能超过500个字符', trigger: 'blur' }
    ]
  }
  
  /**
   * 检查是否需要更新备注
   * @param {Object} currentFieldValues 当前字段值
   * @returns {boolean} 是否需要更新备注
   */
  const shouldShowUpdateRemark = (currentFieldValues) => {
    if (!isEditMode) {
      return false
    }
    
    const hasContentChanged = checkContentChanged(currentFieldValues)
    contentChanged.value = hasContentChanged
    
    return hasContentChanged
  }
  
  /**
   * 显示更新备注对话框
   * @param {Object} payload 待提交的数据
   */
  const showUpdateRemarkModal = (payload) => {
    pendingUpdateData.value = payload
    updateRemarkForm.remark = '' // 清空之前的输入
    updateRemarkModalVisible.value = true
  }
  
  /**
   * 隐藏更新备注对话框
   */
  const hideUpdateRemarkModal = () => {
    updateRemarkModalVisible.value = false
    updateRemarkForm.remark = ''
    pendingUpdateData.value = null
    
    // 重置表单验证状态
    if (updateRemarkFormRef.value) {
      updateRemarkFormRef.value.resetFields()
    }
  }
  
  /**
   * 处理更新备注对话框确认
   */
  const handleUpdateRemarkSubmit = async () => {
    try {
      // 先进行表单验证
      await updateRemarkFormRef.value.validate()
      
      if (!pendingUpdateData.value) {
        message.error('没有待提交的数据')
        return false
      }
      
      // 添加更新备注到待提交数据
      const finalPayload = {
        ...pendingUpdateData.value,
        update_remark: updateRemarkForm.remark.trim()
      }
      
      // 隐藏对话框
      updateRemarkModalVisible.value = false
      
      // 执行提交回调
      const success = await submitCallback(finalPayload)
      
      if (success) {
        // 重置状态
        resetUpdateRemarkState()
      }
      
      return success
    } catch (error) {
      // 表单验证失败，不执行提交
      console.log('更新备注表单验证失败:', error)
      return false
    }
  }
  
  /**
   * 处理更新备注对话框取消
   */
  const handleUpdateRemarkCancel = () => {
    hideUpdateRemarkModal()
  }
  
  /**
   * 重置更新备注状态
   */
  const resetUpdateRemarkState = () => {
    updateRemarkModalVisible.value = false
    updateRemarkForm.remark = ''
    pendingUpdateData.value = null
    contentChanged.value = false
    
    // 重置表单验证状态
    if (updateRemarkFormRef.value) {
      updateRemarkFormRef.value.resetFields()
    }
  }
  
  /**
   * 处理表单提交（带更新备注检查）
   * @param {Object} payload 提交数据
   * @param {Object} currentFieldValues 当前字段值
   * @returns {Promise<boolean>} 提交是否成功
   */
  const handleSubmitWithRemarkCheck = async (payload, currentFieldValues) => {
    try {
      // 检查是否需要更新备注
      if (shouldShowUpdateRemark(currentFieldValues)) {
        // 显示更新备注对话框
        showUpdateRemarkModal(payload)
        return true // 返回true表示已处理，但实际提交会在对话框确认后进行
      }
      
      // 如果没有内容变化，直接提交
      const success = await submitCallback(payload)
      
      if (success) {
        resetUpdateRemarkState()
      }
      
      return success
    } catch (error) {
      console.error('提交时发生错误:', error)
      message.error('提交时发生错误: ' + (error.message || '未知错误'))
      return false
    }
  }
  
  /**
   * 获取更新备注对话框的配置
   * @param {string} entityName 实体名称（如：文件、方案）
   * @returns {Object} 对话框配置
   */
  const getUpdateRemarkModalConfig = (entityName = '内容') => {
    return {
      title: '填写更新备注',
      width: '500px',
      okText: '确认保存',
      cancelText: '取消',
      maskClosable: false,
      alertMessage: `检测到${entityName}已发生变化`,
      alertDescription: '请填写本次更新的备注信息，说明具体修改了什么内容。',
      placeholder: '请详细说明本次修改的内容，方便后续查看历史版本时了解改动情况',
      maxLength: 500,
      showCount: true,
      rows: 4
    }
  }
  
  /**
   * 验证更新备注内容
   * @param {string} remark 备注内容
   * @returns {Object} 验证结果 { valid: boolean, message?: string }
   */
  const validateUpdateRemark = (remark) => {
    if (!remark || !remark.trim()) {
      return { valid: false, message: '请填写更新备注' }
    }
    
    if (remark.trim().length < 5) {
      return { valid: false, message: '更新备注至少需要5个字符' }
    }
    
    if (remark.trim().length > 500) {
      return { valid: false, message: '更新备注不能超过500个字符' }
    }
    
    return { valid: true }
  }
  
  return {
    // 状态
    updateRemarkModalVisible,
    updateRemarkFormRef,
    updateRemarkForm,
    pendingUpdateData,
    contentChanged,
    updateRemarkRules,
    
    // 方法
    shouldShowUpdateRemark,
    showUpdateRemarkModal,
    hideUpdateRemarkModal,
    handleUpdateRemarkSubmit,
    handleUpdateRemarkCancel,
    resetUpdateRemarkState,
    handleSubmitWithRemarkCheck,
    getUpdateRemarkModalConfig,
    validateUpdateRemark
  }
}
