/**
 * 基础路由
 * @type { *[] }
 */

import UserManagement from '@/views/system/UserManagement.vue';
import RoleManagement from '@/views/system/RoleManagement.vue';

const constantRouterMap = [
  // 单独的登录和注册路由，不使用 AppSider 布局
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { guest: true } // 标记为访客路由，已登录用户访问时可重定向
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { guest: true }
  },

  {
    path: '/',
    name:'routers',
    component: () => import('@/layouts/AppSider.vue'),
    // meta: { requiresAuth: true }, // 主布局下的所有页面默认需要认证
    redirect: '/tender-scheme', // 默认重定向到某个页面，例如 /dashboard 或 /framework
    children: [
      // --- 招标方案管理 --- 
      {
        path: '/tender-scheme',
        name: 'TenderSchemeModule', // 顶级菜单路由名称
        component: () => import('@/layouts/Menu.vue'), // 使用通用子菜单布局
        props: { id: 'TenderSchemeModule' }, // 用于 Menu.vue 识别当前激活的菜单组
        redirect: { name: 'TenderSchemeTemplateList' }, // 默认重定向到模板列表
        meta: { requiresAuth: true, title: '招标方案', antIcon: 'SolutionOutlined' }, // 确保使用 antIcon
        children: [
          {
            path: 'templates',
            name: 'TenderSchemeTemplateList',
            component: () => import('@/views/tenderScheme/TemplateList.vue'),
            // meta: { title: '模板管理', icon: 'ProfileOutlined' },
            meta: { title: '模板管理' },
          },
          {
            path: 'templates/create',
            name: 'TenderSchemeTemplateCreate',
            component: () => import('@/views/tenderScheme/TemplateForm.vue'),
            meta: { title: '新建模板', parentPath: '/tender-scheme/templates', showInMenu: false },
          },
          {
            path: 'templates/edit/:id',
            name: 'TenderSchemeTemplateEdit',
            component: () => import('@/views/tenderScheme/TemplateForm.vue'),
            meta: { title: '编辑模板', parentPath: '/tender-scheme/templates', showInMenu: false },
          },
          {
            path: 'projects', // This will be the list page for tender schemes
            name: 'TenderSchemeProjectList',
            component: () => import('@/views/tenderScheme/SchemeList.vue'), // Point to the newly created ProjectList
            // meta: { title: '方案制作', icon: 'ProjectOutlined' }, 
            meta: { title: '方案制作' }, 
          },
          {
            path: 'projects/create',
            name: 'TenderSchemeProjectCreate',
            component: () => import('@/views/tenderScheme/SchemeForm.vue'), 
            meta: { title: '新建方案', parentPath: '/tender-scheme/projects', showInMenu: false },
          },
          {
            path: 'projects/edit/:id',
            name: 'TenderSchemeProjectEdit',
            component: () => import('@/views/tenderScheme/SchemeForm.vue'), 
            meta: { title: '编辑方案', parentPath: '/tender-scheme/projects', showInMenu: false },
          },
          {
            path: 'projects/view/:id',
            name: 'TenderSchemeProjectView',
            component: () => import('@/views/tenderScheme/SchemeView.vue'),
            props: true,
            meta: { title: '查看方案', requiresAuth: true, breadcrumb: [{ name: 'TenderSchemeProjectList', title: '方案制作' }, { title: '查看' }] }
          }
        ]
      },
      // --- 招标文件管理 ---
      {
        path: '/bidding-document',
        name: 'BiddingDocumentModule', // 顶级菜单路由名称
        component: () => import('@/layouts/Menu.vue'),
        props: { id: 'BiddingDocumentModule' },
        redirect: { name: 'BiddingDocumentTemplateList' }, // 默认重定向到模板列表
        meta: { requiresAuth: true, title: '招标文件', antIcon: 'FileWordOutlined' }, // 选择一个合适的图标
        children: [
          // {
          //   path: 'bidding-document/template-list',
          //   name: 'BiddingDocumentTemplateList',
          //   component: () => import('@/views/biddingDocument/template/TemplateList.vue'),
          //   meta: { title: '招标文件模板', requiresAuth: true },
          // },
          // {
          //   path: 'bidding-document/file-list',
          //   name: 'BiddingDocumentFileList',
          //   component: () => import('@/views/biddingDocument/file/FileList.vue'),
          //   meta: { title: '招标文件管理', requiresAuth: true },
          // },
          {
            path: 'templates', // /bidding-document/templates
            name: 'BiddingDocumentTemplateList',
            component: () => import('@/views/biddingDocument/TemplateList.vue'), // 占位符视图
            meta: { title: '模板管理', requiresAuth: true, keepAlive: true }
          },
          {
            path: 'templates/new',
            name: 'BiddingDocumentTemplateNew',
            component: () => import('@/views/biddingDocument/TemplateForm.vue'), // 占位符视图
            meta: {showInMenu: false, title: '新建模板', requiresAuth: true, breadcrumb: [{ name: 'BiddingDocumentTemplateList', title: '模板管理' }, { title: '新建' }] }
          },
          {
            path: 'templates/edit/:id',
            name: 'BiddingDocumentTemplateEdit',
            component: () => import('@/views/biddingDocument/TemplateForm.vue'), // 占位符视图
            props: true,
            meta: { title: '编辑模板', requiresAuth: true, breadcrumb: [{ name: 'BiddingDocumentTemplateList', title: '模板管理' }, { title: '编辑' }] }
          },
          {
            path: 'files', // /bidding-document/files
            name: 'BiddingDocumentFileList',
            component: () => import('@/views/biddingDocument/FileList.vue'), // 占位符视图
            meta: { title: '文件管理', requiresAuth: true, keepAlive: true }
          },
          {
            path: 'files/new',
            name: 'BiddingDocumentFileNew',
            component: () => import('@/views/biddingDocument/FileForm.vue'), // 占位符视图
            meta: { showInMenu: false, title: '新建文件', requiresAuth: true, breadcrumb: [{ name: 'BiddingDocumentFileList', title: '文件管理' }, { title: '新建' }] }
          },
          {
            path: 'files/edit/:id',
            name: 'BiddingDocumentFileEdit',
            component: () => import('@/views/biddingDocument/FileForm.vue'), // 占位符视图
            props: true,
            meta: { title: '编辑文件', requiresAuth: true, breadcrumb: [{ name: 'BiddingDocumentFileList', title: '文件管理' }, { title: '编辑' }] }
          },
          {
            path: 'files/view/:id',
            name: 'BiddingDocumentFileView',
            component: () => import('@/views/biddingDocument/FileView.vue'), // 占位符视图
            props: true,
            meta: { title: '查看文件', requiresAuth: true, breadcrumb: [{ name: 'BiddingDocumentFileList', title: '文件管理' }, { title: '查看' }] }
          }
        ]
      },
      // --- 系统管理 (用户管理、角色管理) 
      {
        path: '/system',
        name: 'SystemManagement',
        component: () => import('@/layouts/Menu.vue'),
        props: { id: 'SystemManagement' },
        redirect: { name: 'SystemUsers' }, // 修改这里：默认到 SystemUsers
        meta: { requiresAuth: true, title: '系统管理', antIcon: 'SettingOutlined' }, // 确保使用 antIcon
        children: [
          {
            path: '/system/users',
            name: 'SystemUsers',
            component: UserManagement,
            meta: { requiresAuth: true, title: '用户管理', roles: ['superadmin'] } // 假设只有superadmin能管理用户
          },
          {
            path: '/system/roles',
            name: 'SystemRoles',
            component: RoleManagement, // 修改这里的 component
            meta: { requiresAuth: true, title: '角色管理', roles: ['superadmin'] } // 假设只有superadmin能管理角色
          },
          {
            path: 'template-fields', // 路径: /system/template-fields
            name: 'SystemTemplateFieldList',
            component: () => import('@/views/system/TemplateFieldList.vue'),
            meta: { title: '模板字段管理', requiresAuth: true, keepAlive: true }, // keepAlive 根据需要设置
          },
          {
            path: 'template-fields/create', // 路径: /system/template-fields/create
            name: 'SystemTemplateFieldCreate',
            component: () => import('@/views/system/TemplateFieldForm.vue'),
            meta: { title: '新建模板字段', requiresAuth: true, parentPath: '/system/template-fields', showInMenu: false }
          },
          {
            path: 'template-fields/edit/:id', // 路径: /system/template-fields/edit/:id
            name: 'SystemTemplateFieldEdit',
            component: () => import('@/views/system/TemplateFieldForm.vue'),
            props: true, // 将路由参数作为 props 传递给组件
            meta: { title: '编辑模板字段', requiresAuth: true, parentPath: '/system/template-fields', showInMenu: false }
          },
        ]
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue') // 假设有一个 NotFound.vue 页面
  }
]

export default constantRouterMap