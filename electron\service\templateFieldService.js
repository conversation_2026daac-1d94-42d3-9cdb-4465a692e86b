'use strict';

const { sqlitedbService } = require('./database/sqlitedb');

/**
 * 模板字段池服务
 * @class
 */
class TemplateFieldService {
  constructor(ctx) {
    this.ctx = ctx; // ctx 可能在IPC中不完全可用，主要用于HTTP
    this.db = sqlitedbService.db;
    this.tableName = sqlitedbService.templateFieldsTableName;
  }

  /**
   * 创建模板字段
   * @param {object} data - 字段数据 { field_name, field_key, field_type?, remark? }
   * @returns {Promise<object>} 创建结果 { success, data?, message?, statusCode? }
   */
  async create(data) {
    const { field_name, field_key, field_type = 'custom', remark = '' } = data;

    if (!field_name || !field_key) {
      return { success: false, message: '字段名称和字段键不能为空', statusCode: 400 };
    }

    // 检查 field_key 是否已存在
    try {
      const checkSql = `SELECT id FROM ${this.tableName} WHERE field_key = @field_key`;
      const existingField = this.db.prepare(checkSql).get({ field_key });
      if (existingField) {
        return { success: false, message: `字段键 "${field_key}" 已存在`, statusCode: 409 }; // 409 Conflict
      }
    } catch (error) {
        console.error('Error checking for existing field_key:', error);
        return { success: false, message: `检查字段键唯一性失败: ${error.message}`, statusCode: 500 };
    }
    
    const insertSql = `
      INSERT INTO ${this.tableName} (field_name, field_key, field_type, remark, created_at, updated_at)
      VALUES (@field_name, @field_key, @field_type, @remark, datetime('now', 'localtime'), datetime('now', 'localtime'))
    `;
    try {
      const stmt = this.db.prepare(insertSql);
      const result = stmt.run({
        field_name: field_name,
        field_key: field_key,
        field_type: field_type,
        remark: remark
      });

      if (result.lastInsertRowid) {
        const newFieldResponse = await this.getById(result.lastInsertRowid);
        if (newFieldResponse.success) {
          return { success: true, data: newFieldResponse.data, message: '模板字段创建成功' };
        }
        return { success: false, message: '创建模板字段后获取详情失败', statusCode: 500 };
      }
      return { success: false, message: '创建模板字段失败', statusCode: 500 };
    } catch (error) {
      console.error('Error creating template field:', error);
      // SQLite UNIQUE constraint on field_key might throw here if not caught by pre-check (race condition)
      if (error.message && error.message.includes('UNIQUE constraint failed: template_fields.field_key')) {
          return { success: false, message: `字段键 "${field_key}" 已存在 (DB constraint)`, statusCode: 409 };
      }
      return { success: false, message: `创建模板字段失败: ${error.message}`, statusCode: 500 };
    }
  }

  /**
   * 获取模板字段列表 (可分页、筛选)
   * @param {object} params - 查询参数 { page, pageSize, field_name, field_key, field_type }
   * @returns {Promise<object>} { success, data: { list, pagination }?, message?, statusCode? }
   */
  async list(params = {}) {
    const { page = 1, pageSize = 10, field_name, field_key, field_type } = params;
    const offset = (page - 1) * pageSize;
    
    let whereClauses = [];
    let queryParams = {};

    if (field_name) {
      whereClauses.push('field_name LIKE @field_name_like');
      queryParams.field_name_like = `%${field_name}%`;
    }
    if (field_key) {
      whereClauses.push('field_key LIKE @field_key_like');
      queryParams.field_key_like = `%${field_key}%`;
    }
    if (field_type) {
      whereClauses.push('field_type = @field_type');
      queryParams.field_type = field_type;
    }

    const whereString = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : '';
    
    const dataSql = `
      SELECT * FROM ${this.tableName}
      ${whereString}
      ORDER BY updated_at DESC
      LIMIT @limit OFFSET @offset
    `;
    const countSql = `SELECT COUNT(id) as total FROM ${this.tableName} ${whereString}`;

    try {
      const list = this.db.prepare(dataSql).all({ ...queryParams, limit: pageSize, offset });
      const row = this.db.prepare(countSql).get(queryParams);
      const total = row ? row.total : 0;

      return {
        success: true,
        data: {
          list: list,
          pagination: {
            total,
            page: Number(page),
            pageSize: Number(pageSize),
            totalPages: Math.ceil(total / pageSize),
          },
        }
      };
    } catch (error) {
      console.error('Error listing template fields:', error);
      return { success: false, message: `获取模板字段列表失败: ${error.message}`, statusCode: 500 };
    }
  }

  /**
   * 根据ID获取模板字段详情
   * @param {number} id - 字段ID
   * @returns {Promise<object>} { success, data?, message?, statusCode? }
   */
  async getById(id) {
    const sql = `SELECT * FROM ${this.tableName} WHERE id = ?`;
    try {
      const field = this.db.prepare(sql).get(id);
      if (field) {
        return { success: true, data: field };
      }
      return { success: false, message: '未找到指定的模板字段', statusCode: 404 };
    } catch (error) {
      console.error(`Error getting template field by ID ${id}:`, error);
      return { success: false, message: `获取模板字段详情失败: ${error.message}`, statusCode: 500 };
    }
  }

  /**
   * 更新模板字段
   * @param {number} id - 字段ID
   * @param {object} data - 更新数据 { field_name?, field_key?, field_type?, remark? }
   * @returns {Promise<object>} { success, data?, message?, statusCode? }
   */
  async update(id, data) {
    const { field_name, field_key, field_type, remark } = data;

    if (Object.keys(data).length === 0) {
        return { success: false, message: '没有提供任何更新数据', statusCode: 400 };
    }
    
    const existingFieldResponse = await this.getById(id);
    if (!existingFieldResponse.success) {
        return existingFieldResponse; // ID不存在
    }

    // 如果要更新 field_key，检查新 field_key 是否已存在 (且不属于当前ID)
    if (field_key && field_key !== existingFieldResponse.data.field_key) {
        try {
            const checkSql = `SELECT id FROM ${this.tableName} WHERE field_key = @field_key AND id != @id`;
            const conflictingField = this.db.prepare(checkSql).get({ field_key, id });
            if (conflictingField) {
            return { success: false, message: `更新失败：字段键 "${field_key}" 已被其他字段使用`, statusCode: 409 };
            }
        } catch (error) {
            console.error('Error checking for existing field_key during update:', error);
            return { success: false, message: `更新时检查字段键唯一性失败: ${error.message}`, statusCode: 500 };
        }
    }

    let fieldsToUpdate = [];
    let paramsForRun = { id };

    if (field_name !== undefined) {
      fieldsToUpdate.push('field_name = @field_name');
      paramsForRun.field_name = field_name;
    }
    if (field_key !== undefined) {
      fieldsToUpdate.push('field_key = @field_key');
      paramsForRun.field_key = field_key;
    }
    if (field_type !== undefined) {
      fieldsToUpdate.push('field_type = @field_type');
      paramsForRun.field_type = field_type;
    }
    if (remark !== undefined) {
      fieldsToUpdate.push('remark = @remark');
      paramsForRun.remark = remark;
    }
    
    if (fieldsToUpdate.length === 0) {
      // 这种情况理论上不会发生，因为前面检查了 Object.keys(data).length
      // 但作为安全措施，如果真的没有字段要更新，则返回当前数据
      return { success: true, data: existingFieldResponse.data, message: '数据无变化' };
    }

    fieldsToUpdate.push("updated_at = datetime('now', 'localtime')");
    const setClauses = fieldsToUpdate.join(', ');

    const updateSql = `UPDATE ${this.tableName} SET ${setClauses} WHERE id = @id`;

    try {
      const stmt = this.db.prepare(updateSql);
      const result = stmt.run(paramsForRun);

      if (result.changes > 0) {
        const updatedFieldResponse = await this.getById(id);
        if (updatedFieldResponse.success) {
            return { success: true, data: updatedFieldResponse.data, message: '模板字段更新成功' };
        }
        return { success: false, message: '更新模板字段后获取详情失败', statusCode: 500 };
      } else {
        // 没有行被改变，可能因为ID不存在（但已检查），或者新旧值相同
        const currentDataResponse = await this.getById(id);
        if (currentDataResponse.success) {
            return { success: true, data: currentDataResponse.data, message: '数据无实质变化或更新失败' };
        }
        return { success: false, message: '操作完成但获取最新数据失败', statusCode: 500 };
      }
    } catch (error) {
      console.error(`Error updating template field ID ${id}:`, error);
      if (error.message && error.message.includes('UNIQUE constraint failed: template_fields.field_key')) {
        return { success: false, message: `更新失败：字段键 "${field_key}" 已被其他字段使用 (DB constraint)`, statusCode: 409 };
      }
      return { success: false, message: `更新模板字段数据库操作失败: ${error.message}`, statusCode: 500 };
    }
  }

  /**
   * 删除模板字段
   * @param {number} id - 字段ID
   * @returns {Promise<object>} { success, message?, statusCode? }
   */
  async delete(id) {
    const sql = `DELETE FROM ${this.tableName} WHERE id = ?`;
    try {
      const stmt = this.db.prepare(sql);
      const result = stmt.run(id);
      if (result.changes > 0) {
        return { success: true, message: '模板字段删除成功' };
      }
      return { success: false, message: '未删除任何模板字段 (可能ID不存在)', statusCode: 404 };
    } catch (error) {
      console.error(`Error deleting template field ID ${id}:`, error);
      return { success: false, message: `删除模板字段失败: ${error.message}`, statusCode: 500 };
    }
  }
}

module.exports = TemplateFieldService; 