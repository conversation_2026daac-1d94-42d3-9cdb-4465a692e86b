<template>
  <div class="risk-panel" :class="{ 'risk-panel-collapsed': collapsed }">
    <!-- 面板头部 -->
    <div class="risk-panel-header" @click="toggleCollapsed">
      <div class="header-left">
        <WarningOutlined class="risk-icon" />
        <span class="title">风险评估</span>
        <a-badge :count="totalRiskCount" :offset="[10, 0]" />
      </div>
      <div class="header-right">
        <a-button type="text" size="small" @click.stop="refreshRiskAssessment" :loading="refreshing" title="刷新风险评估">
          <ReloadOutlined />
        </a-button>
        <a-button type="text" size="small" @click.stop="toggleCollapsed">
          <RightOutlined :class="{ 'collapsed-icon': collapsed }" />
        </a-button>
      </div>
    </div>

    <!-- 面板内容 -->
    <div class="risk-panel-content" v-show="!collapsed">
      <!-- 整体风险概览 -->
      <div class="risk-overview" v-if="overallRisk">
        <div class="overall-level">
          <span class="level-label">整体风险等级：</span>
          <a-tag :color="getRiskLevelColor(overallRisk.overall_risk_level)" class="level-tag">{{ overallRisk.overall_risk_level }}风险</a-tag>
        </div>
        <div class="overall-score">
          <span class="score-label">风险评分：</span>
          <span class="score-value" :style="{ color: getRiskScoreColor(overallRisk.overall_risk_score) }">
            {{ overallRisk.overall_risk_score }}
          </span>
        </div>
        <div class="risk-summary" v-if="overallRisk.risk_summary">
          <p>{{ overallRisk.risk_summary }}</p>
        </div>
      </div>

      <!-- 字段风险列表 -->
      <div class="field-risks-section" v-if="fieldRisks && fieldRisks.length > 0">
        <h4 class="section-title">字段风险详情</h4>
        <div class="field-risks-list">
          <div
            v-for="(risk, index) in fieldRisks"
            :key="risk.field_key"
            class="field-risk-item"
            :class="{ active: activeRiskIndex === index }"
            @click="scrollToField(risk)"
            @mouseenter="highlightField(risk, true)"
            @mouseleave="highlightField(risk, false)"
          >
            <div class="risk-item-header">
              <span class="field-name">{{ risk.field_name }}</span>
              <a-tag :color="getRiskLevelColor(risk.risk_level)" size="small">
                {{ risk.risk_level }}
              </a-tag>
            </div>
            <div class="risk-reason">{{ risk.reason }}</div>
            <div class="risk-suggestion" v-if="risk.suggestion">
              <strong>建议：</strong>
              {{ risk.suggestion }}
            </div>
            <div class="risk-score">
              <span>评分：{{ risk.risk_score }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 分类风险列表 -->
      <div class="category-risks-section" v-if="categoryRisks && categoryRisks.length > 0">
        <h4 class="section-title">分类风险分析</h4>
        <div class="category-risks-list">
          <div v-for="risk in categoryRisks" :key="risk.category" class="category-risk-item">
            <div class="category-header">
              <span class="category-name">{{ risk.category }}</span>
              <a-tag :color="getRiskLevelColor(risk.level)" size="small">
                {{ risk.level }}
              </a-tag>
            </div>
            <div class="category-description">{{ risk.description }}</div>
            <div class="category-score">
              <span>评分：{{ risk.score }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 改进建议 -->
      <div class="recommendations-section" v-if="recommendations && recommendations.length > 0">
        <h4 class="section-title">改进建议</h4>
        <ul class="recommendations-list">
          <li v-for="(recommendation, index) in recommendations" :key="index">
            {{ recommendation }}
          </li>
        </ul>
      </div>

      <!-- 无风险状态 -->
      <div class="no-risk-state" v-if="!hasAnyRisk">
        <a-empty description="暂无风险评估结果">
          <a-button type="primary" @click="startRiskAssessment">开始风险评估</a-button>
        </a-empty>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { WarningOutlined, ReloadOutlined, RightOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

// Props定义
const props = defineProps({
  riskAssessmentData: {
    type: Object,
    default: null
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emit定义
const emit = defineEmits(['scrollToField', 'highlightField', 'refreshAssessment', 'startAssessment'])

// 响应式数据
const collapsed = ref(false)
const refreshing = ref(false)
const activeRiskIndex = ref(-1)

// 计算属性
const overallRisk = computed(() => {
  return props.riskAssessmentData || null
})

const fieldRisks = computed(() => {
  return props.riskAssessmentData?.field_risks || []
})

const categoryRisks = computed(() => {
  return props.riskAssessmentData?.detailed_risks || []
})

const recommendations = computed(() => {
  return props.riskAssessmentData?.recommendations || []
})

const totalRiskCount = computed(() => {
  return fieldRisks.value.length + categoryRisks.value.length
})

const hasAnyRisk = computed(() => {
  return props.riskAssessmentData && (fieldRisks.value.length > 0 || categoryRisks.value.length > 0 || overallRisk.value?.risk_summary)
})

// 方法
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

const getRiskLevelColor = level => {
  const colorMap = {
    高: 'red',
    中: 'orange',
    低: 'yellow'
  }
  return colorMap[level] || 'default'
}

const getRiskScoreColor = score => {
  if (score >= 80) return '#ff4d4f'
  if (score >= 60) return '#ffa500'
  if (score >= 40) return '#fadb14'
  return '#52c41a'
}

const scrollToField = risk => {
  activeRiskIndex.value = fieldRisks.value.findIndex(r => r.field_key === risk.field_key)
  emit('scrollToField', risk)
}

const highlightField = (risk, highlight) => {
  emit('highlightField', risk, highlight)
}

const refreshRiskAssessment = async () => {
  refreshing.value = true
  try {
    await emit('refreshAssessment')
    message.success('风险评估已刷新')
  } catch (error) {
    message.error('刷新失败：' + error.message)
  } finally {
    refreshing.value = false
  }
}

const startRiskAssessment = () => {
  emit('startAssessment')
}

// 监听loading状态
watch(
  () => props.loading,
  newVal => {
    if (newVal) {
      refreshing.value = true
    } else {
      refreshing.value = false
    }
  }
)
</script>

<style scoped>
.risk-panel {
  width: 350px;
  height: calc(100vh - 180px);
  flex-shrink: 0;

  background: white;
  /* border-left: 1px solid #e8e8e8; */
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
}

.risk-panel-collapsed {
  width: 40px;
  overflow: hidden;
}

.risk-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
  cursor: pointer;
  background: #fafafa;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.risk-icon {
  color: #ff4d4f;
  font-size: 16px;
}

.title {
  font-weight: 500;
  font-size: 14px;
  /* 不换行 */
  white-space: nowrap;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.collapsed-icon {
  transform: rotate(180deg);
  transition: transform 0.3s ease;
}

.risk-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.risk-overview {
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.overall-level,
.overall-score {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.level-label,
.score-label {
  margin-right: 8px;
  color: #666;
}

.level-tag {
  margin: 0;
}

.score-value {
  font-weight: 500;
  font-size: 16px;
}

.risk-summary p {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 13px;
  line-height: 1.5;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 6px;
}

.field-risks-list,
.category-risks-list {
  space-y: 8px;
}

.field-risk-item,
.category-risk-item {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.field-risk-item:hover,
.category-risk-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

.field-risk-item.active {
  border-color: #1890ff;
  background: #f0f7ff;
}

.risk-item-header,
.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.field-name,
.category-name {
  font-weight: 500;
  color: #333;
}

.risk-reason,
.category-description {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.risk-suggestion {
  color: #666;
  font-size: 12px;
  line-height: 1.4;
  margin-bottom: 6px;
}

.risk-suggestion strong {
  color: #1890ff;
}

.risk-score,
.category-score {
  font-size: 12px;
  color: #999;
  text-align: right;
}

.recommendations-list {
  margin: 0;
  padding-left: 20px;
}

.recommendations-list li {
  margin-bottom: 6px;
  color: #666;
  font-size: 13px;
  line-height: 1.4;
}

.no-risk-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}
</style>
