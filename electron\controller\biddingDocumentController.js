'use strict'

const BiddingDocumentService = require('../service/biddingDocumentService')

/**
 * 招标文件控制器
 * @class
 */
class BiddingDocumentController {
  constructor(ctx) {
    this.biddingDocumentService = new BiddingDocumentService()
  }

  /**
   * 创建招标文件
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async create(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body
    
    // 从请求头获取用户ID
    const userId = ctx && ctx.headers ? ctx.headers['x-user-id'] : null
    if (userId) {
      params.created_by = parseInt(userId)
      params.updated_by = parseInt(userId)
    }

    try {
      const result = await this.biddingDocumentService.create(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取招标文件列表
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async list(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.query || {}
    
    // 从请求头获取用户ID
    const userId = ctx && ctx.headers ? ctx.headers['x-user-id'] : null
    if (userId) {
      params.current_user_id = parseInt(userId)
    }

    try {
      const result = await this.biddingDocumentService.list(params)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取单个招标文件
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async getById(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的文件ID' }
    }

    try {
      const result = await this.biddingDocumentService.getById(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 更新招标文件
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async update(args, ctx) {
    const params = args && Object.keys(args).length > 0 ? args : ctx.request.body

    const { id, ...updateData } = params
    const documentId = parseInt(id)

    if (isNaN(documentId)) {
      return { success: false, message: '无效的文件ID' }
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return { success: false, message: '没有提供需要更新的数据' }
    }

    // 从请求头获取用户ID
    const userId = ctx && ctx.headers ? ctx.headers['x-user-id'] : null
    if (userId) {
      updateData.updated_by = parseInt(userId)
    }

    try {
      const result = await this.biddingDocumentService.update(documentId, updateData)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 删除招标文件
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async delete(args, ctx) {
    let id
    if (args && args.id) {
      id = parseInt(args.id)
    } else {
      id = parseInt(ctx.query?.id || ctx.request?.body?.id)
    }

    if (isNaN(id)) {
      return { success: false, message: '无效的文件ID' }
    }

    try {
      const result = await this.biddingDocumentService.delete(id)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }

  /**
   * 获取招标文件历史记录
   * @param {Object} args - 前端传递的参数
   * @param {Object} ctx - HTTP请求时的koa上下文对象
   */
  async getHistory(args, ctx) {
    let documentId
    if (args && args.document_id) {
      documentId = parseInt(args.document_id)
    } else {
      documentId = parseInt(ctx.query?.document_id || ctx.request?.body?.document_id)
    }

    if (isNaN(documentId)) {
      return { success: false, message: '无效的文件ID' }
    }

    try {
      const result = await this.biddingDocumentService.getHistory(documentId)
      return result
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
}

BiddingDocumentController.toString = () => '[class BiddingDocumentController]'

module.exports = BiddingDocumentController
