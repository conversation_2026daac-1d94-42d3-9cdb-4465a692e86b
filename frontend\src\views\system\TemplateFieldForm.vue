<template>
  <!-- <div class="page-container form-container"> -->
     <a-page-header :title="isEditMode ? '编辑模版字段' : '新建模版字段'" @back="handleCancel" />
    <a-card>
    <!-- <a-card :bordered="false"> -->
      <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" @finish="onFinish" @finishFailed="onFinishFailed">
        <a-form-item label="字段名称" name="field_name">
          <a-input v-model:value="formState.field_name" placeholder="请输入字段名称，如：项目名称" />
        </a-form-item>

        <a-form-item label="字段键" name="field_key">
          <a-input v-model:value="formState.field_key" placeholder="请输入字段唯一键，如：project_name" :disabled="isEditMode" />
          <div v-if="isEditMode" class="form-item-extra-info">编辑模式下字段键不可修改。</div>
        </a-form-item>

        <a-form-item label="字段类型" name="field_type">
          <a-select v-model:value="formState.field_type" placeholder="请选择字段类型">
            <a-select-option value="custom">自定义字段</a-select-option>
            <a-select-option value="default">系统默认字段</a-select-option>
            <a-select-option value="system_reserved">系统预留字段</a-select-option>
            <!-- 根据需要添加更多 -->
          </a-select>
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="4" />
        </a-form-item>

        <a-form-item :wrapper-col="{ offset: 6, span: 16 }">
          <a-button type="primary" html-type="submit" :loading="isLoading">
            {{ isEditMode ? '更新' : '创建' }}
          </a-button>
          <a-button style="margin-left: 10px" @click="goBack">返回</a-button>
        </a-form-item>
      </a-form>
    <!-- </a-card> -->
     </a-card>
  <!-- </div> -->
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import api from '@/api'

const router = useRouter()
const route = useRoute()
const formRef = ref()

const isLoading = ref(false)
const isEditMode = computed(() => !!route.params.id)
const fieldId = computed(() => (route.params.id ? parseInt(route.params.id, 10) : null))

const formState = reactive({
  field_name: '',
  field_key: '',
  field_type: 'custom', // 默认类型
  remark: ''
})

// 验证规则
const rules = {
  field_name: [{ required: true, message: '请输入字段名称', trigger: 'blur' }],
  field_key: [
    { required: true, message: '请输入字段键', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '字段键只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  field_type: [{ required: true, message: '请选择字段类型', trigger: 'change' }]
}

const handleCancel = () => {
  router.push({ name: 'SystemTemplateFieldList' })
}

// 获取字段详情 (编辑模式)
const fetchFieldDetails = async () => {
  if (!isEditMode.value || !fieldId.value) return
  isLoading.value = true
  try {
    const response = await api.templateField.getById({ id: fieldId.value })
    if (response && response.success && response.data) {
      Object.assign(formState, response.data)
    } else {
      message.error(response.message || '获取字段详情失败')
      goBack() // 获取失败则返回列表
    }
  } catch (error) {
    message.error('获取字段详情时发生错误: ' + error.message)
    goBack()
  } finally {
    isLoading.value = false
  }
}

// 提交表单
const onFinish = async values => {
  isLoading.value = true
  try {
    let response
    const payload = { ...values }

    if (isEditMode.value && fieldId.value) {
      // 编辑模式下，不应传递 field_key, 因为它通常是不可变的，或者其修改逻辑已在service层处理
      // 如果允许修改，需要确保后端 update 逻辑正确处理（通常不允许或需要特殊权限）
      // 为了安全起见，我们从 payload 中移除 field_key，除非后端明确支持其更新
      // const { field_key, ...updatePayload } = payload; // 如果 field_key 不可更改
      // response = await templateFieldApi.updateTemplateField(fieldId.value, updatePayload);

      // 根据我们 Service 层的设计，field_key 是可以更新的（但会检查唯一性），所以这里传递它
      response = await api.templateField.update({ id: route.params.id, ...payload })
    } else {
      response = await api.templateField.create(payload)
    }

    if (response && response.success) {
      message.success(response.message || (isEditMode.value ? '更新成功！' : '创建成功！'))
      goBack() // 操作成功后返回列表页
    } else {
      message.error(response.message || (isEditMode.value ? '更新失败' : '创建失败'))
    }
  } catch (err) {
    message.error(err.message || '操作失败')
  } finally {
    isLoading.value = false
  }
}

const onFinishFailed = errorInfo => {
  console.log('表单验证失败:', errorInfo)
  message.error('请检查表单输入项！')
}

// 返回列表页
const goBack = () => {
  // 假设列表页路由名称为 'SystemTemplateFieldList'
  router.push({ name: 'SystemTemplateFieldList' })
}

onMounted(() => {
  if (isEditMode.value) {
    fetchFieldDetails()
  }
})
</script>

<style scoped>
.form-container {
  padding: 16px;
  /* max-width: 800px;  */
  margin: 0 auto;
}
.form-item-extra-info {
  color: #999;
  font-size: 12px;
  line-height: 1.5;
}
</style>
