<template>
  <!-- 编辑器外层容器，用于居中显示和背景设置 -->
  <div class="tiptap-editor-wrapper">
    <!-- 吸顶工具栏 - 移到外层容器，充满宽度 -->
    <div v-if="editor && showToolbar" class="sticky-toolbar-container">
      <editor-toolbar :editor="editor" :insert-field-fn="insertFieldViaToolbar" />
    </div>

    <div class="tiptap-editor-container">
      <div :class="['editor-wrapper', { 'is-focused': isFocused, 'is-disabled': !finalEditable, 'is-fill-mode': fillMode }]" :style="{ minHeight: minHeight }">
        <editor-content :editor="editor" />
      </div>

      <!-- 自定义字段气泡菜单 -->
      <CustomFieldBubbleMenu v-if="editor" :editor="editor" />

      <!-- 风险提示组件 - 集成到编辑器内部 -->
      <RiskTooltip ref="riskTooltipRef" :visible="riskTooltipVisible" :risk-data="currentRiskData" :target-element="riskTooltipTarget" />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onBeforeUnmount, onMounted, onUnmounted, toRefs, computed, nextTick, provide } from 'vue'
import { useEditor, EditorContent } from '@tiptap/vue-3'
import { Plugin, PluginKey, TextSelection, NodeSelection } from '@tiptap/pm/state'
import { Slice, Fragment } from '@tiptap/pm/model'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import Underline from '@tiptap/extension-underline'
import TextAlign from '@tiptap/extension-text-align'
import TextStyle from '@tiptap/extension-text-style'
import FontFamily from '@tiptap/extension-font-family'
import Color from '@tiptap/extension-color'
import Highlight from '@tiptap/extension-highlight'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import { BubbleMenu } from '@tiptap/vue-3'

// 导入自定义字段扩展，路径相对于当前文件
import { CustomFieldExtension } from './extensions/CustomFieldExtension'
import { FillModeExtension } from './extensions/FillModeExtension'
import { ReadonlyContentExtension } from './extensions/ReadonlyContentExtension'
import { TemplateNodeAttributesExtension } from './extensions/TemplateNodeAttributesExtension'
import { ImageExtension } from './extensions/ImageExtension'
import { FontSizeExtension } from './extensions/FontSizeExtension'
import { ParagraphSpacingExtension } from './extensions/ParagraphSpacingExtension'
import RiskHighlightExtension from './extensions/RiskHighlightExtension'
import RiskTooltip from './RiskTooltip.vue'
import EditorToolbar from './EditorToolbar.vue'
import CustomFieldBubbleMenu from './CustomFieldBubbleMenu.vue'

const props = defineProps({
  modelValue: { type: Object, default: () => ({ type: 'doc', content: [{ type: 'paragraph' }] }) },
  placeholder: { type: String, default: '请输入内容...' },
  minHeight: { type: String, default: '200px' },
  editable: { type: Boolean, default: true },
  fillMode: { type: Boolean, default: false },
  showToolbar: { type: Boolean, default: true },
  readonlyParagraphIds: { type: Array, default: () => [] }, // 只读段落ID数组（数据库中的段落ID）
  currentTemplateId: { type: [String, Number], default: null }, // 当前模板ID
  riskData: { type: Array, default: () => [] } // 风险数据数组
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur'])

const { modelValue, placeholder, editable, fillMode, minHeight, showToolbar, readonlyParagraphIds, currentTemplateId, riskData } = toRefs(props)
const isFocused = ref(false)
let internalUpdateInProgress = false

// 风险提示相关状态 - 从父组件移入
const riskTooltipVisible = ref(false)
const currentRiskData = ref(null)
const riskTooltipTarget = ref(null)
const riskTooltipRef = ref(null) // RiskTooltip 组件的引用

// 风险点击处理方法 - 从父组件移入
const handleRiskClick = (riskData, event) => {
  if (riskData) {
    // 点击时显示/隐藏提示框
    if (currentRiskData.value?.field_key === riskData.field_key && riskTooltipVisible.value) {
      hideRiskTooltip()
    } else {
      currentRiskData.value = riskData
      riskTooltipTarget.value = event.target
      riskTooltipVisible.value = true
    }
  }
}

// 全局点击事件处理 - 点击其他地方隐藏风险提示
const handleGlobalClick = event => {
  if (!riskTooltipVisible.value) return

  const target = event.target
  if (!target) return

  // 检查点击目标是否在风险提示组件内
  const tooltipElement = document.querySelector('.risk-tooltip')
  if (tooltipElement && tooltipElement.contains(target)) {
    return
  }

  // 检查点击目标是否在风险高亮区域内
  // 包括直接点击和点击子元素的情况
  if (target.closest('.risk-highlight') || target.classList?.contains('risk-highlight')) {
    return
  }

  // 检查点击目标是否在编辑器内部的风险相关元素上
  const editorElement = document.querySelector('.tiptap-editor-container')
  if (editorElement && editorElement.contains(target)) {
    // 如果点击的是编辑器内的风险相关元素，不隐藏提示框
    const isRiskRelated =
      target.hasAttribute('data-risk-level') || target.hasAttribute('data-field-key') || target.closest('[data-risk-level]') || target.closest('[data-field-key]')

    if (isRiskRelated) {
      return
    }
  }

  // 如果点击的不是风险相关区域，隐藏提示框
  hideRiskTooltip()
}

// 隐藏风险提示框的统一方法
const hideRiskTooltip = () => {
  riskTooltipVisible.value = false
  currentRiskData.value = null
  riskTooltipTarget.value = null
}

// 键盘事件处理 - 按 Escape 键隐藏风险提示
const handleKeyDown = event => {
  if (event.key === 'Escape' && riskTooltipVisible.value) {
    hideRiskTooltip()
  }
}

// 节流函数 - 限制函数调用频率
const throttle = (func, delay) => {
  let timeoutId
  let lastExecTime = 0
  return function (...args) {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      func.apply(this, args)
      lastExecTime = currentTime
    } else {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        func.apply(this, args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

// 滚动事件处理 - 动态计算提示组件位置
const handleScroll = throttle(() => {
  if (riskTooltipVisible.value && riskTooltipRef.value) {
    // 调用 RiskTooltip 组件的 calculatePosition 方法重新计算位置
    riskTooltipRef.value.calculatePosition()
  }
}, 16) // 约60fps的更新频率

// 提供填充模式状态给子组件（如CustomFieldNodeView）
provide('fillMode', fillMode)

// Helper function to sanitize text nodes (can be moved to a utility file if used elsewhere)
/**
 * 递归遍历 Tiptap JSON 节点，确保所有 'text' 类型的节点都有一个字符串 'text' 属性。
 * @param {object} node - 当前遍历到的 Tiptap 节点。
 */
function sanitizeTextNodesInTiptapJSON(node) {
  if (!node || typeof node !== 'object') {
    return
  }

  if (node.type === 'text') {
    if (typeof node.text !== 'string') {
      // console.warn('[TiptapEditor] Sanitizing text node:', JSON.stringify(node));
      node.text = '' // 如果 text 属性不是字符串（或缺失），则设为空字符串
    }
  }

  if (node.content && Array.isArray(node.content)) {
    for (const childNode of node.content) {
      sanitizeTextNodesInTiptapJSON(childNode)
    }
  }
}

// 计算最终编辑状态：非填充模式下使用editable，填充模式下强制为true
const finalEditable = computed(() => {
  // 填充模式下编辑器必须可编辑，但内容权限由FillModeExtension控制
  if (fillMode.value) {
    return true
  }
  return editable.value
})

// Sanitize initial content before passing to useEditor
let initialContentForEditor
try {
  initialContentForEditor = JSON.parse(JSON.stringify(modelValue.value))
} catch (e) {
  console.error('[TiptapEditor] Error cloning initial modelValue:', e, modelValue.value)
  initialContentForEditor = { type: 'doc', content: [{ type: 'paragraph' }] } // Fallback
}
sanitizeTextNodesInTiptapJSON(initialContentForEditor)

// 构建扩展数组
const buildExtensions = () => {
  const extensions = [
    StarterKit.configure({
      history: true,
      paragraph: false, // 排除默认的段落扩展
      heading: false, // 排除默认的标题扩展
      bulletList: false, // 排除默认的无序列表扩展
      orderedList: false, // 排除默认的有序列表扩展
      horizontalRule: false, // 排除默认的水平线扩展
      codeBlock: false, // 移除代码块
      blockquote: false // 移除引用
    }),
    Placeholder.configure({
      placeholder: computed(() => {
        if (fillMode.value) {
          return '填充模式：只能编辑自定义字段内容'
        }
        return placeholder.value
      }).value
    }),
    Underline,
    TextAlign.configure({
      types: ['heading', 'paragraph']
    }),
    TextStyle, // 文本样式基础扩展
    FontFamily.configure({
      types: ['textStyle']
    }),
    Color.configure({
      types: ['textStyle']
    }),
    Highlight.configure({
      multicolor: true, // 支持多种高亮颜色
      HTMLAttributes: {
        class: 'custom-highlight'
      }
    }),
    FontSizeExtension.configure({
      types: ['textStyle']
    }),
    ParagraphSpacingExtension,
    // Table 配置已移至 TemplateNodeAttributesExtension
    TableRow,
    TableHeader,
    TableCell,
    CustomFieldExtension,
    FillModeExtension.configure({
      isFillModeActive: () => fillMode.value,
      getEditorInstance: () => editor.value
    }),
    ReadonlyContentExtension.configure({
      getReadonlyParagraphIds: () => readonlyParagraphIds.value,
      getCurrentTemplateId: () => currentTemplateId.value
    }),
    TemplateNodeAttributesExtension,
    ImageExtension
  ]

  // 安全地添加风险高亮扩展
  try {
    extensions.push(
      RiskHighlightExtension.configure({
        riskData: riskData.value || [],
        onRiskClick: handleRiskClick
      })
    )
  } catch (error) {
    console.error('Failed to configure RiskHighlightExtension:', error)
  }

  return extensions
}

const editor = useEditor({
  content: initialContentForEditor,
  editable: finalEditable.value,
  extensions: buildExtensions(),
  onUpdate: ({ editor: updatedEditor }) => {
    internalUpdateInProgress = true
    emit('update:modelValue', updatedEditor.getJSON())

    // 如果是填充模式，检查光标位置
    if (fillMode.value) {
      nextTick(() => {
        validateCursorPosition(updatedEditor)
      })
    }

    nextTick(() => {
      internalUpdateInProgress = false
    })
  },
  onFocus: () => {
    isFocused.value = true
    emit('focus')

    // 如果是填充模式，验证光标位置
    if (fillMode.value) {
      nextTick(() => {
        validateCursorPosition(editor.value)
      })
    }
  },
  onBlur: () => {
    isFocused.value = false
    emit('blur')
  },
  onCreate: ({ editor: createdEditor }) => {
    // 编辑器创建完成后，如果是填充模式，自动移动到第一个字段
    if (fillMode.value) {
      nextTick(() => {
        moveToFirstCustomField()
      })
    }

    // 延迟初始化风险高亮，确保编辑器完全准备就绪
    setTimeout(() => {
      if (riskData.value && Array.isArray(riskData.value) && riskData.value.length > 0) {
        updateRiskHighlights(riskData.value)
      }
    }, 100)
  }
})

watch(
  modelValue,
  newJsonValue => {
    if (editor.value && !editor.value.isDestroyed) {
      if (editor.value.isComposing) {
        return
      }

      if (internalUpdateInProgress) {
        return
      }

      const currentContentJSON = JSON.stringify(editor.value.getJSON())
      console.log('[TiptapEditor watch] currentContentJSON:', currentContentJSON)

      let newContentForSet
      try {
        newContentForSet = JSON.parse(JSON.stringify(newJsonValue))
      } catch (e) {
        console.error('[TiptapEditor watch] Error cloning newJsonValue:', e, newJsonValue)
        return
      }
      sanitizeTextNodesInTiptapJSON(newContentForSet)

      const newContentJSON = JSON.stringify(newContentForSet)

      if (currentContentJSON !== newContentJSON) {
        let isValidForProsemirror = true
        function checkForInvalidTextNodes(node) {
          if (!node || typeof node !== 'object') return
          if (node.type === 'text') {
            if (typeof node.text !== 'string') {
              console.error('[TiptapEditor VALIDATION FAIL] Found invalid text node BEFORE setContent:', JSON.stringify(node))
              isValidForProsemirror = false
            }
          }
          if (node.content && Array.isArray(node.content)) {
            for (const child of node.content) {
              if (!isValidForProsemirror) break
              checkForInvalidTextNodes(child)
            }
          }
        }
        checkForInvalidTextNodes(newContentForSet)

        if (!isValidForProsemirror) {
          console.error('[TiptapEditor] Content is still invalid before calling setContent. Aborting setContent.')
          return
        }
        newContentForSet.content[0].id = 1
        console.log(newContentForSet)

        editor.value.commands.setContent(newContentForSet, false)
      }
    }
  },
  { deep: true }
)

// 监听编辑状态变化
watch(finalEditable, newEditableValue => {
  if (editor.value && editor.value.isEditable !== newEditableValue) {
    editor.value.setEditable(newEditableValue)
  }
})

// 监听填充模式变化
watch(fillMode, newFillMode => {
  if (editor.value) {
    console.log('[TiptapEditor] 填充模式变化:', newFillMode)

    // 填充模式变化时，需要更新编辑器状态
    const shouldBeEditable = newFillMode ? true : editable.value
    if (editor.value.isEditable !== shouldBeEditable) {
      editor.value.setEditable(shouldBeEditable)
    }

    // 如果切换到填充模式，自动将光标移动到第一个字段
    if (newFillMode) {
      nextTick(() => {
        moveToFirstCustomField()
      })
    }

    // 更新占位符
    const placeholderText = newFillMode ? '填充模式：只能编辑自定义字段内容' : placeholder.value
    // 注意：Placeholder扩展的配置在创建时已经设置，这里可能需要重新配置
  }
})

// 检查并验证光标位置
const validateCursorPosition = editorInstance => {
  if (!editorInstance || !fillMode.value || editorInstance.isDestroyed) return

  const { state } = editorInstance
  const { selection } = state
  const { $from, $to } = selection

  // 检查当前选择是否在字段内
  const fromInField = $from.parent.type.name === 'customField'
  const toInField = $to.parent.type.name === 'customField'
  const sameParent = fromInField && toInField && $from.parent === $to.parent

  if (!sameParent) {
    // 如果光标不在有效位置，移动到最近的字段
    moveToClosestCustomField(editorInstance, $from.pos)
  }
}

// 移动光标到最近的自定义字段
const moveToClosestCustomField = (editorInstance, currentPos) => {
  if (!editorInstance || editorInstance.isDestroyed) return

  const fieldPositions = []

  // 查找所有自定义字段
  editorInstance.state.doc.descendants((node, pos) => {
    if (node.type.name === 'customField') {
      const fieldStart = pos + 1 // 字段内容开始位置
      const fieldEnd = pos + 1 + node.content.size // 字段内容结束位置
      fieldPositions.push({ start: fieldStart, end: fieldEnd })
    }
  })

  if (fieldPositions.length === 0) return

  // 找到最近的字段
  let closestPos = fieldPositions[0].start
  let minDistance = Math.abs(currentPos - fieldPositions[0].start)

  for (const field of fieldPositions) {
    let distance
    let targetPos

    if (currentPos < field.start) {
      distance = field.start - currentPos
      targetPos = field.start
    } else if (currentPos > field.end) {
      distance = currentPos - field.end
      targetPos = field.end
    } else {
      distance = 0
      targetPos = currentPos
    }

    if (distance < minDistance) {
      minDistance = distance
      closestPos = targetPos
    }
  }

  // 移动光标
  const { state } = editorInstance
  const newSelection = TextSelection.create(state.doc, closestPos)
  editorInstance.view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
}

// 移动光标到第一个自定义字段
const moveToFirstCustomField = () => {
  if (!editor.value || !fillMode.value) return

  let firstFieldPos = -1

  // 查找第一个自定义字段
  editor.value.state.doc.descendants((node, pos) => {
    if (firstFieldPos === -1 && node.type.name === 'customField') {
      firstFieldPos = pos + 1 // 字段内容开始位置
      return false // 停止遍历
    }
  })

  // 如果找到字段，移动光标
  if (firstFieldPos !== -1) {
    const { state } = editor.value
    const newSelection = TextSelection.create(state.doc, firstFieldPos)
    editor.value.view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
    editor.value.commands.focus()
  }
}

// 监听只读段落ID的变化
watch(
  readonlyParagraphIds,
  newIds => {
    console.log('只读段落ID更新:', newIds)
    // ReadonlyContentExtension会通过getReadonlyParagraphIds函数获取最新的值
    // 触发只读段落重新标记
    if (editor.value && !editor.value.isDestroyed) {
      // 通过强制刷新编辑器视图来触发只读段落标记
      nextTick(() => {
        editor.value.view.updateState(editor.value.state)
      })
    }
  },
  { deep: true }
)

// 监听当前模板ID的变化
watch(currentTemplateId, newId => {
  console.log('当前模板ID更新:', newId)
  // 触发只读段落重新标记
  if (editor.value && !editor.value.isDestroyed) {
    nextTick(() => {
      editor.value.view.updateState(editor.value.state)
    })
  }
})

// 组件挂载时添加全局事件监听器
onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
  document.addEventListener('keydown', handleKeyDown)
  window.addEventListener('scroll', handleScroll, true) // 使用捕获模式监听所有滚动事件
})

// 组件卸载时移除事件监听器和销毁编辑器
onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
  document.removeEventListener('keydown', handleKeyDown)
  window.removeEventListener('scroll', handleScroll, true)
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

const insertField = attributes => {
  if (editor.value && editor.value.isEditable) {
    editor.value.chain().focus().insertCustomField(attributes).run()
  }
}

const insertFieldViaToolbar = attributes => {
  insertField(attributes)
}

// 风险评估相关方法
const updateRiskHighlights = newRiskData => {
  if (editor.value && !editor.value.isDestroyed) {
    editor.value.commands.updateRiskHighlights(newRiskData)
  }
}

const scrollToField = fieldKey => {
  if (!editor.value || editor.value.isDestroyed) return

  // 查找指定字段并滚动到该位置
  editor.value.state.doc.descendants((node, pos) => {
    if (node.type.name === 'customField' && node.attrs.field_key === fieldKey) {
      const { state } = editor.value
      const newSelection = TextSelection.create(state.doc, pos + 1)
      editor.value.view.dispatch(state.tr.setSelection(newSelection).scrollIntoView())
      editor.value.commands.focus()
      return false // 停止遍历
    }
  })
}

const highlightField = (fieldKey, highlight) => {
  if (!editor.value || editor.value.isDestroyed) return

  // 临时高亮字段（可以通过添加临时类名实现）
  const editorDOM = editor.value.view.dom
  const fieldElements = editorDOM.querySelectorAll(`[data-field-key="${fieldKey}"]`)

  fieldElements.forEach(element => {
    if (highlight) {
      element.classList.add('temp-highlight')
    } else {
      element.classList.remove('temp-highlight')
    }
  })
}

// 监听风险数据变化
watch(
  riskData,
  newRiskData => {
    if (editor.value && !editor.value.isDestroyed) {
      nextTick(() => {
        updateRiskHighlights(newRiskData || [])
      })
    }
  },
  { deep: true, immediate: false }
)

defineExpose({
  insertField,
  getEditorInstance: () => editor.value,
  getJSON: () => editor.value?.getJSON(),
  getHTML: () => editor.value?.getHTML(),
  updateRiskHighlights,
  scrollToField,
  highlightField
})
</script>

<style scoped>
/* 导入样式文件 */
@import './index.less';
</style>
