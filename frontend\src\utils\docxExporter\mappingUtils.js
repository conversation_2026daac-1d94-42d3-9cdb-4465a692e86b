import * as docx from 'docx'

/**
 * 映射工具模块
 * 提供 Tiptap 节点属性到 docx.js 对应属性的映射函数
 */

/**
 * 将 Tiptap 节点的 textAlign 属性值映射到 docx.js 的 AlignmentType。
 * @param {string | undefined} align - Tiptap 节点的 textAlign 值。
 * @returns {AlignmentType} docx.js 的对齐类型。
 */
export function mapTextAlign(align) {
  if (!align) return docx.AlignmentType.LEFT

  switch (align.toUpperCase()) {
    case 'CENTER':
      return docx.AlignmentType.CENTER
    case 'RIGHT':
      return docx.AlignmentType.RIGHT
    case 'JUSTIFY':
      return docx.AlignmentType.BOTH // 'JUSTIFY' in Tiptap, 'BOTH' in docx
    default:
      return docx.AlignmentType.LEFT
  }
}

/**
 * 将 Tiptap 标题节点的 level 属性值映射到 docx.js 的 HeadingLevel。
 * @param {number | undefined} level - Tiptap 标题节点的 level 值。
 * @returns {HeadingLevel | undefined} docx.js 的标题级别。
 */
export function mapHeadingLevel(level) {
  switch (level) {
    case 1:
      return docx.HeadingLevel.HEADING_1
    case 2:
      return docx.HeadingLevel.HEADING_2
    case 3:
      return docx.HeadingLevel.HEADING_3
    case 4:
      return docx.HeadingLevel.HEADING_4
    case 5:
      return docx.HeadingLevel.HEADING_5
    case 6:
      return docx.HeadingLevel.HEADING_6
    default:
      return undefined
  }
}
