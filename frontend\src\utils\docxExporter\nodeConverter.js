import * as docx from 'docx'
import { mapTextAlign, mapHeadingLevel } from './mappingUtils.js'
import { processInlineContent } from './inlineContentProcessor.js'

/**
 * 节点转换器模块
 * 负责将 Tiptap 节点转换为 docx.js 文档元素
 */

/**
 * 将单个 Tiptap 节点转换为一个或多个 docx.js 文档元素 (Paragraph, Table等)。
 * @param {object} tiptapNode - 单个 Tiptap 节点。
 * @param {NumberingManager} numberingManager - 用于管理有序列表的编号定义。
 * @returns {Array<Paragraph | Table>} docx.js 元素的数组。
 */
export function convertTiptapNodeToDocxElements(tiptapNode, numberingManager) {
  const elements = []
  const textAlign = tiptapNode.attrs?.textAlign ? mapTextAlign(tiptapNode.attrs.textAlign) : undefined

  switch (tiptapNode.type) {
    case 'paragraph':
      elements.push(createParagraphElement(tiptapNode, numberingManager, textAlign))
      break

    case 'heading':
      elements.push(createHeadingElement(tiptapNode, numberingManager, textAlign))
      break

    case 'bulletList':
      elements.push(...createBulletListElements(tiptapNode, numberingManager))
      break

    case 'orderedList':
      elements.push(...createOrderedListElements(tiptapNode, numberingManager))
      break

    case 'table':
      const tableElement = createTableElement(tiptapNode, numberingManager)
      if (tableElement) {
        elements.push(tableElement)
      }
      break

    case 'horizontalRule':
      elements.push(createHorizontalRuleElement())
      break

    case 'pageBreak':
      elements.push(createPageBreakElement())
      break

    case 'tableOfContents':
      elements.push(...createTableOfContentsElements(tiptapNode))
      break

    case 'image':
      // 图片处理 - 现在所有图片都是内联的，不会出现在这里
      // 因为图片扩展配置为 inline: true，所以图片只会在 processInlineContent 中处理
      console.warn('[DOCX Export] 意外的块级图片节点，跳过处理')
      break

    default:
      // console.warn("Unsupported Tiptap block node type for DOCX export:", tiptapNode.type);
      break
  }

  return elements
}

/**
 * 创建段落元素
 * @param {object} tiptapNode - Tiptap段落节点
 * @param {NumberingManager} numberingManager - 编号管理器
 * @param {AlignmentType} textAlign - 文本对齐方式
 * @returns {Paragraph} docx.js段落元素
 */
function createParagraphElement(tiptapNode, numberingManager, textAlign) {
  const inlineContent = processInlineContent(tiptapNode.content, numberingManager)

  // 构建段落选项
  const paragraphOptions = {
    children: inlineContent,
    alignment: textAlign,
    spacing: { after: 100 }
  }

  // 处理首行缩进
  if (tiptapNode.attrs?.textIndent) {
    const textIndent = tiptapNode.attrs.textIndent
    if (textIndent === '2em') {
      // 2字符缩进，转换为twips（1字符约等于12pt，1pt = 20twips）
      paragraphOptions.indent = { firstLine: 480 } // 2 * 12 * 20 = 480 twips
      console.log('[DOCX Export] 应用首行缩进2字符')
    } else if (textIndent.endsWith('em')) {
      // 其他em单位缩进
      const emValue = parseFloat(textIndent.replace('em', ''))
      paragraphOptions.indent = { firstLine: Math.round(emValue * 12 * 20) }
      console.log('[DOCX Export] 应用首行缩进:', textIndent, '-> twips:', paragraphOptions.indent.firstLine)
    }
  }

  // 处理行间距
  if (tiptapNode.attrs?.lineHeight) {
    const lineHeight = tiptapNode.attrs.lineHeight
    const lineHeightValue = parseFloat(lineHeight)
    if (!isNaN(lineHeightValue)) {
      // 转换为docx.js的行间距格式（240 = 单倍行距）
      paragraphOptions.spacing.line = Math.round(lineHeightValue * 240)
      paragraphOptions.spacing.lineRule = docx.LineRuleType.AUTO
      console.log('[DOCX Export] 应用行间距:', lineHeight, '-> docx行距:', paragraphOptions.spacing.line)
    }
  }

  return new docx.Paragraph(paragraphOptions)
}

/**
 * 创建标题元素
 * @param {object} tiptapNode - Tiptap标题节点
 * @param {NumberingManager} numberingManager - 编号管理器
 * @param {AlignmentType} textAlign - 文本对齐方式
 * @returns {Paragraph} docx.js标题段落元素
 */
function createHeadingElement(tiptapNode, numberingManager, textAlign) {
  const headingInlineContent = processInlineContent(tiptapNode.content, numberingManager)

  // 构建标题选项
  const headingOptions = {
    heading: mapHeadingLevel(tiptapNode.attrs?.level),
    children: headingInlineContent,
    alignment: textAlign,
    spacing: { after: 100 }
  }

  // 处理首行缩进（标题也可以有缩进）
  if (tiptapNode.attrs?.textIndent) {
    const textIndent = tiptapNode.attrs.textIndent
    if (textIndent === '2em') {
      headingOptions.indent = { firstLine: 480 }
      console.log('[DOCX Export] 应用标题首行缩进2字符')
    } else if (textIndent.endsWith('em')) {
      const emValue = parseFloat(textIndent.replace('em', ''))
      headingOptions.indent = { firstLine: Math.round(emValue * 12 * 20) }
      console.log('[DOCX Export] 应用标题首行缩进:', textIndent, '-> twips:', headingOptions.indent.firstLine)
    }
  }

  // 处理行间距
  if (tiptapNode.attrs?.lineHeight) {
    const lineHeight = tiptapNode.attrs.lineHeight
    const lineHeightValue = parseFloat(lineHeight)
    if (!isNaN(lineHeightValue)) {
      headingOptions.spacing.line = Math.round(lineHeightValue * 240)
      headingOptions.spacing.lineRule = docx.LineRuleType.AUTO
      console.log('[DOCX Export] 应用标题行间距:', lineHeight, '-> docx行距:', headingOptions.spacing.line)
    }
  }

  return new docx.Paragraph(headingOptions)
}

/**
 * 创建无序列表元素
 * @param {object} tiptapNode - Tiptap无序列表节点
 * @param {NumberingManager} numberingManager - 编号管理器
 * @returns {Array<Paragraph>} docx.js段落元素数组
 */
function createBulletListElements(tiptapNode, numberingManager) {
  const elements = []

  if (tiptapNode.content) {
    tiptapNode.content.forEach(listItemNode => {
      // listItemNode is of type 'listItem'
      if (listItemNode.content) {
        // listItem 通常包含一个或多个段落
        listItemNode.content.forEach(itemContentNode => {
          // itemContentNode is e.g. 'paragraph'
          const listInlineContent = processInlineContent(itemContentNode.content, numberingManager)

          elements.push(
            new docx.Paragraph({
              children: listInlineContent,
              bullet: { level: 0 }, // TODO: 支持嵌套列表的 level
              alignment: itemContentNode.attrs?.textAlign ? mapTextAlign(itemContentNode.attrs.textAlign) : undefined,
              spacing: { after: 50 }
            })
          )
        })
      }
    })
  }

  return elements
}

/**
 * 创建有序列表元素
 * @param {object} tiptapNode - Tiptap有序列表节点
 * @param {NumberingManager} numberingManager - 编号管理器
 * @returns {Array<Paragraph>} docx.js段落元素数组
 */
function createOrderedListElements(tiptapNode, numberingManager) {
  const elements = []

  if (tiptapNode.content) {
    const listStyleRef = numberingManager.createNumberingDefinition()

    tiptapNode.content.forEach(listItemNode => {
      if (listItemNode.content) {
        listItemNode.content.forEach(itemContentNode => {
          const orderedListInlineContent = processInlineContent(itemContentNode.content, numberingManager)

          elements.push(
            new docx.Paragraph({
              children: orderedListInlineContent,
              numbering: { reference: listStyleRef, level: 0 }, // TODO: 支持嵌套列表的 level
              alignment: itemContentNode.attrs?.textAlign ? mapTextAlign(itemContentNode.attrs.textAlign) : undefined,
              spacing: { after: 50 }
            })
          )
        })
      }
    })
  }

  return elements
}

/**
 * 创建表格元素
 * @param {object} tiptapNode - Tiptap表格节点
 * @param {NumberingManager} numberingManager - 编号管理器
 * @returns {Table|null} docx.js表格元素或null
 */
function createTableElement(tiptapNode, numberingManager) {
  console.log('[DOCX Export] 处理表格节点:', JSON.stringify(tiptapNode, null, 2))

  const rows = []

  if (tiptapNode.content) {
    // content is array of 'tableRow'
    tiptapNode.content.forEach(rowNode => {
      const cells = []

      if (rowNode.content) {
        // content is array of 'tableCell' or 'tableHeader'
        rowNode.content.forEach(cellNode => {
          // 表格单元格的内容应该是块级元素，主要是段落
          const cellChildren = cellNode.content
            ? cellNode.content.flatMap(contentInCell => convertTiptapNodeToDocxElements(contentInCell, numberingManager))
            : [new docx.Paragraph({ text: '' })]

          // 如果转换结果为空（例如，空段落或不支持的类型被过滤），确保至少有一个空段落，否则 docx.js 会报错
          const finalCellChildren = cellChildren.length > 0 ? cellChildren : [new docx.Paragraph({ text: '' })]

          // 处理单元格合并属性
          const cellAttributes = cellNode.attrs || {}
          const cellOptions = {
            children: finalCellChildren,
            shading:
              cellNode.type === 'tableHeader'
                ? {
                    fill: 'auto',
                    val: docx.ShadingType.CLEAR,
                    color: 'D3D3D3'
                  }
                : undefined // LightGray
          }

          // 添加列合并支持 (Tiptap的colspan对应docx.js的columnSpan)
          if (cellAttributes.colspan && cellAttributes.colspan > 1) {
            cellOptions.columnSpan = cellAttributes.colspan
            console.log(`[DOCX Export] 应用列合并: colspan=${cellAttributes.colspan}`)
          }

          // 添加行合并支持 (Tiptap的rowspan对应docx.js的rowSpan)
          if (cellAttributes.rowspan && cellAttributes.rowspan > 1) {
            cellOptions.rowSpan = cellAttributes.rowspan
            console.log(`[DOCX Export] 应用行合并: rowspan=${cellAttributes.rowspan}`)
          }

          cells.push(new docx.TableCell(cellOptions))
        })
      }

      rows.push(new docx.TableRow({ children: cells }))
    })
  }

  if (rows.length > 0) {
    return new docx.Table({
      rows,
      width: { size: 100, type: docx.WidthType.PERCENTAGE }
    })
  }

  return null
}

/**
 * 创建水平分割线元素
 * @returns {Paragraph} docx.js段落元素
 */
function createHorizontalRuleElement() {
  return new docx.Paragraph({
    children: [new docx.ThematicBreak()]
  })
}

/**
 * 创建分页符元素
 * @returns {Paragraph} docx.js段落元素
 */
function createPageBreakElement() {
  return new docx.Paragraph({
    children: [new docx.PageBreak()],
    spacing: { after: 0 }
  })
}

/**
 * 创建目录元素
 * @param {object} tiptapNode - Tiptap目录节点
 * @returns {Array<Paragraph>} docx.js段落元素数组
 */
function createTableOfContentsElements(tiptapNode) {
  const elements = []

  // 目录：插入目录占位符，DOCX会自动生成
  const tocTitle = tiptapNode.attrs?.title || '目录'
  const tocLevels = tiptapNode.attrs?.levels || '1,2,3'

  // 目录标题
  elements.push(
    new docx.Paragraph({
      children: [new docx.TextRun({ text: tocTitle, bold: true, size: 28 })],
      alignment: docx.AlignmentType.CENTER,
      spacing: { after: 240 }
    })
  )

  // 插入目录 - 使用简化的实现
  elements.push(
    new docx.TableOfContents('目录', {
      hyperlink: true,
      headingStyleRange: '1-' + (tocLevels.split(',').length || 3),
      captionLabel: tocTitle
    })
  )

  return elements
}
