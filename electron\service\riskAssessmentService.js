const path = require('path')
const fs = require('fs')
const { sqlitedbService } = require('./database/sqlitedb')

/**
 * 风险评估服务
 * 负责对招标方案和招标文件进行风险评估
 */
class RiskAssessmentService {
  constructor(ctx) {
    this.ctx = ctx
    this.db = sqlitedbService.db
    this.biddingDocumentTemplateService = null
  }

  // 添加辅助错误处理方法
  errorResponse(message, statusCode = 400) {
    console.error(`Risk Assessment Service Error: ${message}`)
    return { success: false, message, statusCode }
  }

  dbErrorResponse() {
    return {
      success: false,
      message: '数据库操作失败，请检查数据库连接或稍后再试。',
      statusCode: 500
    }
  }

  /**
   * 启动风险评估
   * @param {string} documentType 文档类型：'tender_scheme' 或 'bidding_document'
   * @param {number} documentId 文档ID
   * @param {number} userId 用户ID
   * @returns {Promise<number>} 评估任务ID
   */
  async startAssessment(documentType, documentId, userId) {
    if (!this.db) return this.dbErrorResponse()

    try {
      // 检查文档是否存在且没有关联的评估结果
      const tableName = documentType === 'tender_scheme' ? 'tender_schemes' : 'bidding_documents'
      const document = this.db.prepare(`SELECT id, risk_assessment_id FROM ${tableName} WHERE id = ?`).get(documentId)

      if (!document) throw new Error('文档不存在')
      // if (document.risk_assessment_id) throw new Error('该文档已有风险评估结果，如需重新评估请先清除现有结果')

      // 创建评估任务
      const insertTask = this.db.prepare(`
        INSERT INTO risk_assessment (document_type, user_id, status, created_at, updated_at)
        VALUES (?, ?, 'pending', datetime('now','localtime'), datetime('now','localtime'))`)

      const result = insertTask.run(documentType, userId)
      const taskId = result.lastInsertRowid

      // 更新文档的 risk_assessment_id 字段
      const updateDocumentSql = `UPDATE ${tableName} SET risk_assessment_id = ?, updated_at = datetime('now','localtime') WHERE id = ?`
      this.db.prepare(updateDocumentSql).run(taskId, documentId)

      // 异步执行评估，传递文档类型和文档ID
      setImmediate(() => this.executeRiskAssessment(taskId, documentType, documentId))

      return taskId
    } catch (error) {
      console.error('启动风险评估失败:', error)
      throw error
    }
  }

  /**
   * 执行风险评估
   * @param {number} taskId 任务ID
   * @param {string} documentType 文档类型
   * @param {number} documentId 文档ID
   */
  async executeRiskAssessment(taskId, documentType, documentId) {
    try {
      // 更新任务状态为处理中
      this.db
        .prepare(
          `UPDATE risk_assessment 
           SET status = 'processing', started_at = datetime('now','localtime'), updated_at = datetime('now','localtime')
           WHERE id = ?`
        )
        .run(taskId)

      // 提取文档内容
      const extractionResult = await this.extractDocumentContent(documentType, documentId)

      if (!extractionResult.contextText || extractionResult.contextText.trim() === '') throw new Error('文档中没有找到需要评估的内容')

      // 调用AI进行风险评估
      const aiResult = await this.callAIForRiskAssessment(extractionResult)

      // 保存评估结果，将风险等级映射为对应的状态
      const riskLevel = aiResult.data.overall_risk_level
      const riskLevelMap = { 高: 'high', 中: 'medium', 低: 'low' }
      const finalStatus = riskLevelMap[riskLevel] || 'low' // 默认为低风险

      this.db
        .prepare(
          `UPDATE risk_assessment 
             SET status = ?, 
                 completed_at = datetime('now','localtime'),
                 updated_at = datetime('now','localtime'),
                 content_sent = ?,
                 ai_result = ?,
                 tokens_used = ?
             WHERE id = ?`
        )
        .run(finalStatus, extractionResult.contextText, JSON.stringify(aiResult.data), aiResult.tokens || 0, taskId)

      // 发送完成通知
      try {
        // 获取任务的用户ID
        const task = this.db.prepare(`SELECT user_id FROM risk_assessment WHERE id = ?`).get(taskId)

        const { webSocketService } = require('./webSocketService')
        webSocketService.sendRiskAssessmentCompleted({
          taskId,
          documentType: documentType,
          documentId: documentId,
          userId: task?.user_id,
          result: aiResult.data
        })
      } catch (error) {
        console.error('发送WebSocket评估完成通知失败:', error)
      }

      console.log(`风险评估完成，任务ID: ${taskId}`)
    } catch (error) {
      console.error(`风险评估执行失败，任务ID: ${taskId}`, error)

      // 更新任务状态为失败
      this.db
        .prepare(
          `UPDATE risk_assessment 
           SET status = 'failed', 
               completed_at = datetime('now','localtime'),
               updated_at = datetime('now','localtime'),
               error_message = ?
           WHERE id = ?`
        )
        .run(error.message, taskId)

      // 发送失败通知
      try {
        // 获取任务的用户ID
        const task = this.db.prepare(`SELECT user_id FROM risk_assessment WHERE id = ?`).get(taskId)

        const { webSocketService } = require('./webSocketService')
        webSocketService.sendRiskAssessmentFailed({
          taskId,
          documentType: documentType,
          documentId: documentId,
          userId: task?.user_id,
          error: error.message
        })
      } catch (error) {
        console.error('发送WebSocket评估失败通知失败:', error)
      }
    }
  }

  /**
   * 从文档中提取内容进行风险评估
   * @param {string} documentType 文档类型
   * @param {number} documentId 文档ID
   * @returns {Promise<Object>} 提取的内容和结构信息
   */
  async extractDocumentContent(documentType, documentId) {
    try {
      let document

      // 根据文档类型获取文档信息
      if (documentType === 'tender_scheme') {
        // 获取招标方案信息
        document = this.db
          .prepare(
            `SELECT ts.*, tst.content as template_content 
             FROM tender_schemes ts 
             LEFT JOIN tender_scheme_templates tst ON ts.template_id = tst.id 
             WHERE ts.id = ?`
          )
          .get(documentId)
      } else if (documentType === 'bidding_document') {
        // 获取招标文件信息
        document = this.db.prepare(`SELECT bd.* FROM bidding_documents bd WHERE bd.id = ?`).get(documentId)
      }

      if (!document) throw new Error('文档不存在')

      // 解析文档内容
      let documentContent = null

      if (documentType === 'tender_scheme') {
        // 招标方案：模板内容 + 字段值合并后的最终内容
        if (document.template_content && document.content) {
          try {
            const templateContent = JSON.parse(document.template_content)
            const fieldValues = JSON.parse(document.content)

            // 合并模板和字段值
            documentContent = this.mergeTemplateWithFieldValues(templateContent, fieldValues)
          } catch (e) {
            console.error('招标方案内容解析失败:', e)
          }
        }
      } else if (documentType === 'bidding_document') {
        // 招标文件：需要获取模板并合并字段值
        if (document.template_id && document.content) {
          try {
            // 懒加载实例化模板服务
            if (!this.biddingDocumentTemplateService) {
              const BiddingDocumentTemplateService = require('./biddingDocumentTemplateService')
              this.biddingDocumentTemplateService = new BiddingDocumentTemplateService()
            }

            const templateResult = await this.biddingDocumentTemplateService.getById(document.template_id)

            if (templateResult.success && templateResult.data && templateResult.data.content) {
              const templateContent = templateResult.data.content
              const fieldValues = JSON.parse(document.content)

              // 合并模板和字段值
              documentContent = this.mergeTemplateWithFieldValues(templateContent, fieldValues)
            } else {
              console.error('获取招标文件模板失败:', templateResult.message)
            }
          } catch (e) {
            console.error('调用招标文件模板服务失败:', e)
          }
        }
      }

      if (!documentContent) throw new Error('无法获取文档内容')

      // 提取所有文本内容，特别标识customField
      const extractionResult = this.extractTextContentWithCustomFields(documentContent)

      return extractionResult
    } catch (error) {
      console.error('提取文档内容失败:', error)
      throw error
    }
  }

  /**
   * 合并模板和字段值
   * @param {Object} templateContent 模板内容 (Tiptap JSON)
   * @param {Object} fieldValues 字段值对象 { field_key: [...content nodes] }
   * @returns {Object} 合并后的内容
   *
   * 注意：此方法与前端的 mergeTemplateWithFieldValues 保持一致
   */
  mergeTemplateWithFieldValues(templateContent, fieldValues) {
    // 深度克隆模板内容
    const mergedContent = JSON.parse(JSON.stringify(templateContent))

    // 递归遍历并填充字段值
    this.fillCustomFieldsRecursively(mergedContent, fieldValues)

    return mergedContent
  }

  /**
   * 递归填充自定义字段值
   * @param {Object} node 当前节点
   * @param {Object} fieldValues 字段值对象 { field_key: [...content nodes] }
   */
  fillCustomFieldsRecursively(node, fieldValues) {
    if (!node || typeof node !== 'object') return

    // 如果是自定义字段节点，填充内容
    if (node.type === 'customField' && node.attrs && node.attrs.field_key) {
      const fieldKey = node.attrs.field_key

      if (fieldValues.hasOwnProperty(fieldKey)) {
        const value = fieldValues[fieldKey]

        // 期望 value 是节点数组（包含样式信息）
        if (Array.isArray(value)) {
          node.content = value
        } else {
          // 如果不是数组，设置为空内容
          console.warn(`[fillCustomFieldsRecursively] 字段 ${fieldKey} 的值不是数组:`, value)
          node.content = []
        }
      } else {
        // 字段值不存在，设置为空内容
        node.content = []
      }
    }

    // 递归处理子节点
    if (node.content && Array.isArray(node.content)) {
      node.content.forEach(childNode => {
        this.fillCustomFieldsRecursively(childNode, fieldValues)
      })
    }
  }

  /**
   * 从节点数组中提取纯文本
   * @param {Array} nodes - Tiptap节点数组
   * @returns {string} - 纯文本内容
   */
  extractPlainTextFromNodes(nodes) {
    let text = ''

    nodes.forEach(node => {
      if (node.type === 'text') {
        text += node.text || ''
      } else if (node.content && Array.isArray(node.content)) {
        // 递归处理子节点
        text += this.extractPlainTextFromNodes(node.content)
      }
    })

    return text
  }

  /**
   * 提取文本内容并特别标识customField
   * @param {Object} documentContent Tiptap JSON文档内容
   * @returns {Object} 包含文本内容和位置信息的结果
   */
  extractTextContentWithCustomFields(documentContent) {
    const result = {
      textContent: [], // 用于发送给AI的文本内容数组
      customFields: [], // 自定义字段位置信息
      contextText: '' // 完整的上下文文本
    }
    // 递归提取内容
    this.extractTextRecursively(documentContent, result, [])
    // #region 将调试信息写入文件而不是控制台打印
    // console.log('开始写入')
    // const fs = require('fs')
    // const path = require('path')

    // // 构建日志目录和文件路径
    // const logsDir = path.join(__dirname, '../logs')
    // const debugFile = path.join(logsDir, `debug-output-${new Date().toLocaleString().replace(/[\/\s:]/g, '-')}.json`)

    // // 确保日志目录存在，如果不存在则创建
    // if (!fs.existsSync(logsDir)) {
    //   fs.mkdirSync(logsDir, { recursive: true })
    //   console.log(`已创建日志目录: ${logsDir}`)
    // }

    // // 写入调试信息到文件
    // fs.writeFileSync(debugFile, JSON.stringify({ documentContent, result }, null, 2))
    // console.log(`调试信息已写入文件: ${debugFile}`)

    // // 抛出错误，终止程序
    // throw new Error('测试')
    // #endregion
    // 组合成发送给AI的文本
    result.contextText = result.textContent.join('\n')

    return result
  }

  /**
   * 递归提取文本内容
   * @param {Object} node 当前节点
   * @param {Object} result 结果对象
   * @param {Array} path 当前路径（用于定位）
   */
  extractTextRecursively(node, result, path) {
    if (!node || typeof node !== 'object') return

    const currentPath = [...path]

    // 处理不同类型的节点
    if (node.type === 'text' && node.text) {
      // 普通文本节点
      result.textContent.push(node.text)
    } else if (node.type === 'customField' && node.attrs && node.attrs.field_key) {
      // 自定义字段节点 - 特殊处理
      const fieldKey = node.attrs.field_key
      const fieldName = node.attrs.field_name || fieldKey

      // 获取字段内容（支持多个节点和样式）
      let fieldText = ''
      if (node.content && Array.isArray(node.content)) {
        // 递归提取所有文本内容，包括嵌套的节点
        fieldText = this.extractPlainTextFromNodes(node.content)
      }

      if (fieldText && fieldText.trim() !== '') {
        // 在文本内容中特殊标识
        const customFieldMarker = `[CUSTOM_FIELD:${fieldKey}:${fieldName}]${fieldText}[/CUSTOM_FIELD:${fieldKey}]`
        result.textContent.push(customFieldMarker)

        // 记录自定义字段位置信息
        result.customFields.push({
          field_key: fieldKey,
          field_name: fieldName,
          text: fieldText,
          path: currentPath,
          textIndex: result.textContent.length - 1 // 在文本数组中的索引
        })
      }
    } else if (node.type === 'heading' && node.content) {
      // 标题节点 - 添加层级标识
      const level = node.attrs?.level || 1
      const headingText = node.content
        .filter(child => child.type === 'text' && child.text)
        .map(child => child.text)
        .join('')

      if (headingText.trim()) {
        result.textContent.push(`${'#'.repeat(level)} ${headingText}`)
      }
    } else if (node.type === 'paragraph' && node.content) {
      // 段落节点 - 处理其内容
      node.content.forEach((childNode, index) => {
        this.extractTextRecursively(childNode, result, [...currentPath, 'content', index])
      })

      // 段落结束添加换行
      if (node.content.some(child => child.type === 'text' || child.type === 'customField')) {
        result.textContent.push('') // 空行分隔段落
      }
    }

    // 递归处理其他有content的节点
    else if (node.content && Array.isArray(node.content) && node.type !== 'paragraph') {
      node.content.forEach((childNode, index) => {
        this.extractTextRecursively(childNode, result, [...currentPath, 'content', index])
      })
    }
  }

  /**
   * 调用AI进行风险评估
   * @param {Object} extractionResult 提取的内容和位置信息
   * @returns {Promise<Object>} AI评估结果
   */
  async callAIForRiskAssessment(extractionResult) {
    try {
      // 读取AI配置
      const configPath = path.join(__dirname, '../config/aiConfig.js')
      const aiConfig = require(configPath)

      if (!aiConfig.deepseek || !aiConfig.deepseek.apiKey) {
        throw new Error('DeepSeek API配置缺失')
      }

      const prompt = `你是一个专业的招标风险评估专家。请对以下招标文件内容进行风险评估。

文档内容中包含特殊标记的自定义字段，格式为：[CUSTOM_FIELD:字段key:字段名称]字段内容[/CUSTOM_FIELD:字段key]

请特别关注这些自定义字段的内容，结合上下文进行风险分析。

文档内容：
${extractionResult.contextText}

请从以下维度进行评估：
1. 合规性风险：是否符合招标法规要求
2. 技术风险：技术方案是否可行
3. 商务风险：商务条款是否合理
4. 执行风险：项目执行是否存在风险

**重要要求**：
1. 对于[CUSTOM_FIELD:xxx:xxx]标记的自定义字段，如果发现风险问题，请在field_risks数组中明确指出该字段的风险点。
2. 必须直接返回纯JSON格式，不要包含任何解释文字、markdown代码块、注释或其他格式化内容。
3. 返回内容只能是一个有效的JSON对象，以{开始，以}结束。

JSON格式要求：
{
  "overall_risk_level": "高|中|低",
  "overall_risk_score": 85,
  "risk_summary": "整体风险描述",
  "detailed_risks": [
    {
      "category": "合规性风险",
      "level": "高|中|低",
      "score": 90,
      "description": "具体风险描述"
    }
  ],
  "field_risks": [
    {
      "field_key": "project_name",
      "field_name": "项目名称",
      "risk_level": "高|中|低",
      "risk_score": 80,
      "reason": "该字段存在的具体风险原因",
      "suggestion": "改进建议"
    }
  ],
  "recommendations": ["建议1", "建议2"]
}`

      // 调用DeepSeek API
      const fetch = require('node-fetch')
      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${aiConfig.deepseek.apiKey}` },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            { role: 'system', content: '你是一个专业的招标风险评估专家。请严格按照要求的JSON格式返回结果，不要添加任何额外的说明或格式化标记。' },
            { role: 'user', content: prompt }
          ],
          temperature: 0.2, // 降低温度以获得更稳定的输出
          max_tokens: 2000
        })
      })

      if (!response.ok) throw new Error(`DeepSeek API调用失败: ${response.status} ${response.statusText}`)

      const result = await response.json()
      if (!result.choices || !result.choices[0] || !result.choices[0].message) throw new Error('DeepSeek API返回数据格式异常')

      const aiResponse = result.choices[0].message.content
      let parsedResult

      try {
        parsedResult = JSON.parse(aiResponse)
      } catch (e) {
        console.error('JSON解析失败:', e)
        console.error('AI响应内容:', aiResponse)

        // 如果无法解析JSON，创建一个默认结构
        parsedResult = {
          overall_risk_level: '中',
          overall_risk_score: 50,
          risk_summary: 'AI响应格式异常，无法正确解析风险评估结果',
          detailed_risks: [],
          field_risks: [],
          recommendations: ['请检查AI配置和提示词格式', '建议重新进行风险评估']
        }
      }
      return { data: parsedResult, tokens: result.usage ? result.usage.total_tokens : 0 }
    } catch (error) {
      console.error('AI风险评估调用失败:', error)
      throw error
    }
  }

  /**
   * 获取风险评估信息
   * @param {number} riskAssessmentId 风险评估ID
   * @returns {Object} 风险评估信息
   */
  getAssessmentInfo(riskAssessmentId) {
    return this.db.prepare(`SELECT * FROM risk_assessment WHERE id = ?`).get(riskAssessmentId)
  }

  /**
   * 获取用户的评估任务列表
   * @param {number} userId 用户ID
   * @param {Object} options 查询选项
   * @returns {Array} 任务列表
   */
  getUserTasks(userId, options = {}) {
    let sql = `SELECT ra.*
               FROM risk_assessment ra
               WHERE ra.user_id = ?`
    const params = [userId]

    if (options.status) {
      sql += ' AND ra.status = ?'
      params.push(options.status)
    }

    if (options.documentType) {
      sql += ' AND ra.document_type = ?'
      params.push(options.documentType)
    }

    sql += ' ORDER BY ra.created_at DESC'

    if (options.limit) {
      sql += ' LIMIT ?'
      params.push(options.limit)
    }

    return this.db.prepare(sql).all(...params)
  }

  /**
   * 取消评估任务
   * @param {number} taskId 任务ID
   * @param {number} userId 用户ID
   */
  cancelTask(taskId, userId) {
    const task = this.db.prepare(`SELECT * FROM risk_assessment WHERE id = ? AND user_id = ?`).get(taskId, userId)

    if (!task) {
      throw new Error('评估任务不存在或无权限')
    }

    if (!['pending', 'processing'].includes(task.status)) {
      throw new Error('只能取消待处理或处理中的任务')
    }

    this.db
      .prepare(
        `UPDATE risk_assessment 
         SET status = 'cancelled', 
             completed_at = datetime('now','localtime'),
             updated_at = datetime('now','localtime')
         WHERE id = ?`
      )
      .run(taskId)

    // 发送取消通知
    try {
      const { webSocketService } = require('./webSocketService')
      webSocketService.sendRiskAssessmentCancelled({
        taskId,
        userId
      })
    } catch (error) {
      console.error('发送WebSocket评估取消通知失败:', error)
    }
  }
}

module.exports = RiskAssessmentService
