/**
 * HTTP API 请求工具函数
 * 提供统一的HTTP请求方法
 */

// 获取API基础URL
export function getApiBaseUrl() {
  // 从localStorage获取IP配置，如果没有则使用默认值
  const IP = localStorage.getItem('IP') || 'localhost'
  return `http://${IP}:7071`
}

/**
 * 获取认证token
 * @returns {string|null} 认证token
 */
function getAuthToken() {
  return localStorage.getItem('token') || sessionStorage.getItem('token')
}

/**
 * 获取当前用户ID
 * @returns {number|null} 当前用户ID
 */
export function getCurrentUserId() {
  try {
    const userString = localStorage.getItem('user')
    if (userString) {
      const user = JSON.parse(userString)
      return user.id
    }
  } catch (error) {
    console.error('Error parsing user data:', error)
  }
  return null
}

/**
 * 通用HTTP请求工具函数
 * @param {string} endpoint - API端点路径
 * @param {string} method - HTTP方法 (GET, POST, PUT, DELETE等)
 * @param {Object|null} data - 请求数据
 * @param {Object} options - 额外的请求配置选项
 * @returns {Promise<Object>} API响应数据
 */
export async function httpRequest(endpoint, method = 'GET', data = null, options = {}) {
  // 构建完整的请求URL
  const url = `${getApiBaseUrl()}${endpoint}`

  // 默认请求配置
  const config = {
    method: method.toUpperCase(),
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  }

  // 添加认证token
  const token = getAuthToken()
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }

  // 添加用户ID到请求头
  const userId = getCurrentUserId()
  if (userId) {
    config.headers['X-User-ID'] = userId.toString()
  }

  // 处理请求数据
  if (data !== null && data !== undefined) {
    if (config.method === 'GET' || config.method === 'DELETE') {
      // GET和DELETE请求将数据作为查询参数
      if (typeof data === 'object') {
        const cleanParams = Object.fromEntries(Object.entries(data).filter(([_, v]) => v !== undefined && v !== '' && v !== null))
        const queryString = new URLSearchParams(cleanParams).toString()
        const separator = url.includes('?') ? '&' : '?'
        const finalUrl = queryString ? `${url}${separator}${queryString}` : url
        config.url = finalUrl
      } else {
        // 如果data是基本类型，通常作为ID参数
        const separator = url.includes('?') ? '&' : '?'
        config.url = `${url}${separator}id=${encodeURIComponent(data)}`
      }
    } else {
      // POST, PUT等请求将数据放在请求体中
      if (data instanceof FormData) {
        // 如果是FormData，移除Content-Type让浏览器自动设置
        delete config.headers['Content-Type']
        config.body = data
      } else {
        config.body = JSON.stringify(data)
      }
    }
  }

  console.log(`[HTTP Request] ${config.method} ${config.url || url}`, data)

  try {
    // 发送HTTP请求
    const response = await fetch(config.url || url, config)

    // 处理响应
    const contentType = response.headers.get('content-type')
    let responseData

    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json()
    } else {
      responseData = await response.text()
    }

    console.log(`[HTTP Response] ${config.method} ${config.url || url}`, responseData)

    // 检查HTTP状态码
    if (!response.ok) {
      const errorMessage = responseData?.message || `HTTP Error: ${response.status} ${response.statusText}`
      throw new Error(errorMessage)
    }

    // 检查业务状态码
    if (typeof responseData === 'object' && responseData.success === false) {
      throw new Error(responseData.message || 'API请求失败')
    }

    // 返回数据
    return responseData
  } catch (error) {
    console.error(`[HTTP Error] ${config.method} ${config.url || url}`, error)

    // 处理网络错误
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('网络连接失败，请检查网络连接或服务器状态')
    }

    // 处理认证错误
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      // 清除认证信息
      localStorage.removeItem('token')
      sessionStorage.removeItem('token')
      throw new Error('认证已过期，请重新登录')
    }

    throw error
  }
}

/**
 * GET请求的便捷方法
 * @param {string} endpoint - API端点路径
 * @param {Object} params - 查询参数
 * @param {Object} options - 额外选项
 * @returns {Promise<Object>} API响应数据
 */
export const get = (endpoint, params = null, options = {}) => {
  return httpRequest(endpoint, 'GET', params, options)
}

/**
 * POST请求的便捷方法
 * @param {string} endpoint - API端点路径
 * @param {Object} data - 请求数据
 * @param {Object} options - 额外选项
 * @returns {Promise<Object>} API响应数据
 */
export const post = (endpoint, data = null, options = {}) => {
  return httpRequest(endpoint, 'POST', data, options)
}

/**
 * PUT请求的便捷方法
 * @param {string} endpoint - API端点路径
 * @param {Object} data - 请求数据
 * @param {Object} options - 额外选项
 * @returns {Promise<Object>} API响应数据
 */
export const put = (endpoint, data = null, options = {}) => {
  return httpRequest(endpoint, 'PUT', data, options)
}

/**
 * DELETE请求的便捷方法
 * @param {string} endpoint - API端点路径
 * @param {Object} data - 请求数据（通常是ID）
 * @param {Object} options - 额外选项
 * @returns {Promise<Object>} API响应数据
 */
export const del = (endpoint, data = null, options = {}) => {
  return httpRequest(endpoint, 'DELETE', data, options)
}

/**
 * 文件上传的便捷方法
 * @param {string} endpoint - API端点路径
 * @param {FormData} formData - 包含文件的FormData对象
 * @param {Object} options - 额外选项
 * @returns {Promise<Object>} API响应数据
 */
export const upload = (endpoint, formData, options = {}) => {
  return httpRequest(endpoint, 'POST', formData, {
    ...options,
    // 让浏览器自动设置Content-Type
    headers: {
      ...options.headers
      // 不设置Content-Type，让浏览器自动处理multipart/form-data
    }
  })
}

/**
 * 设置API基础URL (用于配置不同环境的API地址)
 * @param {string} ip - 服务器IP地址
 * @param {number} port - 服务器端口号
 */
export const setApiBaseUrl = (ip, port = 7071) => {
  localStorage.setItem('IP', ip)
  // 可以选择性地保存端口
  if (port !== 7071) {
    localStorage.setItem('PORT', port.toString())
  }
}
