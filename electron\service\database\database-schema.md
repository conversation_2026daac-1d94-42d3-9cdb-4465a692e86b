# 数据库表结构设计文档

## 1. 用户管理相关表

### 1.1 用户表 (app_users)
```sql
CREATE TABLE app_users (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT NOT NULL UNIQUE,        -- 用户名
  password_hash TEXT NOT NULL,          -- 密码哈希
  nickname TEXT,                        -- 昵称
  email TEXT UNIQUE,                    -- 邮箱
  role_id INTEGER,                      -- 角色ID
  status TEXT DEFAULT 'active',         -- 状态：active/inactive
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (role_id) REFERENCES app_roles(id) ON DELETE SET NULL
);
```

### 1.2 角色表 (app_roles)
```sql
CREATE TABLE app_roles (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL UNIQUE,            -- 角色名称
  description TEXT,                     -- 角色描述
  permissions TEXT,                     -- 权限列表(JSON格式)
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime'))
);
```

## 2. 招标方案相关表

### 2.1 招标方案模板表 (tender_scheme_templates)
```sql
CREATE TABLE tender_scheme_templates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                   -- 模板名称
  type INTEGER NOT NULL,                -- 方案类型：1-服务类，2-工程类，3-物资类，4-加油站
  remark TEXT,                          -- 备注
  content TEXT,                         -- 模板内容(Tiptap JSON格式)
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime'))
);
```

### 2.2 招标方案表 (tender_schemes)
```sql
CREATE TABLE tender_schemes (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                   -- 方案名称
  template_id INTEGER,                  -- 关联的模板ID
  remark TEXT,                          -- 备注
  content TEXT,                         -- 方案内容(字段值对象的JSON字符串)
  version_number INTEGER DEFAULT 1,     -- 当前版本号，累加递增，不需要用户输入
  update_remark TEXT DEFAULT '',       -- 更新备注，用户提交更新时填写
  updated_by INTEGER,                   -- 更新人ID，提交更新的用户
  share_status TEXT DEFAULT 'private',  -- 分享状态：private/public/specific_users
  share_users TEXT,                     -- 指定分享用户ID列表(逗号分隔)
  created_by INTEGER,                   -- 创建人ID
  risk_assessment_id INTEGER REFERENCES risk_assessment(id) ON DELETE SET NULL, -- 关联的风险评估结果ID
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (template_id) REFERENCES tender_scheme_templates(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES app_users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES app_users(id) ON DELETE SET NULL
);
```

### 2.3 招标方案历史版本表 (tender_scheme_history)
```sql
CREATE TABLE tender_scheme_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  scheme_id INTEGER NOT NULL,           -- 关联的招标方案ID
  content TEXT NOT NULL,                -- 历史版本的内容(更新前的字段值对象JSON字符串)
  version_number INTEGER NOT NULL,      -- 版本号(更新前的版本号)
  update_remark TEXT DEFAULT '',       -- 更新备注(更新前的备注)
  updated_by INTEGER,                   -- 更新人ID(更新前的更新人)
  risk_assessment_id INTEGER,           -- 该版本关联的风险评估结果ID，保留历史版本的评估记录
  created_at DATETIME,                  -- 版本创建时间(实际是主表更新前的updated_at)
  FOREIGN KEY (scheme_id) REFERENCES tender_schemes(id) ON DELETE CASCADE,
  FOREIGN KEY (updated_by) REFERENCES app_users(id) ON DELETE SET NULL
);
-- 索引
CREATE INDEX idx_scheme_history_scheme_id ON tender_scheme_history(scheme_id);
CREATE INDEX idx_scheme_history_version ON tender_scheme_history(scheme_id, version_number);
```

## 3. 招标文件相关表

### 3.1 招标文件模板表 (bidding_document_templates)
```sql
CREATE TABLE bidding_document_templates (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                   -- 模板名称
  parent_id INTEGER,                    -- 父模板ID(用于层级关系)
  file_type TEXT,                       -- 文件类型或模板分类
  remark TEXT,                          -- 备注，如用途
  paragraph_refs TEXT,                  -- 段落引用JSON数组
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (parent_id) REFERENCES bidding_document_templates(id) ON DELETE CASCADE
);
```

### 3.2 招标文件模板段落内容表 (bidding_document_template_contents)
```sql
CREATE TABLE bidding_document_template_contents (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  content_data TEXT NOT NULL,           -- Tiptap JSON段落内容
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime'))
);
```

### 3.3 招标文件表 (bidding_documents)
```sql
CREATE TABLE bidding_documents (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name TEXT NOT NULL,                   -- 文件名称
  template_id INTEGER,                  -- 关联的招标文件模板ID
  scheme_id INTEGER,                    -- 关联的招标方案ID(可选)
  remark TEXT,                          -- 备注
  content TEXT,                         -- 文件具体内容(字段值对象的JSON字符串)
  version_number INTEGER DEFAULT 1,     -- 当前版本号，累加递增，不需要用户输入
  update_remark TEXT DEFAULT '',       -- 更新备注，用户提交更新时填写
  updated_by INTEGER,                   -- 更新人ID，提交更新的用户
  share_status TEXT DEFAULT 'private',  -- 分享状态：private/public/specific_users
  share_users TEXT,                     -- 指定分享用户ID列表(逗号分隔)
  created_by INTEGER,                   -- 创建人ID
  risk_assessment_id INTEGER REFERENCES risk_assessment(id) ON DELETE SET NULL,
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (template_id) REFERENCES bidding_document_templates(id) ON DELETE SET NULL,
  FOREIGN KEY (scheme_id) REFERENCES tender_schemes(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES app_users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES app_users(id) ON DELETE SET NULL
);
```

### 3.4 招标文件历史版本表 (bidding_document_history)
```sql
CREATE TABLE bidding_document_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  document_id INTEGER NOT NULL,         -- 关联的招标文件ID
  content TEXT NOT NULL,                -- 历史版本的内容(更新前的字段值对象JSON字符串)
  version_number INTEGER NOT NULL,      -- 版本号(更新前的版本号)
  update_remark TEXT DEFAULT '',       -- 更新备注(更新前的备注)
  updated_by INTEGER,                   -- 更新人ID(更新前的更新人)
  risk_assessment_id INTEGER,           -- 该版本关联的风险评估结果ID，保留历史版本的评估记录
  created_at DATETIME,                  -- 版本创建时间(实际是主表更新前的updated_at)
  FOREIGN KEY (document_id) REFERENCES bidding_documents(id) ON DELETE CASCADE,
  FOREIGN KEY (updated_by) REFERENCES app_users(id) ON DELETE SET NULL
);
-- 索引
CREATE INDEX idx_document_history_document_id ON bidding_document_history(document_id);
CREATE INDEX idx_document_history_version ON bidding_document_history(document_id, version_number);
```

## 4. 模板字段池表

### 4.1 模板字段池表 (template_fields)
```sql
CREATE TABLE template_fields (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  field_name TEXT NOT NULL,             -- 字段名称(显示名)
  field_key TEXT NOT NULL UNIQUE,       -- 字段标识符(唯一键)
  field_type TEXT DEFAULT 'custom',     -- 字段类型：default/custom
  remark TEXT,                          -- 备注
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime'))
);
```

## 5. 数据关系说明

### 5.1 用户与权限关系
- 用户表 → 角色表：多对一关系，一个用户对应一个角色
- 角色表存储权限信息(JSON格式)

### 5.2 招标方案关系
- 招标方案 → 招标方案模板：多对一关系
- 招标方案 → 招标方案历史版本：一对多关系
- 招标方案 → 用户(创建人)：多对一关系

### 5.3 招标文件关系
- 招标文件 → 招标文件模板：多对一关系
- 招标文件 → 招标方案：多对一关系(可选)
- 招标文件 → 招标文件历史版本：一对多关系
- 招标文件 → 用户(创建人)：多对一关系

### 5.4 模板层级关系
- 招标文件模板自引用：parent_id字段实现层级结构
- 段落内容与模板通过paragraph_refs字段建立多对多关系

## 6. 分享功能设计

### 6.1 分享状态
- `private`：私有，仅创建者可见
- `public`：公开，所有用户可见
- `specific_users`：指定用户，仅指定用户可见

### 6.2 指定用户格式
当 `share_status = 'specific_users'` 时，`share_users` 字段存储格式：
```
"1,2,3,5" // 用户ID的逗号分隔字符串
```

## 7. 版本管理设计

### 7.1 版本号规则
- 从1开始递增，系统自动管理
- 每次内容变更时版本号+1
- 主表的 `version_number` 字段记录当前版本号
- 用户不需要手动输入版本号，由系统自动累加

### 7.2 版本更新字段
- **version_number**：当前版本号，系统自动累加，默认为1
- **update_remark**：更新备注，用户提交更新时填写，记录本次更新的说明
- **updated_by**：更新人ID，记录提交更新的用户

### 7.3 历史版本存储机制
- **保存时机**：每次更新主表数据前，先将当前数据保存到历史版本表
- **保存内容**：历史版本表存储的是更新前的完整状态
  - `content`：更新前的字段值对象JSON字符串
  - `version_number`：更新前的版本号
  - `update_remark`：更新前的备注
  - `updated_by`：更新前的更新人
  - `created_at`：主表更新前的 `updated_at` 时间

### 7.4 版本管理流程
1. **创建时**：主表初始版本号为1，无历史版本
2. **更新时**：
   - 将当前主表数据复制到历史版本表
   - 主表版本号+1，更新内容、备注、更新人、更新时间
   - 历史版本表记录更新前的状态

### 7.5 版本对比和回滚
- 通过历史版本表可以查看任意版本的内容
- 支持版本间的内容对比功能
- 支持回滚到历史版本（将历史版本数据恢复到主表）

## 8. AI对话相关表

### 8.1 AI对话会话表 (ai_chat_sessions)
```sql
CREATE TABLE ai_chat_sessions (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  user_id INTEGER NOT NULL,             -- 用户ID
  title TEXT,                           -- 会话标题（自动生成或用户设置）
  status TEXT DEFAULT 'active',         -- 会话状态：active/deleted
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  updated_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (user_id) REFERENCES app_users(id) ON DELETE CASCADE
);
```

### 8.2 AI对话消息表 (ai_chat_messages)
```sql
CREATE TABLE ai_chat_messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id INTEGER NOT NULL,          -- 关联的会话ID
  role TEXT NOT NULL,                   -- 消息角色：user/assistant/system
  content TEXT NOT NULL,                -- 消息内容
  timestamp DATETIME DEFAULT (datetime('now','localtime')), -- 消息时间戳
  tokens_used INTEGER DEFAULT 0,       -- 本条消息使用的token数量
  model_name TEXT,                      -- 使用的AI模型名称
  created_at DATETIME DEFAULT (datetime('now','localtime')),
  FOREIGN KEY (session_id) REFERENCES ai_chat_sessions(id) ON DELETE CASCADE
);
```

### 8.3 AI对话功能说明

#### 8.3.1 会话管理
- **会话创建**：每个用户可以创建多个独立的对话会话
- **会话状态**：active(活跃)、deleted(已删除)
- **会话标题**：支持自动生成和手动修改
- **软删除**：删除会话时仅更改状态，不物理删除数据

#### 8.3.2 消息管理
- **消息角色**：
  - `user`：用户发送的消息
  - `assistant`：AI回复的消息
  - `system`：系统提示消息
- **上下文维护**：每个会话独立维护对话历史
- **Token统计**：记录每条消息的token使用量
- **模型记录**：记录使用的AI模型名称

#### 8.3.3 功能特性
- **多会话管理**：用户可以同时维护多个对话会话
- **历史记录**：完整保存对话历史，支持查看和检索
- **自动标题**：根据首条消息自动生成会话标题
- **权限控制**：用户只能访问自己的会话
- **数据清理**：支持清理过期的已删除会话数据

## 9. 风险评估模块

### 9.1 风险评估表 (risk_assessment)
用于存储AI风险评估任务和结果的核心表。

```sql
CREATE TABLE risk_assessment (
  id INTEGER PRIMARY KEY AUTOINCREMENT,                    -- 评估任务主键ID，唯一标识每次风险评估
  document_type TEXT NOT NULL,                             -- 文档类型：tender_scheme(招标方案)/bidding_document(招标文件)
  user_id INTEGER NOT NULL,                                -- 发起评估的用户ID，关联app_users表
  status TEXT DEFAULT 'pending',                           -- 评估状态：pending(待处理)/processing(处理中)/low(低风险)/medium(中风险)/high(高风险)/failed(失败)/cancelled(已取消)
  started_at DATETIME,                                     -- 开始处理评估的具体时间戳
  completed_at DATETIME,                                   -- 评估完成的具体时间戳，用于计算处理耗时
  content_sent TEXT,                                       -- 实际发送给AI模型的文档内容，保留原始输入用于问题排查
  ai_result TEXT,                                          -- AI返回的评估结果JSON字符串，包含风险等级、理由、字段索引等
  tokens_used INTEGER DEFAULT 0,                          -- 本次评估消耗的AI tokens数量，用于成本统计
  error_message TEXT,                                      -- 评估失败时的详细错误信息
  created_at DATETIME DEFAULT (datetime('now','localtime')), -- 评估任务创建时间
  updated_at DATETIME DEFAULT (datetime('now','localtime')), -- 评估任务最后更新时间
  FOREIGN KEY (user_id) REFERENCES app_users(id) ON DELETE CASCADE  -- 用户删除时级联删除相关评估记录
);

-- 索引优化
CREATE INDEX idx_risk_assessment_user_id ON risk_assessment(user_id);           -- 按用户查询评估历史
CREATE INDEX idx_risk_assessment_status ON risk_assessment(status);             -- 按状态查询待处理任务
CREATE INDEX idx_risk_assessment_document_type ON risk_assessment(document_type); -- 按文档类型统计
CREATE INDEX idx_risk_assessment_created_at ON risk_assessment(created_at);     -- 按时间排序查询
```

## 10. 索引优化

### 10.1 性能索引
- 历史版本表：按主表ID和版本号建立复合索引
- AI对话表：按用户ID、会话ID、状态等建立索引
- 风险评估表：按文档类型和ID、任务状态等建立索引
- 外键字段自动建立索引

### 10.2 AI对话相关索引
```sql
-- AI会话表索引
CREATE INDEX idx_ai_chat_sessions_user_id ON ai_chat_sessions(user_id);
CREATE INDEX idx_ai_chat_sessions_status ON ai_chat_sessions(status);

-- AI消息表索引
CREATE INDEX idx_ai_chat_messages_session_id ON ai_chat_messages(session_id);
CREATE INDEX idx_ai_chat_messages_timestamp ON ai_chat_messages(timestamp);
CREATE INDEX idx_ai_chat_messages_role ON ai_chat_messages(role);
```

### 10.3 风险评估相关索引
```sql
-- 风险评估表索引
CREATE INDEX idx_risk_assessment_user_id ON risk_assessment(user_id);
CREATE INDEX idx_risk_assessment_status ON risk_assessment(status);
CREATE INDEX idx_risk_assessment_document_type ON risk_assessment(document_type);
CREATE INDEX idx_risk_assessment_created_at ON risk_assessment(created_at);
```

### 10.4 查询优化
- 历史版本查询：通过索引快速定位特定版本
- 分享权限查询：通过状态和用户ID快速筛选
- AI对话查询：通过用户ID和会话ID快速检索对话数据
- 风险评估查询：通过文档类型和ID快速检索评估结果 