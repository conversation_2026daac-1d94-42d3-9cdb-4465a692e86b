## 《招标文件助手》项目纲要

### 1. 模块分解

- 招标方案

  1.  模板管理
      - 普通的模板列表，不需要分级，创建模板时需要选择方案类型
      - 字段：模板名称、方案类型、备注
  2.  方案制作
      - 创建与填写流程：
        - 用户通过选择模板，生成具体招标方案
        - 模板内容以“填空题”形式呈现，用户填写对应字段
        - 历史版本：每次修改自动生成新版本，可进行版本对比（content 字段内容有变化，在历史记录表中新增一条数据，可与当前最新版本对比）
      - 分享：
        - 分享方式：私有（默认）、公开、指定用户
        - 创建人可 `编辑、分享、删除`，其他被分享用户可 `编辑`
      - 导出为 docx 文档：
        - 招标文件需严格符合模板定义的 Word 格式
        - 使用 docx.js 完成样式一致的 Word 文档导出
      - 字段：方案名称、模板（模板列表中选择）、备注

- 招标文件

  1.  模板管理
      - 模板分为多级（一级、二级、三级...，最多有五级，前端列表通过树形表格展示）根据角色判断可编辑的级别
      - 下级模板内容基于上级模板
      - 上级模板段落内容变更时，会级联更新下级模板引用的相同段落
      - 字段：模板名称、文件类型、备注（用途）
  2.  文件生成
      - 字段：文件名称、选择模板、选择方案（选填）、备注
      - 自动字段填充（创建招标文件时 选择方案）：
        - 系统维护字段池（如招标单位、投标人、项目编号等）
        - 招标文件模板与招标方案中的填写的字段实现自动同步填充
      - 其余内容与 "招标方案 -> 方案制作" 一样
      - 方案参考面板功能
        在选择方案时，自动加载方案关联的模板内容用于参考，展示在左侧，可折叠
        也可选择本地文件（.docx），选择本地文件，并通过localhost记录当前招标文件打开的本地文件路径

- 系统管理

  1.  用户管理
  2.  角色管理
  3.  菜单管理
      - 用户分配对应角色（可分配多角色）
      - 角色附带权限：菜单、模板编辑（从前端判断）
  4.  字典管理
      - 方案类型 - 1 服务类、2 工程类、3 物资类、4 加油站
      - 模板字段池 支持设置字段（如：项目名称、投标单位）为可复用关键词
      - 模板中通过编辑器选择或插入字段占位符，便于自动填充

- AI Chat

  1. 基础 AI 对话
     - 以可折叠侧边栏形式在右侧展示
     - 聊天历史分页/懒加载支持。避免一次性加载所有历史消息，实现按需滚动加载，上滑加载旧消息

  | 功能模块     | 描述                                                   |
  | ------------ | ------------------------------------------------------ |
  | 对话窗口展示 | 前端右侧折叠侧边栏，展示 AI 对话内容                   |
  | 消息交互     | 支持用户输入提问，AI 回复展示                          |
  | 上下文管理   | 每轮对话携带历史上下文（由后端维护）                   |
  | 对话记录存储 | 后端存储每位用户的对话历史（DeepSeek 接口 + 自建记录） |

  2. 多会话管理，侧边栏右上角图标：新对话、显示聊天记录
     - 新对话，开启新聊天
     - 显示聊天记录，点击后以对话框形式展示当前用户的会话列表

  | 功能     | 描述                                            |
  | -------- | ----------------------------------------------- |
  | 新建对话 | 用户点击“新聊天”按钮，生成新的会话（sessionId） |
  | 会话切换 | 用户可查看并切换以往的会话记录                  |
  | 删除会话 | 可删除某个会话及其所有记录                      |
  | 命名会话 | 支持自动/手动命名会话（如按首条提问内容截取）   |

注意事项：
用户与权限管理

- **本地账号系统**：
  - 系统不依赖外部服务器，所有数据本地存储
  - 支持创建多个账号，账号信息与权限保存在本地数据库中
- **权限级别划分**： - 超级管理员：管理所有用户及权限 - 普通用户：根据权限编辑对应级别的模板或方案 - 权限控制粒度包括：可查看、可编辑、不可访问
  局域网协作与共享
- **局域网发现与连接**：
  - 应用支持发现同一局域网下的其他设备
  - 支持设备间文件共享、同步、权限校验（如只读、编辑）
- **共享控制**：
  - 模板全部共享，但编辑的权限、菜单 需要根据角色控制，招标文件模板的权限要精确到等级（例如：角色 A 只能编辑一级模板，角色 B 只能编辑二级模板）
    - 招标方案、文件 可主动分享给局域网设备，手动设置共享权限
    - 共享可随时取消，对方会立即失去访问权限
- **招标方案、招标文件 对应关系**
  1. 系统管理 -> 模板字段池 模块(类似于字典)
  2. 在 tiptap 编辑器组件内，添加插入字段工具，分默认字段，指定字段（都有下划线），在新建方案时，编辑方案内容，只能对字段内容进行编辑，其他内容不能修改（后面生成招标文件时，选择招标文件模板、招标方案，对应的字段值需要自动填充到招标文件中）。需要对 tiptap 编辑器内容的保存格式进行调整，现在是富文本格式，因为考虑到要导出成 word，所以要保存为 json 格式
  3. 关于方案制作，它选定了模板，模板变更后，对应的方案内容也要跟着变。因为方案是填写的是模板中对应的字段，所以方案内容只存储字段就行，渲染时通过字段值补充模板的字段。模板制作时，用户可以自定义字段（不需要在字段池中保存），但要保证 field_key 在本模板中唯一.
  4. 1. 招标方案数据存储：
     - 后端 tenderSchemeService.js 在创建 (create) 和更新 (update) 方案时，其 content 字段存储一个仅包含 { "field_key": "用户填写的值", ... } 这样的对象（序列化为字符串后）。
     - 版本历史 (version_history) 也将相应地只存储这些字段值的快照。
     1. 前端渲染逻辑变更 (SchemeForm.vue)：
     - 加载方案时：
       - 获取方案所关联的模板的完整 Tiptap JSON 内容。
       - 获取方案自身存储的字段值对象。
       - 关键：实现一个辅助函数，该函数接收模板 JSON 和字段值对象，然后遍历模板 JSON，找到所有 customField 节点，并用字段值对象中的对应值更新这些节点的内部文本内容。
       - 将这个“合并”后的 Tiptap JSON 传递给编辑器进行渲染。
       - 只允许对字段区域进行编辑(不允许光标出现在非字段区域)
     - 保存方案时：
       - 从编辑器获取当前完整的 Tiptap JSON 内容。
       - 关键：实现另一个辅助函数，该函数遍历编辑器内容，提取所有 customField 节点的 field_key 及其当前的用户填写的值，构建成一个字段值对象。
       - 将这个字段值对象发送给后端保存。
  5. 招标文件模板存储：
     - Tiptap 编辑器获取 JSON 数据类型为：{type: 'doc', content: Array(14)}，将 content 数据项单独存储，每条模板数据存储对应的 id，在后端拼接处理后返回给前端。
     - 模板仅记录当前的 content 的 id 序列数组，例如，二级模板会记录一级模板的 id，content 项记录在一级模板中的上下文，上一段落的 id/下一段落的 id，这些在后端进行拼接处理。在前端编辑时，二级模板不能对一级模板进行改动，只能在一级模板基础上增加段落。
     - 保存招标文件数据时同招标方案一样

### 2. ✅ 技术要点

#### 2.1 框架选型

| 部分         | 技术栈                                                         |
| ------------ | -------------------------------------------------------------- |
| 桌面框架     | **Electron + electron-egg**（Node.js + Vue3 + Koa + Egg 模型） |
| 前端构建     | Vue3 + Vite + + ant-design-vue                                 |
| 前端状态管理 | vuex 列表页通过 keep-alive 进行路由缓存                        |
| 后端通信     | http                                                           |
| 数据存储     | SQLite                                                         |
| 编辑器       | Tiptap（可自定义 NodeView，实现字段插槽/填空）                 |
| Word 导出    | docx.js                                                        |

---

#### 2.2 核心开发机制

| 模块         | 技术设计要点                                                                 | 备注                                             |
| ------------ | ---------------------------------------------------------------------------- | ------------------------------------------------ |
| 模板内容继承 | 段落内容实体独立存储，模板内容存储段落 ID、所属模板 id，仅所属模板可编辑段落 | 解决模板文件管理问题，实现数字化管理             |
| 填空题实现   | Tiptap 插件扩展 NodeView，实现自定义字段                                     | 解决文件制作时，修改了不能修改的内容双方核对问题 |
| 本地用户权限 | 用户表 + 角色表 + 权限字段控制，所有数据存在本地 SQLite                      | 解决用户权限管理问题，实现数字化管理             |

# 大模型风险评估实现

**目标**：

> 用户上传文档后，系统使用大模型分析 _填空内容_ 的潜在风险，按“高/中/低”级别分类，并在前端展示中标出这些文本片段并显示提示信息，，支持后台异步任务、WebSocket 通知前端。

1. 用户手动触发风险评估（方案制作/文件管理页面），确认提示框
2. 后端提取招标方案/文件（Tiptap JSON 结构）处理：
   模板与字段合并后，提取纯文本内容，自定义字段通过 customField 标记出，风险评估仅对“填空内容”部分进行分析
3. 将提取后的文本打包发送给大模型，支持异步执行（可能耗时），风险评估在后端单独进行，不阻塞主流程
4. 评估任务完成后，将结果持久化保存到 风险评估表。 关联关系：风险评估跟随招标方案/文件的版本，即版本关联风险评估 id.
5. 使用 websocket 向前端推送“评估完成”事件及风险简要摘要。
6. 前端展示高亮风险字段。
   根据 `risk` 的不同等级，添加：背景高亮，气泡提示（点击自定义字段查看评估详情），风险列表聚合显示（右侧单独列出）
7. 提示：每个自定义字段在页面上可能重复出现多个，评估时请标记字段索引（目前未实现）。
