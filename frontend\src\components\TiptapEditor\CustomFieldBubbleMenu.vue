<template>
  <BubbleMenu v-if="editor" :editor="editor" :tippy-options="tippyOptions" :should-show="shouldShow">
    <!-- 气泡菜单工具栏 -->
    <div class="custom-field-bubble-menu">
      <!-- 字号调整 -->
      <div class="menu-group">
        <a-button size="small" @click="decreaseFontSize" :disabled="!canDecreaseFontSize">
          <template #icon><MinusOutlined /></template>
        </a-button>
        <span class="font-size-display">{{ currentFontSizeLabel }}</span>
        <a-button size="small" @click="increaseFontSize" :disabled="!canIncreaseFontSize">
          <template #icon><PlusOutlined /></template>
        </a-button>
      </div>

      <a-divider type="vertical" />

      <!-- 文本格式 -->
      <div class="menu-group">
        <a-tooltip title="粗体">
          <a-button size="small" :type="editor.isActive('bold') ? 'primary' : 'default'" @click="editor.chain().focus().toggleBold().run()">
            <template #icon><BoldOutlined /></template>
          </a-button>
        </a-tooltip>

        <a-tooltip title="斜体">
          <a-button size="small" :type="editor.isActive('italic') ? 'primary' : 'default'" @click="editor.chain().focus().toggleItalic().run()">
            <template #icon><ItalicOutlined /></template>
          </a-button>
        </a-tooltip>

        <a-tooltip title="下划线">
          <a-button size="small" :type="editor.isActive('underline') ? 'primary' : 'default'" @click="editor.chain().focus().toggleUnderline().run()">
            <template #icon><UnderlineOutlined /></template>
          </a-button>
        </a-tooltip>

        <a-tooltip title="删除线">
          <a-button size="small" :type="editor.isActive('strike') ? 'primary' : 'default'" @click="editor.chain().focus().toggleStrike().run()">
            <template #icon><StrikethroughOutlined /></template>
          </a-button>
        </a-tooltip>
      </div>

      <a-divider type="vertical" />

      <!-- 对齐方式 -->
      <div class="menu-group">
        <a-tooltip title="左对齐">
          <a-button size="small" :type="currentTextAlign === 'left' ? 'primary' : 'default'" @click="setTextAlign('left')">
            <template #icon><AlignLeftOutlined /></template>
          </a-button>
        </a-tooltip>

        <a-tooltip title="居中对齐">
          <a-button size="small" :type="currentTextAlign === 'center' ? 'primary' : 'default'" @click="setTextAlign('center')">
            <template #icon><AlignCenterOutlined /></template>
          </a-button>
        </a-tooltip>

        <a-tooltip title="右对齐">
          <a-button size="small" :type="currentTextAlign === 'right' ? 'primary' : 'default'" @click="setTextAlign('right')">
            <template #icon><AlignRightOutlined /></template>
          </a-button>
        </a-tooltip>
      </div>

      <a-divider type="vertical" />

      <!-- 颜色选择 -->
      <div class="menu-group">
        <!-- 文字颜色 -->
        <a-tooltip title="文字颜色">
          <a-dropdown>
            <template #overlay>
              <div class="color-picker-panel">
                <div class="color-grid">
                  <div v-for="color in colorOptions" :key="color" class="color-item" :style="{ backgroundColor: color }" @click="setTextColor(color)"></div>
                </div>
                <a-divider style="margin: 8px 0" />
                <a-button size="small" @click="setTextColor(null)" block>默认颜色</a-button>
              </div>
            </template>
            <a-button size="small">
              <template #icon><FontColorsOutlined /></template>
              <span class="color-indicator" :style="{ backgroundColor: currentColor || '#000' }"></span>
            </a-button>
          </a-dropdown>
        </a-tooltip>

        <!-- 高亮颜色 -->
        <a-tooltip title="高亮">
          <a-dropdown>
            <template #overlay>
              <div class="color-picker-panel">
                <div class="color-grid">
                  <div v-for="color in highlightColorOptions" :key="color" class="color-item" :style="{ backgroundColor: color }" @click="setHighlightColor(color)"></div>
                </div>
                <a-divider style="margin: 8px 0" />
                <a-button size="small" @click="setHighlightColor(null)" block>清除高亮</a-button>
              </div>
            </template>
            <a-button size="small">
              <template #icon><BgColorsOutlined /></template>
              <span class="color-indicator" :style="{ backgroundColor: currentHighlightColor || 'transparent' }"></span>
            </a-button>
          </a-dropdown>
        </a-tooltip>
      </div>
    </div>
  </BubbleMenu>
</template>

<script setup>
import { ref, computed, inject } from 'vue'
import { BubbleMenu } from '@tiptap/vue-3'
import {
  BoldOutlined,
  ItalicOutlined,
  UnderlineOutlined,
  StrikethroughOutlined,
  AlignLeftOutlined,
  AlignCenterOutlined,
  AlignRightOutlined,
  FontColorsOutlined,
  BgColorsOutlined,
  MinusOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

const props = defineProps({
  editor: {
    type: Object,
    required: true
  }
})

// 注入填充模式状态
const fillMode = inject('fillMode', false)
const isFillMode = computed(() => fillMode.value)

// 字号选项（从小到大）
const fontSizeOptions = [
  { label: '小六', value: '6.5pt', order: 1 },
  { label: '六号', value: '7.5pt', order: 2 },
  { label: '小五', value: '9pt', order: 3 },
  { label: '五号', value: '10.5pt', order: 4 }, // 默认
  { label: '小四', value: '12pt', order: 5 },
  { label: '四号', value: '14pt', order: 6 },
  { label: '小三', value: '15pt', order: 7 },
  { label: '三号', value: '16pt', order: 8 },
  { label: '小二', value: '18pt', order: 9 },
  { label: '二号', value: '22pt', order: 10 },
  { label: '小一', value: '24pt', order: 11 },
  { label: '一号', value: '26pt', order: 12 },
  { label: '小初', value: '36pt', order: 13 },
  { label: '初号', value: '42pt', order: 14 }
]

// 颜色选项
const colorOptions = ref([
  '#000000',
  '#333333',
  '#666666',
  '#999999',
  '#CCCCCC',
  '#FF0000',
  '#FF6600',
  '#FFCC00',
  '#FFFF00',
  '#00FF00',
  '#00FFCC',
  '#00CCFF',
  '#0099FF',
  '#0066FF',
  '#0000FF',
  '#6600FF',
  '#9900FF',
  '#CC00FF',
  '#FF0099',
  '#FF3366'
])

// 高亮颜色选项
const highlightColorOptions = ref([
  '#FFFF00', // 黄色（经典高亮）
  '#FFE600',
  '#FFCC00',
  '#FFB300',
  '#FF9900', // 橙色系
  '#CCFF00',
  '#99FF00',
  '#66FF00', // 绿色系
  '#00FFCC',
  '#00CCFF',
  '#0099FF', // 蓝色系
  '#CC99FF',
  '#FF99CC',
  '#FF6699', // 紫粉色系
  '#FFD1DC'
])

// Tippy 配置
const tippyOptions = {
  duration: 100,
  placement: 'top', // 居中显示在选中内容上方
  hideOnClick: false,
  appendTo: () => document.body,
  offset: [0, 10] // 与选中内容保持一定距离
}

// 判断是否应该显示气泡菜单
const shouldShow = ({ editor, from, to }) => {
  // 检查是否有选中内容（不是空选区）
  if (from === to) return false

  // 填充模式下的特殊处理
  if (isFillMode.value) {
    // 检查选中的内容是否在自定义字段内
    const { $from } = editor.state.selection

    // 向上查找是否在 customField 节点内
    let depth = $from.depth
    while (depth > 0) {
      const node = $from.node(depth)
      if (node && node.type.name === 'customField') {
        return true
      }
      depth--
    }

    // 填充模式下，只在自定义字段内显示
    return false
  }

  // 普通编辑模式：有选中内容就显示
  return true
}

// 获取当前字号
const currentFontSize = computed(() => {
  const fontSize = props.editor.getAttributes('textStyle').fontSize
  return fontSize || '10.5pt' // 默认五号
})

// 获取当前字号标签
const currentFontSizeLabel = computed(() => {
  const sizeOption = fontSizeOptions.find(opt => opt.value === currentFontSize.value)
  return sizeOption ? sizeOption.label : '五号'
})

// 获取当前字号索引
const currentFontSizeIndex = computed(() => {
  const index = fontSizeOptions.findIndex(opt => opt.value === currentFontSize.value)
  return index !== -1 ? index : 3 // 默认五号的索引
})

// 是否可以减小字号
const canDecreaseFontSize = computed(() => currentFontSizeIndex.value > 0)

// 是否可以增大字号
const canIncreaseFontSize = computed(() => currentFontSizeIndex.value < fontSizeOptions.length - 1)

// 减小字号
const decreaseFontSize = () => {
  if (canDecreaseFontSize.value) {
    const newSize = fontSizeOptions[currentFontSizeIndex.value - 1].value
    props.editor.chain().focus().setFontSize(newSize).run()
  }
}

// 增大字号
const increaseFontSize = () => {
  if (canIncreaseFontSize.value) {
    const newSize = fontSizeOptions[currentFontSizeIndex.value + 1].value
    props.editor.chain().focus().setFontSize(newSize).run()
  }
}

// 获取当前对齐方式
const currentTextAlign = computed(() => {
  // 自定义字段是内联元素，对齐方式应该应用在其父段落上
  const { $from } = props.editor.state.selection
  const paragraph = $from.parent

  if (paragraph && paragraph.attrs && paragraph.attrs.textAlign) {
    return paragraph.attrs.textAlign
  }
  return 'left'
})

// 设置对齐方式
const setTextAlign = align => {
  // 对于内联元素的对齐，需要设置其父段落的对齐方式
  props.editor.chain().focus().setTextAlign(align).run()
}

// 获取当前文字颜色
const currentColor = computed(() => {
  return props.editor.getAttributes('textStyle').color || '#000000'
})

// 设置文字颜色
const setTextColor = color => {
  if (color) {
    props.editor.chain().focus().setColor(color).run()
  } else {
    props.editor.chain().focus().unsetColor().run()
  }
}

// 获取当前高亮颜色
const currentHighlightColor = computed(() => {
  // highlight 扩展使用 data-color 属性存储颜色
  const attrs = props.editor.getAttributes('highlight')
  return attrs.color || 'transparent'
})

// 设置高亮颜色
const setHighlightColor = color => {
  if (color) {
    // 使用 highlight 扩展的 toggleHighlight 命令
    props.editor.chain().focus().toggleHighlight({ color }).run()
  } else {
    // 移除高亮
    props.editor.chain().focus().unsetHighlight().run()
  }
}
</script>

<style lang="less" scoped>
.custom-field-bubble-menu {
  width: max-content;
  display: flex;
  align-items: center;
  padding: 6px 8px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  gap: 4px;

  .menu-group {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .font-size-display {
    min-width: 40px;
    text-align: center;
    font-size: 12px;
    color: #595959;
    user-select: none;
  }

  :deep(.ant-btn) {
    min-width: 28px;
    height: 28px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  :deep(.ant-divider-vertical) {
    height: 20px;
    margin: 0 4px;
  }

  .color-indicator {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    margin-left: 4px;
    vertical-align: middle;
  }
}

// 颜色选择面板
.color-picker-panel {
  padding: 12px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-width: 220px;

  .color-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 6px;
    margin-bottom: 8px;
  }

  .color-item {
    width: 32px;
    height: 32px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
