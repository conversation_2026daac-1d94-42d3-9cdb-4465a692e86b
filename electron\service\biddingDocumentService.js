'use strict'

// const BaseService = require('./baseService'); // 移除
const { sqlitedbService } = require('./database/sqlitedb')

/**
 * 招标文件服务
 * 实现招标文件的创建、查询、更新、删除，支持模板和方案的自动填充
 * @class
 */
class BiddingDocumentService {
  constructor(ctx) {
    // super(); // 移除
    this.ctx = ctx
    this.db = sqlitedbService.db
    this.tableName = sqlitedbService.biddingDocumentsTableName
    this.historyTableName = sqlitedbService.biddingDocumentHistoryTableName
    this.templateTableName = sqlitedbService.biddingDocumentTemplatesTableName
    this.schemeTableName = sqlitedbService.tenderSchemesTableName
    this.contentsTableName = sqlitedbService.templateContentsTableName
  }

  // 添加辅助错误处理方法
  errorResponse(message, statusCode = 400) {
    console.error(`Service Error: ${message}`)
    return { success: false, message, statusCode } // statusCode for HTTP, may not be directly used by IPC client
  }

  dbErrorResponse() {
    return {
      success: false,
      message: '数据库操作失败，请检查数据库连接或稍后再试。',
      statusCode: 500
    }
  }

  /**
   * 创建招标文件
   * @param {object} data - { name, template_id?, scheme_id?, remark?, content?, created_by }
   * @returns {Promise<object>} { success, data?, message?, statusCode? }
   */
  async create(data) {
    if (!this.db) return this.dbErrorResponse()

    try {
      console.log('[BiddingDocumentService.create] Data:', data)
      const { name, template_id, scheme_id, remark, content, created_by } = data

      if (!name) return this.errorResponse('文件名称不能为空')
      if (!template_id) return this.errorResponse('模板不能为空')
      if (!content) return this.errorResponse('文件内容不能为空')
      if (!created_by) return this.errorResponse('创建人不能为空')

      // 插入招标文件记录，只存储字段值
      const insertStmt = this.db.prepare(`
        INSERT INTO ${this.tableName} 
        (name, template_id, scheme_id, remark, content, version_number, update_remark, 
         updated_by, share_status, share_users, created_by, created_at, updated_at) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', 'localtime'), datetime('now', 'localtime'))
      `)

      const result = insertStmt.run(
        name,
        template_id,
        scheme_id || null,
        remark || null,
        JSON.stringify(content),
        1, // version_number
        '初始版本', // update_remark
        created_by, // updated_by
        'private', // share_status
        null, // share_users
        created_by // created_by
      )

      if (result.lastInsertRowid) {
        const newDocumentResponse = await this.getById(result.lastInsertRowid)
        if (newDocumentResponse.success) {
          return { success: true, data: newDocumentResponse.data, message: '招标文件创建成功' }
        }
        return { success: false, message: newDocumentResponse.message || '创建招标文件后获取详情失败', statusCode: newDocumentResponse.statusCode || 500 }
      }
      return { success: false, message: '创建招标文件失败，未获取到ID', statusCode: 500 }
    } catch (error) {
      console.error('Error creating bidding document:', error)
      return { success: false, message: `创建招标文件失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 获取招标文件列表（根据权限过滤）
   * @param {object} params - 查询参数 { page, pageSize, name, template_id, scheme_id, current_user_id }
   * @returns {object} { list, pagination }
   */
  async list(params = {}) {
    const { page = 1, pageSize = 10, name, template_id, scheme_id, current_user_id } = params
    const offset = (page - 1) * pageSize
    let whereClauses = []
    let queryParams = {}

    // 基础条件过滤
    if (name) {
      whereClauses.push('bd.name LIKE @name_like')
      queryParams.name_like = `%${name}%`
    }
    if (template_id) {
      whereClauses.push('bd.template_id = @template_id')
      queryParams.template_id = template_id
    }
    if (scheme_id) {
      whereClauses.push('bd.scheme_id = @scheme_id')
      queryParams.scheme_id = scheme_id
    }

    // 权限过滤：创建人、公开、指定分享人
    if (current_user_id) {
      whereClauses.push(`(
        bd.created_by = @current_user_id OR 
        bd.share_status = 'public' OR 
        (bd.share_status = 'specific_users' AND (
          bd.share_users LIKE @user_id_start OR 
          bd.share_users LIKE @user_id_middle OR 
          bd.share_users LIKE @user_id_end OR 
          bd.share_users = @user_id_exact
        ))
      )`)
      queryParams.current_user_id = current_user_id
      queryParams.user_id_start = `${current_user_id},%`
      queryParams.user_id_middle = `%,${current_user_id},%`
      queryParams.user_id_end = `%,${current_user_id}`
      queryParams.user_id_exact = current_user_id.toString()
    }

    const whereString = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : ''

    // 连表查询相关信息
    const dataSql = `
      SELECT bd.id, bd.name, bd.template_id, bdt.name as template_name, 
             bd.scheme_id, ts.name as scheme_name, bd.remark,
             bd.version_number, bd.update_remark, bd.share_status, bd.share_users,
             bd.created_by, u1.nickname as created_by_name,
             bd.updated_by, u2.nickname as updated_by_name,
             bd.created_at, bd.updated_at,
             bd.risk_assessment_id,
             CASE 
               WHEN bd.risk_assessment_id IS NULL THEN 'none'
               WHEN ra.status IN ('pending', 'processing') THEN 'processing'
               ELSE ra.status
             END as display_risk_status
      FROM ${this.tableName} bd
      LEFT JOIN ${this.templateTableName} bdt ON bd.template_id = bdt.id
      LEFT JOIN ${this.schemeTableName} ts ON bd.scheme_id = ts.id
      LEFT JOIN ${sqlitedbService.appUsersTableName} u1 ON bd.created_by = u1.id
      LEFT JOIN ${sqlitedbService.appUsersTableName} u2 ON bd.updated_by = u2.id
      LEFT JOIN risk_assessment ra ON bd.risk_assessment_id = ra.id
      ${whereString}
      ORDER BY bd.updated_at DESC
      LIMIT @pageSize OFFSET @offset
    `

    const countSql = `
      SELECT COUNT(bd.id) as total 
      FROM ${this.tableName} bd 
      ${whereString}
    `

    try {
      const finalQueryParamsData = { ...queryParams, pageSize, offset }
      const list = this.db.prepare(dataSql).all(finalQueryParamsData)
      const row = this.db.prepare(countSql).get(queryParams)

      const total = row ? row.total : 0

      return {
        success: true,
        data: {
          list: list,
          pagination: {
            total,
            page: Number(page),
            pageSize: Number(pageSize),
            totalPages: Math.ceil(total / pageSize)
          }
        }
      }
    } catch (error) {
      console.error('Error listing bidding documents:', error)
      return { success: false, message: `获取招标文件列表失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 根据ID获取招标文件详情
   * @param {number} id - 文件ID
   * @returns {object} 文件对象
   */
  async getById(id) {
    const sql = `
      SELECT bd.*, bdt.name as template_name, ts.name as scheme_name,
             u1.nickname as created_by_name,
             u2.nickname as updated_by_name,
             CASE 
               WHEN bd.risk_assessment_id IS NULL THEN 'none'
               WHEN ra.status IN ('pending', 'processing') THEN 'processing'
               ELSE ra.status
             END as display_risk_status
      FROM ${this.tableName} bd
      LEFT JOIN ${this.templateTableName} bdt ON bd.template_id = bdt.id
      LEFT JOIN ${this.schemeTableName} ts ON bd.scheme_id = ts.id
      LEFT JOIN ${sqlitedbService.appUsersTableName} u1 ON bd.created_by = u1.id
      LEFT JOIN ${sqlitedbService.appUsersTableName} u2 ON bd.updated_by = u2.id
      LEFT JOIN risk_assessment ra ON bd.risk_assessment_id = ra.id
      WHERE bd.id = ?
    `

    try {
      const document = this.db.prepare(sql).get(id)
      if (document) {
        const processedDocument = document

        // 解析content字段
        if (processedDocument.content && typeof processedDocument.content === 'string') {
          try {
            processedDocument.content = JSON.parse(processedDocument.content)
            if (typeof processedDocument.content !== 'object' || processedDocument.content === null) {
              console.warn(`Document ID ${id}: Parsed content is not an object, resetting to empty object.`)
              processedDocument.content = {}
            }
          } catch (e) {
            console.error(`Error parsing content for document ID ${id}:`, e)
            processedDocument.content = {}
          }
        } else {
          processedDocument.content = processedDocument.content || {}
        }

        return { success: true, data: processedDocument }
      } else {
        return { success: false, message: `ID为 ${id} 的招标文件不存在`, statusCode: 404 }
      }
    } catch (error) {
      console.error('Error getting bidding document by ID:', error)
      return { success: false, message: `获取招标文件详情失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 更新招标文件
   * @param {number} id - 文件ID
   * @param {object} data - 更新数据 { name?, remark?, content?, update_remark?, updated_by?, share_status?, share_users? }
   * @returns {object} 更新后的文件对象
   */
  async update(id, data) {
    const { name,scheme_id, remark, content: newFieldValues, update_remark, updated_by, share_status, share_users } = data

    if (!updated_by) {
      return { success: false, message: '更新人不能为空', statusCode: 400 }
    }

    // 获取现有数据
    const existingDocumentResponse = await this.getById(id)
    if (!existingDocumentResponse.success) {
      return existingDocumentResponse
    }
    const existingDocument = existingDocumentResponse.data

    let fieldsToUpdate = {}
    let paramsForRun = { id }

    // 检查content是否有变化
    let contentChanged = false
    if (newFieldValues !== undefined) {
      const currentContent = JSON.stringify(existingDocument.content || {})
      const newContent = JSON.stringify(newFieldValues || {})

      if (currentContent !== newContent) {
        contentChanged = true

        // 在更新前，将当前数据保存到历史版本表
        await this.saveToHistory(id, existingDocument)

        // 更新内容和版本号
        fieldsToUpdate.content = 'content = @content'
        fieldsToUpdate.version_number = 'version_number = @version_number'
        paramsForRun.content = JSON.stringify(newFieldValues)
        paramsForRun.version_number = (existingDocument.version_number || 1) + 1

        // 版本更新时重置风险评估ID
        fieldsToUpdate.risk_assessment_id = 'risk_assessment_id = NULL'
      }
    }

    // 其他字段更新
    if (name !== undefined) {
      fieldsToUpdate.name = 'name = @name'
      paramsForRun.name = name
    }
    if (scheme_id !== undefined) {
      fieldsToUpdate.scheme_id = 'scheme_id = @scheme_id'
      paramsForRun.scheme_id = scheme_id
    }
    if (remark !== undefined) {
      fieldsToUpdate.remark = 'remark = @remark'
      paramsForRun.remark = remark
    }
    if (update_remark !== undefined) {
      fieldsToUpdate.update_remark = 'update_remark = @update_remark'
      paramsForRun.update_remark = update_remark
    }
    if (share_status !== undefined) {
      fieldsToUpdate.share_status = 'share_status = @share_status'
      paramsForRun.share_status = share_status
    }
    if (share_users !== undefined) {
      fieldsToUpdate.share_users = 'share_users = @share_users'
      paramsForRun.share_users = share_users
    }

    // 更新updated_by和updated_at
    fieldsToUpdate.updated_by = 'updated_by = @updated_by'
    fieldsToUpdate.updated_at = "updated_at = datetime('now', 'localtime')"
    paramsForRun.updated_by = updated_by

    // 如果没有任何变化
    if (Object.keys(fieldsToUpdate).length <= 2) {
      // 只有updated_by和updated_at
      return { success: true, data: existingDocument, message: '数据无变化' }
    }

    const setClauses = Object.values(fieldsToUpdate).join(', ')
    const updateSql = `UPDATE ${this.tableName} SET ${setClauses} WHERE id = @id`

    try {
      const stmt = this.db.prepare(updateSql)
      const result = stmt.run(paramsForRun)

      if (result.changes > 0) {
        const updatedDocumentResponse = await this.getById(id)
        if (updatedDocumentResponse.success) {
          return { success: true, data: updatedDocumentResponse.data, message: '招标文件更新成功' }
        }
        return { success: false, message: updatedDocumentResponse.message || '更新招标文件后获取详情失败', statusCode: updatedDocumentResponse.statusCode || 500 }
      } else {
        return { success: true, data: existingDocument, message: '数据无实质变化' }
      }
    } catch (error) {
      console.error(`Error updating bidding document ID ${id}:`, error)
      return { success: false, message: `更新招标文件失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 保存当前数据到历史版本表
   * @param {number} documentId - 文件ID
   * @param {object} currentData - 当前数据
   */
  async saveToHistory(documentId, currentData) {
    const insertHistorySql = `
      INSERT INTO ${this.historyTableName} 
      (document_id, content, version_number, update_remark, updated_by, created_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `

    try {
      const stmt = this.db.prepare(insertHistorySql)
      stmt.run(
        documentId,
        JSON.stringify(currentData.content || {}),
        currentData.version_number || 1,
        currentData.update_remark || '',
        currentData.updated_by,
        currentData.updated_at
      )
    } catch (error) {
      console.error('Error saving to history:', error)
      throw error
    }
  }

  /**
   * 删除招标文件（同时删除历史记录）
   * @param {number} id - 文件ID
   * @returns {object} 操作结果
   */
  async delete(id) {
    try {
      // 使用事务确保数据一致性
      const deleteTransaction = this.db.transaction(() => {
        // 先删除历史记录
        const deleteHistoryStmt = this.db.prepare(`DELETE FROM ${this.historyTableName} WHERE document_id = ?`)
        deleteHistoryStmt.run(id)

        // 删除主记录
        const deleteMainStmt = this.db.prepare(`DELETE FROM ${this.tableName} WHERE id = ?`)
        const result = deleteMainStmt.run(id)

        return result
      })

      const result = deleteTransaction()

      if (result.changes > 0) {
        return { success: true, message: '招标文件删除成功' }
      } else {
        return { success: false, message: '未删除任何招标文件 (可能ID不存在或已被删除)', statusCode: 404 }
      }
    } catch (error) {
      console.error(`Error deleting bidding document ID ${id}:`, error)
      return { success: false, message: `删除招标文件失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 获取历史记录列表
   * @param {number} documentId - 文件ID
   * @returns {object} 历史记录列表
   */
  async getHistory(documentId) {
    const sql = `
      SELECT h.*, u.nickname as updated_by_name
      FROM ${this.historyTableName} h
      LEFT JOIN ${sqlitedbService.appUsersTableName} u ON h.updated_by = u.id
      WHERE h.document_id = ?
      ORDER BY h.version_number DESC
    `

    try {
      const historyList = this.db.prepare(sql).all(documentId)

      // 解析content字段
      const processedList = historyList.map(item => {
        const processedItem = item
        if (processedItem.content && typeof processedItem.content === 'string') {
          try {
            processedItem.content = JSON.parse(processedItem.content)
          } catch (e) {
            console.error(`Error parsing history content for document ID ${documentId}:`, e)
            processedItem.content = {}
          }
        }
        return processedItem
      })

      return { success: true, data: processedList }
    } catch (error) {
      console.error(`Error getting history for document ID ${documentId}:`, error)
      return { success: false, message: `获取历史记录失败: ${error.message}`, statusCode: 500 }
    }
  }
}

module.exports = BiddingDocumentService
