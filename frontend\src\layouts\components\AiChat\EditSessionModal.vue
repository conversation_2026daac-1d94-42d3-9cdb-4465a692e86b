<template>
  <a-modal
    :open="visible"
    title="编辑会话标题"
    @ok="handleSave"
    @cancel="handleCancel"
  >
    <a-input
      v-model:value="title"
      placeholder="请输入会话标题"
      @pressEnter="handleSave"
    />
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  session: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits([
  'update:visible',
  'save'
])

// 响应式数据
const title = ref('')

// 监听会话变化，更新标题
watch(() => props.session, (newSession) => {
  if (newSession) {
    title.value = newSession.title || ''
  }
}, { immediate: true })

// 方法
const handleSave = () => {
  if (!title.value.trim()) return
  
  emit('save', {
    session: props.session,
    title: title.value.trim()
  })
}

const handleCancel = () => {
  // 重置标题
  title.value = props.session?.title || ''
  emit('update:visible', false)
}
</script> 