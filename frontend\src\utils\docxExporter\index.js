import * as docx from 'docx'
import { NumberingManager } from './NumberingManager.js'
import { convertTiptapNodeToDocxElements } from './nodeConverter.js'
import { processDocumentElements } from './asyncProcessor.js'

/**
 * DOCX 导出器主模块
 * 提供将 Tiptap 编辑器内容导出为 DOCX 文件的功能
 *
 * 模块结构：
 * - NumberingManager: 管理编号列表定义
 * - mappingUtils: 提供属性映射工具
 * - imageProcessor: 处理图片加载和转换
 * - inlineContentProcessor: 处理内联内容转换
 * - nodeConverter: 将 Tiptap 节点转换为 docx.js 元素
 * - asyncProcessor: 处理异步图片节点
 */

/**
 * 将 Tiptap 编辑器的 JSON 内容导出为 DOCX 文件。
 *
 * 主要流程：
 * 1. 验证输入内容
 * 2. 创建编号管理器
 * 3. 转换 Tiptap 节点为 docx.js 元素
 * 4. 处理异步图片节点
 * 5. 创建 DOCX 文档
 * 6. 生成并下载文件
 *
 * @param {object} tiptapJson - Tiptap 编辑器输出的 JSON 对象。
 * @param {string} [fileName="document.docx"] - 下载的 DOCX 文件名。
 */
export async function exportTiptapContentToDocx(tiptapJson, fileName = 'document.docx') {
  try {
    console.log('[DOCX Export] 开始导出流程...')

    // 验证 Tiptap JSON 内容
    if (!tiptapJson || !tiptapJson.content) {
      throw new Error('Invalid Tiptap JSON provided for DOCX export.')
    }

    // 创建编号管理器
    const numberingManager = new NumberingManager()

    // 转换 Tiptap 节点为 docx.js 元素
    console.log('[DOCX Export] 转换 Tiptap 节点...')
    const rawDocxElements = tiptapJson.content.flatMap(node => convertTiptapNodeToDocxElements(node, numberingManager))

    // 处理异步图片节点
    console.log('[DOCX Export] 处理异步图片节点...')
    const docxDocumentChildren = await processDocumentElements(rawDocxElements)

    // 创建文档默认样式配置
    const documentStyles = {
      default: {
        document: {
          run: {
            font: '宋体', // 使用宋体
            size: 21 // 五号字 = 10.5pt = 21半点
          },
          paragraph: {
            spacing: { after: 120 } // 6pt * 20 = 120 twentieths of a point
          }
        }
      }
    }

    // 创建文档页面配置
    const pageProperties = {
      page: {
        margin: {
          top: docx.convertInchesToTwip(1),
          right: docx.convertInchesToTwip(1),
          bottom: docx.convertInchesToTwip(1),
          left: docx.convertInchesToTwip(1)
        }
      }
    }

    // 创建文档元数据
    const documentMetadata = {
      creator: '招标方案助手', // 创建者
      title: fileName.replace('.docx', ''), // 标题
      description: '由招标方案助手生成的文档', // 描述
      subject: '招标文档', // 主题
      lastModifiedBy: '招标方案助手', // 最后修改者
      revision: 1, // 修订版本
      features: { updateFields: true } // 文档说：必须启用 updateFields 功能才能正确更新目录 https://docx.js.org/#/usage/table-of-contents
    }

    // 创建 DOCX 文档
    console.log('[DOCX Export] 创建 DOCX 文档...')
    const doc = new docx.Document({
      // 文档元数据
      ...documentMetadata,

      // 编号列表配置
      numberings: numberingManager.getConfigurations(),

      // 文档章节
      sections: [
        {
          properties: pageProperties,
          children: docxDocumentChildren
        }
      ],

      // 文档样式
      styles: documentStyles
    })

    // 生成并下载文件
    console.log('[DOCX Export] 开始生成DOCX文件...')
    const blob = await docx.Packer.toBlob(doc)

    // 验证生成的 DOCX blob
    if (!blob || blob.size === 0) {
      throw new Error('生成的DOCX文件为空')
    }

    console.log('[DOCX Export] DOCX文件生成成功，大小:', blob.size, 'bytes')

    // 下载 DOCX 文件
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    document.body.appendChild(a) // Required for Firefox
    a.href = url
    a.download = fileName.endsWith('.docx') ? fileName : `${fileName}.docx`
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
    console.log('[DOCX Export] 文件导出成功:', fileName)
  } catch (error) {
    console.error('Error generating or downloading DOCX file:', error)

    // 生成并显示错误消息
    let errorMessage = '导出 DOCX 文件失败'

    if (error.message.includes('图片')) {
      errorMessage += '：图片处理错误 - ' + error.message
    } else if (error.message.includes('Invalid')) {
      errorMessage += '：文档格式错误 - ' + error.message
    } else {
      errorMessage += '：' + error.message
    }

    alert(errorMessage)
  }
}
