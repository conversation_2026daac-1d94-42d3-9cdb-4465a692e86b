<template>
  <div class="version-history">
    <!-- 版本历史抽屉 -->
    <a-drawer title="版本历史" placement="right" :closable="false" width="500" v-model:visible="drawerVisible">
      <a-timeline>
        <a-timeline-item v-for="version in historyVersions" :key="version.id">
          <article>
            <div>
              <span>
                <b>{{ version.created_at }}</b>
              </span>
            </div>
            <div style="display: flex; align-items: center; justify-content: space-between">
              <div>
                <div>版本号: {{ version.version_number }}</div>
                <div>备注: {{ version.update_remark || '无' }}</div>
                <div>更新人: {{ version.updated_by_name }}</div>
              </div>
              <a-button type="primary" style="margin-left: 10px" @click="handleVersionComparison(version)">对比</a-button>
            </div>
          </article>
        </a-timeline-item>
      </a-timeline>
    </a-drawer>

    <!-- 版本对比模态框 -->
    <a-modal
      v-model:visible="comparisonModalVisible"
      :title="`版本对比 - 当前版本 vs 版本${selectedVersion?.version_number || ''}`"
      width="100%"
      wrap-class-name="full-modal"
      :footer="null"
      destroyOnClose
    >
      <div class="flex gap-[16px] max-h-[calc(100vh-75px)] overflow-y-auto">
        <!-- 当前版本 -->
        <a-card title="当前版本" style="flex: 1">
          <div class="version-content">
            <TiptapEditor v-if="currentVersionContent" :model-value="currentVersionContent" :editable="false" :show-toolbar="false" :min-height="'400px'" />
          </div>
        </a-card>

        <!-- 历史版本 -->
        <a-card :title="`版本 ${selectedVersion?.version_number || ''}`" style="flex: 1">
          <div class="version-content">
            <TiptapEditor v-if="selectedVersionContent" :model-value="selectedVersionContent" :editable="false" :show-toolbar="false" :min-height="'400px'" />
          </div>
        </a-card>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import TiptapEditor from '@/components/TiptapEditor/index.vue'
import { mergeTemplateWithFieldValues } from '@/utils/templateUtils'

// Props
const props = defineProps({
  // 控制抽屉显示
  visible: {
    type: Boolean,
    default: false
  },
  // 历史版本列表
  historyVersions: {
    type: Array,
    default: () => []
  },
  // 模板内容 (Tiptap JSON)
  templateContent: {
    type: [Object, null],
    required: true
  },
  // 当前版本的字段值
  currentFieldValues: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible'])

// 响应式数据
const drawerVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
})

const comparisonModalVisible = ref(false)
const selectedVersion = ref(null)

// 计算当前版本内容
const currentVersionContent = computed(() => {
  if (!props.templateContent || !props.currentFieldValues) return null
  return mergeTemplateWithFieldValues(props.templateContent, props.currentFieldValues)
})

// 计算选中版本内容
const selectedVersionContent = computed(() => {
  if (!selectedVersion.value || !props.templateContent) return null

  // 解析历史版本的content字段
  let historyFieldValues = {}
  try {
    historyFieldValues = typeof selectedVersion.value.content === 'string' ? JSON.parse(selectedVersion.value.content) : selectedVersion.value.content || {}
  } catch (error) {
    console.error('解析历史版本内容失败:', error)
    historyFieldValues = {}
  }

  return mergeTemplateWithFieldValues(props.templateContent, historyFieldValues)
})

// 处理版本对比
const handleVersionComparison = version => {
  selectedVersion.value = version
  comparisonModalVisible.value = true
}
</script>

<style scoped>
.version-history .version-content {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 16px;
}

.version-history .ant-card {
  border-radius: 8px;
}

.version-history .ant-timeline-item-content {
  padding-left: 16px;
}
</style>
<style lang="less">
.full-modal {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }
  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }
  .ant-modal-body {
    flex: 1;
  }
}
</style>
