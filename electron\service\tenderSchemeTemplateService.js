'use strict'

// const Service = require('ee-core').Service; // 假设基础 Service 类
const { sqlitedbService } = require('./database/sqlitedb') // 引入我们的数据库服务

/**
 * 招标方案模板服务
 * @class
 */
class TenderSchemeTemplateService {
  // 移除 extends Service
  constructor() {
    // 移除 ctx 参数
    // super(ctx); // 移除
    // this.ctx = ctx; // 移除
    this.db = sqlitedbService.db // 使用 sqlitedbService 中的 db 实例
    this.tableName = sqlitedbService.tenderSchemeTemplatesTableName // 使用正确的表名

    // 调试代码：打印表结构
    if (this.db) {
      try {
        const tableInfo = this.db.prepare(`PRAGMA table_info(${this.tableName})`).all()
        console.log(`[DEBUG] Schema for table '${this.tableName}':`, JSON.stringify(tableInfo, null, 2))
      } catch (error) {
        console.error(`[DEBUG] Error fetching schema for table '${this.tableName}':`, error)
      }
    }
  }

  // 添加辅助错误处理方法
  errorResponse(message, statusCode = 400) {
    console.error(`Service Error: ${message}`)
    return { success: false, message, statusCode }
  }

  dbErrorResponse() {
    return {
      success: false,
      message: '数据库操作失败，请检查数据库连接或稍后再试。',
      statusCode: 500
    }
  }

  /**
   * 创建招标方案模板
   * @param {object} data - 模板数据 { name, type, remark, content }
   * @returns {object} { success: boolean, data?: { id }, message?: string }
   */
  create(data) {
    if (!this.db) return this.dbErrorResponse()

    let { name, type, remark = '', content } = data // content 单独处理

    if (!name || type === undefined) {
      return this.errorResponse('模板名称和方案类型不能为空')
    }

    const numericType = Number(type)
    if (isNaN(numericType) || !Number.isInteger(numericType)) {
      return this.errorResponse('方案类型必须是有效的整数值')
    }

    // 处理 content: 如果是对象则序列化，否则按原样（可能是null、空字符串等）
    let contentToStore = content
    if (contentToStore && typeof contentToStore === 'object') {
      try {
        contentToStore = JSON.stringify(contentToStore)
      } catch (e) {
        console.error('Failed to stringify content object during create:', e)
        return this.errorResponse('内容对象序列化失败', 400)
      }
    } else if (contentToStore === undefined || contentToStore === null) {
      contentToStore = null // 明确存储为 NULL 如果是 undefined 或 null
    } else if (typeof contentToStore !== 'string') {
      // 对于其他非字符串、非对象类型 (例如数字、布尔)，也需要决定如何处理
      // 这里假设如果不是对象、字符串、null，则转为字符串或报错
      console.warn('Content for create is not an object, string, or null. Converting to string or defaulting to null.', contentToStore)
      contentToStore = String(contentToStore) // 或者根据业务设为null
    }

    const insertSql = `
      INSERT INTO ${this.tableName} (name, type, remark, content, created_at, updated_at)
      VALUES (@name, @type, @remark, @content, datetime('now', 'localtime'), datetime('now', 'localtime'))
    `
    try {
      const stmt = this.db.prepare(insertSql)
      const info = stmt.run({
        name: name,
        type: numericType,
        remark: remark,
        content: contentToStore
      })

      if (info.lastInsertRowid) {
        // 创建成功后，通过 getById 获取完整记录，确保 content 被正确反序列化
        const createdRecord = this.getById(info.lastInsertRowid)
        if (createdRecord.success) {
          return { success: true, data: createdRecord.data, message: '招标方案模板创建成功' }
        }
        // 如果 getById 失败，返回一个基本的成功信息但无data
        return { success: true, data: { id: info.lastInsertRowid }, message: '招标方案模板创建成功 (获取详情失败)' }
      }
      return this.errorResponse('创建招标方案模板失败，未获取到ID')
    } catch (error) {
      console.error('Error creating tender scheme template:', error)
      // 特别检查是否是 SQLite 的绑定错误
      if ((error.message && error.message.toLowerCase().includes('constraint')) || error.message.toLowerCase().includes('bind')) {
        return this.errorResponse(`创建招标方案模板失败: 数据库约束或绑定错误。可能的原因：无效的数据类型或值。详细信息: ${error.message}`)
      }
      return this.errorResponse(`创建招标方案模板失败: ${error.message}`)
    }
  }

  /**
   * 获取所有招标方案模板 (可分页)
   * @param {object} params - 查询参数 { page, pageSize, type, name, sortBy, sortOrder }
   * @returns {object} { success: boolean, data?: { list, pagination }, message?: string }
   */
  list(params = {}) {
    // 移除 async
    if (!this.db) return this.dbErrorResponse()

    const { page = 1, pageSize = 10, type, name, sortBy = 'updated_at', sortOrder = 'DESC' } = params
    const offset = (page - 1) * pageSize

    let whereClauses = []
    let queryBindingParams = {} // 用于绑定到 SQL 查询的参数

    if (type !== undefined && type !== null && type !== '') {
      // 确保 type 有效才加入查询
      const numericType = Number(type)
      if (!isNaN(numericType) && Number.isInteger(numericType)) {
        whereClauses.push('type = @type')
        queryBindingParams.type = numericType
      } else {
        // 如果类型无效，可以选择忽略或返回错误，这里选择忽略此筛选条件
        console.warn(`[Service.list] Invalid type provided for filtering: ${type}. Ignoring type filter.`)
      }
    }
    if (name) {
      whereClauses.push('name LIKE @name')
      queryBindingParams.name = `%${name}%`
    }

    const whereString = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : ''

    const validSortOrders = ['ASC', 'DESC']
    const safeSortOrder = validSortOrders.includes(String(sortOrder).toUpperCase()) ? String(sortOrder).toUpperCase() : 'DESC'
    const allowedSortBy = ['id', 'name', 'type', 'created_at', 'updated_at']
    const safeSortBy = allowedSortBy.includes(sortBy) ? sortBy : 'updated_at'

    const dataSql = `
      SELECT id, name, type, remark, content, created_at, updated_at 
      FROM ${this.tableName}
      ${whereString}
      ORDER BY ${safeSortBy} ${safeSortOrder}
      LIMIT @pageSize OFFSET @offset
    `
    const countSql = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereString}`

    try {
      const stmtData = this.db.prepare(dataSql)
      const listData = stmtData.all({ ...queryBindingParams, pageSize, offset })

      const stmtCount = this.db.prepare(countSql)
      const row = stmtCount.get(queryBindingParams)
      const total = row ? row.total : 0

      const processedList = listData.map(item => {
        let processedItem = item
        if (processedItem.type !== undefined && processedItem.type !== null) {
          processedItem.type = Number(processedItem.type)
        }
        // 反序列化 content
        if (processedItem.content && typeof processedItem.content === 'string') {
          try {
            processedItem.content = JSON.parse(processedItem.content)
          } catch (e) {
            console.error(`[Service.list] Failed to parse content for template ID ${processedItem.id}:`, e)
            processedItem.content = { type: 'doc', content: [] } // 解析失败则设为一个空的 Tiptap JSON 对象
          }
        }
        return processedItem
      })

      return {
        success: true,
        data: {
          list: processedList,
          pagination: {
            total,
            page: Number(page),
            pageSize: Number(pageSize),
            totalPages: Math.ceil(total / pageSize)
          }
        }
      }
    } catch (error) {
      console.error('Error listing tender scheme templates:', error)
      return this.errorResponse(`获取招标方案模板列表失败: ${error.message}`)
    }
  }

  /**
   * 根据ID获取招标方案模板
   * @param {number} id - 模板ID
   * @returns {object} { success: boolean, data?: template, message?: string }
   */
  getById(id) {
    // 移除 async
    if (!this.db) return this.dbErrorResponse()
    const sql = `
      SELECT id, name, type, remark, content, created_at, updated_at 
      FROM ${this.tableName} 
      WHERE id = ?
    `
    try {
      const stmt = this.db.prepare(sql)
      const template = stmt.get(id)
      if (template) {
        let processedTemplate = template
        if (processedTemplate.type !== undefined && processedTemplate.type !== null) {
          processedTemplate.type = Number(processedTemplate.type)
        }
        // 反序列化 content
        if (processedTemplate.content && typeof processedTemplate.content === 'string') {
          try {
            processedTemplate.content = JSON.parse(processedTemplate.content)
          } catch (e) {
            console.error(`[Service.getById] Failed to parse content for template ID ${id}:`, e)
            processedTemplate.content = null //或默认JSON结构 {type: 'doc', content: []}
          }
        }
        return { success: true, data: processedTemplate }
      } else {
        return this.errorResponse('未找到指定的招标方案模板', 404)
      }
    } catch (error) {
      console.error(`Error getting tender scheme template by ID ${id}:`, error)
      return this.errorResponse(`获取招标方案模板详情失败: ${error.message}`)
    }
  }

  /**
   * 更新招标方案模板
   * @param {number} id - 模板ID
   * @param {object} data - 更新数据 { name?, type?, remark?, content? }
   * @returns {object} { success: boolean, data?: template, message?: string }
   */
  update(id, data) {
    // 移除 async
    if (!this.db) return this.dbErrorResponse()

    const { name, type, remark, content } = data
    // console.log('=========='); // 保留您添加的日志，或按需移除
    // console.log(data);

    let fieldsToUpdate = []
    let valuesForUpdate = {}

    if (name !== undefined) {
      valuesForUpdate.name = name
      fieldsToUpdate.push('name = @name')
    }
    if (type !== undefined) {
      const numericType = Number(type)
      if (isNaN(numericType) || !Number.isInteger(numericType)) {
        return this.errorResponse('方案类型必须是有效的整数值 (更新时)', 400)
      }
      fieldsToUpdate.push('type = @type')
      valuesForUpdate.type = numericType
    }
    if (remark !== undefined) {
      valuesForUpdate.remark = remark
      fieldsToUpdate.push('remark = @remark')
    }
    if (content !== undefined) {
      // 处理content的JSON序列化
      let contentToStore = null
      if (content && (typeof content === 'object' || typeof content === 'string')) {
        try {
          contentToStore = typeof content === 'object' ? JSON.stringify(content) : content
        } catch (e) {
          console.error('投标方案模板内容JSON序列化失败:', e)
          return { success: false, message: '内容格式错误' }
        }
      }
      valuesForUpdate.content = contentToStore
      fieldsToUpdate.push('content = @content')
    }

    if (fieldsToUpdate.length === 0) {
      // 如果没有要更新的字段，但前端仍可能期望返回更新后的记录，可以调用getById
      const currentRecord = this.getById(id)
      if (currentRecord.success) {
        return { success: true, message: '数据无变化', data: currentRecord.data }
      }
      return { success: true, message: '数据无变化 (获取当前记录失败)' }
    }

    fieldsToUpdate.push("updated_at = datetime('now', 'localtime')")
    valuesForUpdate.id = id

    const updateSql = `
      UPDATE ${this.tableName}
      SET ${fieldsToUpdate.join(', ')}
      WHERE id = @id
    `

    try {
      const stmt = this.db.prepare(updateSql)
      const info = stmt.run(valuesForUpdate)
      if (info.changes > 0) {
        const updatedRecord = this.getById(id) // 获取更新后的记录
        if (updatedRecord.success) {
          return { success: true, message: '招标方案模板更新成功', data: updatedRecord.data }
        }
        return { success: true, message: '招标方案模板更新成功 (获取详情失败)' }
      } else {
        // 可能ID不存在，或者数据确实没有变化（尽管我们上面判断了fieldsToUpdate.length）
        const existingRecord = this.getById(id)
        if (!existingRecord.success) {
          return this.errorResponse('更新失败，记录未找到或数据无变化。', 404)
        }
        // 如果记录存在但无变化，理论上应该被 fieldsToUpdate.length === 0 捕获
        return { success: true, message: '数据无变化或记录未找到', data: existingRecord.data }
      }
    } catch (error) {
      console.error(`Error updating tender scheme template ID ${id}:`, error)
      if ((error.message && error.message.toLowerCase().includes('constraint')) || error.message.toLowerCase().includes('bind')) {
        return this.errorResponse(`更新招标方案模板失败: 数据库约束或绑定错误。可能的原因：无效的数据类型或值。详细信息: ${error.message}`)
      }
      return this.errorResponse(`更新招标方案模板失败: ${error.message}`)
    }
  }

  /**
   * 删除招标方案模板
   * @param {number} id - 模板ID
   * @returns {object} { success: boolean, message?: string }
   */
  delete(id) {
    // 移除 async
    if (!this.db) return this.dbErrorResponse()

    try {
      // 首先检查是否有招标方案在使用这个模板
      const schemesCount = this.db.prepare(`SELECT COUNT(*) as count FROM ${sqlitedbService.tenderSchemesTableName} WHERE template_id = ?`).get(id)

      if (schemesCount && schemesCount.count > 0) {
        return this.errorResponse('无法删除模板：该模板正在被 ' + schemesCount.count + ' 个招标方案使用。请先解除关联或删除相关招标方案。', 409)
      }

      const sql = `DELETE FROM ${this.tableName} WHERE id = ?`
      const stmt = this.db.prepare(sql)
      const info = stmt.run(id)

      if (info.changes > 0) {
        return { success: true, message: '招标方案模板删除成功' }
      } else {
        return this.errorResponse('未找到要删除的招标方案模板', 404)
      }
    } catch (error) {
      console.error(`Error deleting tender scheme template ID ${id}:`, error)
      return this.errorResponse(`删除招标方案模板失败: ${error.message}`)
    }
  }
}

module.exports = TenderSchemeTemplateService
