# 数据库迁移指南

## 概述

此迁移脚本用于将数据库从 v1.0 版本（main.sql）升级到 v2.0 版本（sqlitedb.js），主要变更包括：

1. **风险评估模块重构**：将分离的 `risk_assessment_tasks` 和 `risk_assessment_results` 表合并为统一的 `risk_assessment` 表
2. **字段结构调整**：移除主表中的 `risk_status` 和 `last_risk_assessment_at` 字段，使用 `risk_assessment_id` 外键关联
3. **模板段落内容表优化**：添加 `template_id` 字段建立正确的关联关系

## 使用步骤

### 1. 备份数据库
```bash
# 在执行迁移前，务必备份原数据库文件
cp data/sqlite-app.db data/sqlite-app.db.backup
```

### 2. 执行迁移脚本

#### 方法一：使用 SQLite 命令行工具
```bash
sqlite3 data/sqlite-app.db < migration_v1_to_v2.sql
```

#### 方法二：在应用程序中执行
```javascript
const fs = require('fs');
const { sqlitedbService } = require('./electron/service/database/sqlitedb');

// 读取迁移脚本
const migrationSql = fs.readFileSync('./migration_v1_to_v2.sql', 'utf8');

// 执行迁移
try {
  sqlitedbService.db.exec(migrationSql);
  console.log('数据库迁移完成');
} catch (error) {
  console.error('迁移失败:', error);
  // 恢复备份
  // fs.copyFileSync('data/sqlite-app.db.backup', 'data/sqlite-app.db');
}
```

### 3. 验证迁移结果

#### 检查表结构
```sql
-- 验证新表是否创建成功
.schema risk_assessment
.schema tender_schemes
.schema bidding_documents

-- 检查数据完整性
SELECT '风险评估记录数量' as 项目, COUNT(*) as 数量 FROM risk_assessment
UNION ALL
SELECT '招标方案记录数量', COUNT(*) FROM tender_schemes  
UNION ALL
SELECT '招标文件记录数量', COUNT(*) FROM bidding_documents;
```

#### 检查版本信息
```sql
SELECT * FROM db_version;
```

## 迁移内容详解

### 1. 风险评估数据转换

- **原结构**：任务表 + 结果表分离存储
- **新结构**：统一表存储完整评估信息
- **转换逻辑**：
  - 将任务状态与结果综合判断最终风险级别
  - 将多个结果记录合并为 JSON 数组存储在 `ai_result` 字段

### 2. 主表关联调整

- **招标方案表**：添加 `risk_assessment_id` 外键
- **招标文件表**：添加 `risk_assessment_id` 外键
- **历史版本表**：添加 `risk_assessment_id` 字段保留历史关联

### 3. 模板段落内容表优化

- 添加 `template_id` 字段建立与模板的正确关联
- 根据现有的 `paragraph_refs` 数据推断关联关系

## 安全措施

1. **数据备份**：迁移前自动创建临时备份表
2. **事务处理**：关闭外键约束，确保迁移操作原子性
3. **备份保留**：临时备份表默认保留，可手动删除
4. **版本记录**：创建版本管理表记录迁移历史

## 故障恢复

如果迁移过程中出现问题：

1. **从备份恢复**：
   ```bash
   cp data/sqlite-app.db.backup data/sqlite-app.db
   ```

2. **检查备份表**：
   ```sql
   -- 临时备份表仍在数据库中，可用于数据恢复
   SELECT * FROM temp_risk_assessment_tasks_backup LIMIT 5;
   ```

3. **手动清理**：
   ```sql
   -- 如需清理临时表，取消注释脚本末尾的 DROP 语句
   DROP TABLE temp_risk_assessment_tasks_backup;
   -- ... 其他临时表
   ```

## 注意事项

1. **执行环境**：确保在正确的数据库文件上执行迁移
2. **权限检查**：确保对数据库文件有读写权限
3. **磁盘空间**：迁移过程中会创建临时表，确保有足够磁盘空间
4. **应用停止**：建议在应用停止状态下执行迁移，避免数据冲突

## 验证清单

- [ ] 原数据库已备份
- [ ] 迁移脚本执行成功
- [ ] 新表结构正确创建
- [ ] 数据记录数量一致
- [ ] 外键关联正确建立
- [ ] 应用程序正常启动
- [ ] 功能测试通过 