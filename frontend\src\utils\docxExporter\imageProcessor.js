import * as docx from 'docx'

/**
 * 图片处理模块
 * 提供图片加载、处理和转换为 docx.js ImageRun 的功能
 */

/**
 * 异步加载图片并转换为ArrayBuffer
 * @param {string} imageSrc - 图片源URL
 * @returns {Promise<ArrayBuffer>} 图片数据
 */
export async function loadImageAsArrayBuffer(imageSrc) {
  try {
    console.log('[DOCX Export] 开始加载图片:', imageSrc)

    // 处理不同类型的图片URL
    let imageUrl = imageSrc

    // 如果是完整的HTTP/HTTPS URL，直接使用
    if (imageSrc.startsWith('http://') || imageSrc.startsWith('https://')) {
      imageUrl = imageSrc
    }
    // 如果是相对路径，转换为绝对路径
    else if (imageSrc.startsWith('/')) {
      imageUrl = new URL(imageSrc, window.location.origin).href
    }
    // 如果是Vite的资源路径（以/src/assets/开头），转换为完整URL
    else if (imageSrc.startsWith('/src/assets/')) {
      imageUrl = new URL(imageSrc, window.location.origin).href
    }
    // 如果是blob URL或data URL，直接使用
    else if (imageSrc.startsWith('blob:') || imageSrc.startsWith('data:')) {
      imageUrl = imageSrc
    }
    // 其他情况，尝试作为相对于当前域的路径
    else {
      imageUrl = new URL(imageSrc, window.location.origin).href
    }

    console.log('[DOCX Export] 实际请求URL:', imageUrl)

    const response = await fetch(imageUrl, {
      mode: 'cors', // 允许跨域请求
      credentials: 'same-origin' // 发送同源凭据
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    console.log('[DOCX Export] 图片加载成功，大小:', arrayBuffer.byteLength, 'bytes')
    return arrayBuffer
  } catch (error) {
    console.error('[DOCX Export] 图片加载失败:', error)
    throw error
  }
}

/**
 * 检测图片格式
 * @param {ArrayBuffer} imageBuffer - 图片数据
 * @returns {string} 图片格式 ('jpg', 'png', 'gif')
 */
export function detectImageFormat(imageBuffer) {
  const uint8Array = new Uint8Array(imageBuffer)

  // 更精确的图片格式检测
  if (uint8Array.length >= 2 && uint8Array[0] === 0xff && uint8Array[1] === 0xd8) {
    return 'jpg'
  } else if (uint8Array.length >= 8 && uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && uint8Array[2] === 0x4e && uint8Array[3] === 0x47) {
    return 'png'
  } else if (uint8Array.length >= 6 && uint8Array[0] === 0x47 && uint8Array[1] === 0x49 && uint8Array[2] === 0x46 && uint8Array[3] === 0x38) {
    return 'gif'
  } else {
    console.warn('[DOCX Export] 无法识别图片格式，使用PNG作为默认格式')
    return 'png'
  }
}

/**
 * 验证图片数据和尺寸
 * @param {ArrayBuffer} imageBuffer - 图片数据
 * @param {number} width - 图片宽度
 * @param {number} height - 图片高度
 * @throws {Error} 如果验证失败
 */
export function validateImageData(imageBuffer, width, height) {
  // 验证图片大小，避免过大的图片导致Word问题
  if (imageBuffer.byteLength > 10 * 1024 * 1024) {
    // 10MB限制
    throw new Error('图片文件过大，请使用小于10MB的图片')
  }

  // 验证图片尺寸
  if (!width || !height || width <= 0 || height <= 0) {
    throw new Error(`图片尺寸无效: width=${width}, height=${height}`)
  }
}

/**
 * 异步处理图片节点，将其转换为真正的ImageRun
 * @param {object} asyncImageNode - 异步图片节点
 * @returns {Promise<ImageRun|TextRun>} ImageRun对象（所有图片都是内联的）或错误文本
 */
export async function processAsyncImageNode(asyncImageNode) {
  try {
    console.log('[DOCX Export] 开始处理图片节点:', asyncImageNode)

    // 验证图片源
    if (!asyncImageNode.src) {
      throw new Error('图片源为空')
    }

    const imageBuffer = await loadImageAsArrayBuffer(asyncImageNode.src)

    // 图片尺寸处理 - asyncImageNode.width/height 已经是像素值，直接使用
    const defaultWidthPixels = 400 // 默认宽度400px
    const defaultHeightPixels = 300 // 默认高度300px

    const finalWidth = asyncImageNode.width || defaultWidthPixels
    const finalHeight = asyncImageNode.height || defaultHeightPixels

    // 直接使用像素值，不需要任何转换
    // 这样导出后的尺寸和页面显示的px单位值完全一致

    console.log('[DOCX Export] 图片尺寸计算:', {
      originalWidth: asyncImageNode.width,
      originalHeight: asyncImageNode.height,
      finalWidth,
      finalHeight,
      bufferSize: imageBuffer.byteLength
    })

    // 检测图片格式
    const imageType = detectImageFormat(imageBuffer)
    console.log('[DOCX Export] 检测到图片格式:', imageType, '大小:', imageBuffer.byteLength, 'bytes')

    // 验证图片数据和尺寸
    validateImageData(imageBuffer, finalWidth, finalHeight)

    // 根据官方文档创建ImageRun - 默认就是内联图片
    const imageRun = new docx.ImageRun({
      type: imageType,
      data: imageBuffer,
      transformation: {
        width: finalWidth,
        height: finalHeight
      }
    })

    console.log('[DOCX Export] 内联ImageRun创建成功')

    // 默认情况下ImageRun就是内联的，直接返回
    return imageRun
  } catch (error) {
    console.error('[DOCX Export] 处理图片失败:', {
      error: error.message,
      stack: error.stack,
      imageSrc: asyncImageNode.src,
      imageNode: asyncImageNode
    })

    // 如果图片加载失败，返回错误提示文本（所有图片都是内联的）
    return new docx.TextRun({
      text: `[图片加载失败: ${asyncImageNode.alt || '图片'} - ${error.message}]`,
      color: 'FF0000',
      italics: true
    })
  }
}
