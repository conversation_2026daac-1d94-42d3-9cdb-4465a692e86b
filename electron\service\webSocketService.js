'use strict'

const WebSocket = require('ws')
const { EventEmitter } = require('events')

/**
 * WebSocket服务
 * 负责管理WebSocket连接和消息推送
 */
class WebSocketService extends EventEmitter {
  constructor() {
    super()
    this.wss = null
    this.clients = new Map() // 存储连接的客户端，key为userId，value为WebSocket连接
    this.isRunning = false
  }

  /**
   * 启动WebSocket服务器
   * @param {number} port WebSocket端口
   */
  start(port = 8080) {
    if (this.isRunning) {
      console.log('WebSocket服务已经在运行中')
      return
    }

    this.wss = new WebSocket.Server({ port })
    this.isRunning = true

    this.wss.on('connection', (ws, req) => {
      console.log('新的WebSocket连接建立')

      // 处理客户端消息
      ws.on('message', message => {
        try {
          const data = JSON.parse(message)
          this.handleClientMessage(ws, data)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
          ws.send(
            JSON.stringify({
              type: 'error',
              message: '消息格式错误'
            })
          )
        }
      })

      // 处理连接关闭
      ws.on('close', () => {
        console.log('WebSocket连接关闭')
        // 从客户端列表中移除
        for (const [userId, client] of this.clients.entries()) {
          if (client === ws) {
            this.clients.delete(userId)
            console.log(`用户 ${userId} 的WebSocket连接已移除`)
            break
          }
        }
      })

      // 处理连接错误
      ws.on('error', error => {
        console.error('WebSocket连接错误:', error)
      })
    })

    console.log(`WebSocket服务启动成功，端口: ${port}`)
  }

  /**
   * 停止WebSocket服务器
   */
  stop() {
    if (!this.isRunning) {
      console.log('WebSocket服务未在运行')
      return
    }

    // 关闭所有连接
    this.clients.forEach((ws, userId) => {
      ws.close()
      console.log(`关闭用户 ${userId} 的WebSocket连接`)
    })
    this.clients.clear()

    // 关闭服务器
    if (this.wss) {
      this.wss.close(() => {
        console.log('WebSocket服务器已停止')
      })
    }

    this.isRunning = false
  }

  /**
   * 处理客户端消息
   * @param {WebSocket} ws WebSocket连接
   * @param {Object} data 消息数据
   */
  handleClientMessage(ws, data) {
    switch (data.type) {
      case 'auth':
        // 用户认证，将连接与用户ID关联
        if (data.userId) {
          this.clients.set(data.userId, ws)
          console.log(`用户 ${data.userId} 已连接WebSocket`)
          ws.send(
            JSON.stringify({
              type: 'auth_success',
              message: '认证成功'
            })
          )
        } else {
          ws.send(
            JSON.stringify({
              type: 'auth_failed',
              message: '缺少用户ID'
            })
          )
        }
        break

      case 'ping':
        // 心跳检测
        ws.send(JSON.stringify({ type: 'pong' }))
        break

      default:
        console.log('收到未知类型的消息:', data.type)
        break
    }
  }

  /**
   * 发送风险评估完成通知
   * @param {Object} data 通知数据
   * @returns {boolean} 是否发送成功
   */
  sendRiskAssessmentCompleted(data) {
    const { userId, taskId, documentType, documentId, result } = data

    const ws = this.clients.get(userId)
    if (!ws) {
      console.log(`用户 ${userId} 未连接WebSocket，无法发送通知`)
      return false
    }

    try {
      const message = {
        type: 'risk_assessment_completed',
        data: {
          taskId,
          documentType,
          documentId,
          result,
          timestamp: new Date().toISOString()
        }
      }

      ws.send(JSON.stringify(message))
      console.log(`风险评估完成通知已发送给用户 ${userId}`)
      return true
    } catch (error) {
      console.error('发送风险评估完成通知失败:', error)
      return false
    }
  }

  /**
   * 发送风险评估失败通知
   * @param {Object} data 通知数据
   * @returns {boolean} 是否发送成功
   */
  sendRiskAssessmentFailed(data) {
    const { taskId, error } = data

    // 需要先获取任务信息来确定用户ID
    // 这里假设可以通过其他方式获取用户ID
    const userId = data.userId

    const ws = this.clients.get(userId)
    if (!ws) {
      console.log(`用户 ${userId} 未连接WebSocket，无法发送通知`)
      return false
    }

    try {
      const message = {
        type: 'risk_assessment_failed',
        data: {
          taskId,
          error,
          timestamp: new Date().toISOString()
        }
      }

      ws.send(JSON.stringify(message))
      console.log(`风险评估失败通知已发送给用户 ${userId}`)
      return true
    } catch (error) {
      console.error('发送风险评估失败通知失败:', error)
      return false
    }
  }

  /**
   * 发送风险评估取消通知
   * @param {Object} data 通知数据
   * @returns {boolean} 是否发送成功
   */
  sendRiskAssessmentCancelled(data) {
    const { taskId, userId } = data

    const ws = this.clients.get(userId)
    if (!ws) {
      console.log(`用户 ${userId} 未连接WebSocket，无法发送通知`)
      return false
    }

    try {
      const message = {
        type: 'risk_assessment_cancelled',
        data: {
          taskId,
          timestamp: new Date().toISOString()
        }
      }

      ws.send(JSON.stringify(message))
      console.log(`风险评估取消通知已发送给用户 ${userId}`)
      return true
    } catch (error) {
      console.error('发送风险评估取消通知失败:', error)
      return false
    }
  }

  /**
   * 向指定用户发送自定义消息
   * @param {number} userId 用户ID
   * @param {Object} message 消息内容
   * @returns {boolean} 是否发送成功
   */
  sendMessageToUser(userId, message) {
    const ws = this.clients.get(userId)
    if (!ws) {
      console.log(`用户 ${userId} 未连接WebSocket，无法发送消息`)
      return false
    }

    try {
      ws.send(JSON.stringify(message))
      console.log(`消息已发送给用户 ${userId}`)
      return true
    } catch (error) {
      console.error('发送消息失败:', error)
      return false
    }
  }

  /**
   * 广播消息给所有连接的用户
   * @param {Object} message 消息内容
   */
  broadcast(message) {
    this.clients.forEach((ws, userId) => {
      try {
        ws.send(JSON.stringify(message))
        console.log(`广播消息已发送给用户 ${userId}`)
      } catch (error) {
        console.error(`向用户 ${userId} 广播消息失败:`, error)
      }
    })
  }

  /**
   * 获取在线用户数量
   * @returns {number} 在线用户数量
   */
  getOnlineUserCount() {
    return this.clients.size
  }

  /**
   * 获取在线用户列表
   * @returns {Array} 在线用户ID列表
   */
  getOnlineUsers() {
    return Array.from(this.clients.keys())
  }

  /**
   * 检查用户是否在线
   * @param {number} userId 用户ID
   * @returns {boolean} 是否在线
   */
  isUserOnline(userId) {
    return this.clients.has(userId)
  }
}

// 创建单例实例
const webSocketService = new WebSocketService()

module.exports = { webSocketService }
