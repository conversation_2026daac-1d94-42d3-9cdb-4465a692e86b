<template>
  <node-view-wrapper class="toc-node-view" :class="{ 'is-selected': selected, 'is-readonly': isReadonly }">
    <div class="toc-container" @click="handleTocClick">
      <div class="toc-preview">
        <div class="toc-title">📋 {{ node.attrs.title || '目录' }}</div>
        <div class="toc-info">包含标题级别: {{ node.attrs.levels || '1,2,3' }} | 导出时将自动生成目录</div>
      </div>

      <!-- 选中时显示的控制面板 (填充模式和只读状态下不显示) -->
      <div v-if="showControls && !fillMode.value && !isReadonly" class="toc-controls" @click="handleControlsClick">
        <a-space direction="vertical" size="small">
          <!-- 目录标题控制 -->
          <div class="control-group">
            <span class="control-label">标题:</span>
            <a-input v-model:value="editableTitle" placeholder="目录" @change="updateTitle" style="width: 200px" />
          </div>

          <!-- 标题级别控制 -->
          <div class="control-group">
            <span class="control-label">级别:</span>
            <a-checkbox-group v-model:value="editableLevels" @change="updateLevels">
              <a-checkbox value="1">H1</a-checkbox>
              <a-checkbox value="2">H2</a-checkbox>
              <a-checkbox value="3">H3</a-checkbox>
              <a-checkbox value="4">H4</a-checkbox>
              <a-checkbox value="5">H5</a-checkbox>
              <a-checkbox value="6">H6</a-checkbox>
            </a-checkbox-group>
          </div>

          <!-- 操作按钮 -->
          <div class="control-group">
            <a-space size="small">
              <a-button size="small" @click="resetToDefault">重置</a-button>
              <a-button size="small" type="primary" @click="hideControls">完成</a-button>
            </a-space>
          </div>
        </a-space>
      </div>
    </div>
  </node-view-wrapper>
</template>

<script setup>
import { NodeViewWrapper, nodeViewProps } from '@tiptap/vue-3'
import { ref, computed, onMounted, onBeforeUnmount, inject } from 'vue'

const props = defineProps(nodeViewProps)

// 注入填充模式状态
const fillMode = inject('fillMode', false)

// 可编辑的属性
const editableTitle = ref('')
const editableLevels = ref([])

// 控制面板显示状态
const showControls = ref(false)

// 计算是否为只读状态
const isReadonly = computed(() => {
  return props.node.attrs.isReadonly === true
})

// 处理目录点击事件
const handleTocClick = event => {
  console.log('[TableOfContentsNodeView] TOC clicked')

  // 检查是否为只读状态
  if (isReadonly.value) {
    console.log('[TableOfContentsNodeView] 只读目录不响应点击')
    event.stopPropagation() // 阻止事件冒泡
    return
  }

  // 检查编辑器是否可编辑（只读模式下不做任何操作）
  if (!props.editor.isEditable) {
    console.log('[TableOfContentsNodeView] 只读模式下点击目录，不做任何操作')
    event.stopPropagation() // 阻止事件冒泡
    return
  }

  // 如果在填充模式下，不显示控制面板，而是重定向到最近的字段
  if (fillMode.value) {
    console.log('[TableOfContentsNodeView] 填充模式下点击目录，重定向到最近字段')
    // 不阻止事件冒泡，让FillModeExtension处理
    return
  }

  event.stopPropagation() // 阻止事件冒泡

  // 切换控制面板显示状态
  showControls.value = !showControls.value

  // 如果显示控制面板，添加全局点击监听器
  if (showControls.value) {
    // 延迟添加监听器，避免立即触发
    setTimeout(() => {
      document.addEventListener('click', handleDocumentClick)
    }, 100)
  } else {
    // 如果隐藏控制面板，移除全局监听器
    document.removeEventListener('click', handleDocumentClick)
  }
}

// 处理文档点击事件（用于隐藏控制面板）
const handleDocumentClick = event => {
  console.log('[TableOfContentsNodeView] Document clicked')

  // 检查点击是否在目录容器或控制面板内
  const tocContainer = event.target.closest('.toc-container')
  const controlsPanel = event.target.closest('.toc-controls')

  if (!tocContainer && !controlsPanel) {
    // 点击在目录容器和控制面板外，隐藏控制面板
    showControls.value = false
    document.removeEventListener('click', handleDocumentClick)
  }
}

// 处理控制面板点击
const handleControlsClick = event => {
  console.log('[TableOfContentsNodeView] Click on controls')
  // 阻止事件冒泡，防止隐藏控制面板
  event.stopPropagation()
}

// 初始化可编辑属性
onMounted(() => {
  // 初始化标题
  editableTitle.value = props.node.attrs.title || '目录'

  // 初始化级别
  const levelsStr = props.node.attrs.levels || '1,2,3'
  editableLevels.value = levelsStr
    .split(',')
    .map(level => level.trim())
    .filter(level => level)

  // 初始化控制面板状态为隐藏
  showControls.value = false
})

// 更新属性的通用方法
const updateAttributes = attrs => {
  props.updateAttributes(attrs)
}

// 更新标题
const updateTitle = () => {
  console.log('[TableOfContentsNodeView] updateTitle called')
  updateAttributes({
    title: editableTitle.value || '目录'
  })
}

// 更新级别
const updateLevels = () => {
  console.log('[TableOfContentsNodeView] updateLevels called')
  const levelsStr = editableLevels.value.sort((a, b) => parseInt(a) - parseInt(b)).join(',')
  updateAttributes({
    levels: levelsStr || '1,2,3'
  })
}

// 重置为默认值
const resetToDefault = () => {
  editableTitle.value = '目录'
  editableLevels.value = ['1', '2', '3']
  updateAttributes({
    title: '目录',
    levels: '1,2,3'
  })
}

// 隐藏控制面板
const hideControls = () => {
  showControls.value = false
  document.removeEventListener('click', handleDocumentClick)
}

// 组件卸载时清理事件监听器
onBeforeUnmount(() => {
  // 移除全局点击监听器
  document.removeEventListener('click', handleDocumentClick)
})
</script>

<style lang="less" scoped>
.toc-node-view {
  display: block;
  margin: 16px 0;

  &.is-selected {
    .toc-container::after {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border: 2px solid #1890ff;
      border-radius: 6px;
      pointer-events: none;
    }
  }

  // 只读状态样式
  &.is-readonly {
    .toc-preview {
      background-color: #f5f5f5;
      border: 2px dashed #1890ff;
      position: relative;
      cursor: default;

      &::before {
        content: '继承内容（只读）';
        position: absolute;
        top: -8px;
        right: 8px;
        background: #1890ff;
        color: white;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 2px;
        z-index: 1;
      }

      &:hover {
        background-color: #f0f0f0;
      }
    }

    .toc-container {
      pointer-events: none; // 只读模式下禁用点击
      cursor: default;
    }
  }
}

.toc-container {
  position: relative;
  display: block;
}

.toc-preview {
  border: 2px dashed #1890ff;
  margin: 16px 0;
  padding: 16px;
  background-color: #f6f8fa;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    background-color: #e6f7ff;
    border-color: #40a9ff;
  }
}

.toc-title {
  text-align: center;
  font-weight: bold;
  margin-bottom: 8px;
  color: #1890ff;
  font-size: 16px;
}

.toc-info {
  text-align: center;
  font-size: 12px;
  color: #666;
}

.toc-controls {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  margin-top: 8px;
  white-space: nowrap;
  min-width: 400px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.control-label {
  font-size: 14px;
  color: #666;
  min-width: 50px;
  font-weight: 500;
}

:deep(.ant-checkbox-group) {
  display: flex;
  gap: 8px;
}

:deep(.ant-checkbox-wrapper) {
  margin-right: 0;
}
</style>
