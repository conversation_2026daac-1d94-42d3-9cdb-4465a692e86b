<template>
  <!-- <div class="page-container form-container"> -->

    <a-page-header :title="isEditMode ? '编辑招标文件模板' : '新建招标文件模板'" @back="handleCancel" />
    <a-card :loading="isLoading">
    <!-- <a-card :bordered="false" :loading="isLoading"> -->
      <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" layout="horizontal">
        <a-form-item label="模板名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入模板名称" />
        </a-form-item>

        <a-form-item label="文件类型" name="file_type">
          <a-input v-model:value="formState.file_type" placeholder="例如：招标文件、资格预审文件" />
        </a-form-item>

        <a-form-item label="父模板" name="parent_id">
          <a-tree-select
            v-model:value="formState.parent_id"
            :disabled="isEditMode"
            style="width: 100%"
            :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
            placeholder="选择父模板 (可选，不选则为顶级模板)"
            allow-clear
            tree-default-expand-all
            :tree-data="parentTemplateOptions"
            tree-node-filter-prop="title"
            show-search
            :fieldNames="{ children: 'children', label: 'name', value: 'id' }"
            @change="handleParentTemplateChange"
          />
        </a-form-item>

        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="3" />
        </a-form-item>

        <a-form-item label="模板内容" name="content">
          <tiptap-editor
            v-if="editorVisible"
            ref="tiptapEditorRef"
            v-model="formState.content"
            :editable="true"
            :fill-mode="false"
            :show-toolbar="true"
            min-height="400px"
            placeholder="请输入模板内容..."
            :readonly-paragraph-ids="readonlyParagraphIds"
            :current-template-id="isEditMode ? templateId : null"
          />
          <div v-else class="editor-loading-placeholder">编辑器正在加载...</div>
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
          <a-space>
            <a-button type="primary" @click="handleSubmit" :loading="isLoading" :disabled="isExportingDocx">
              {{ isEditMode ? '更新模板' : '创建模板' }}
            </a-button>
            <a-button @click="handleExportToDocx" :loading="isExportingDocx" :disabled="!editorVisible || isLoading || !formState.content">导出为 DOCX</a-button>
            <a-button @click="handleCancel" :disabled="isLoading || isExportingDocx">取消</a-button>
          </a-space>
        </a-form-item>
      </a-form>
    <!-- </a-card> -->
    </a-card>
  <!-- </div> -->
</template>

<script setup>
import { ref, reactive, watch, onMounted, nextTick, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
// 使用统一的HTTP API
import api from '@/api'
import TiptapEditor from '@/components/TiptapEditor/index.vue'
import { exportTiptapContentToDocx } from '@/utils/docxExporter'

const route = useRoute()
const router = useRouter()

const formRef = ref(null)
const tiptapEditorRef = ref(null)
const isLoading = ref(false)
const isExportingDocx = ref(false)
const editorVisible = ref(false)

// 从路由参数获取模板ID，判断是否为编辑模式
const templateId = ref(route.params.id || null)
const isEditMode = computed(() => !!templateId.value)

const initialFormState = () => ({
  id: undefined,
  name: '',
  file_type: '',
  parent_id: null,
  remark: '',
  content: { type: 'doc', content: [{ type: 'paragraph' }] }
})

const formState = reactive(initialFormState())
const parentTemplateOptions = ref([]) // 父模板树形选择数据
const allTemplatesFlat = ref([]) // 所有模板的扁平列表，用于查找
const readonlyParagraphIds = ref([]) // 只读段落ID数组（数据库中的段落ID）

const rules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  content: [
    {
      required: true,
      validator: async (_, value) => {
        if (
          !value ||
          !value.content ||
          value.content.length === 0 ||
          (value.content.length === 1 && value.content[0].type === 'paragraph' && (!value.content[0].content || value.content[0].content.length === 0))
        ) {
          return Promise.reject('模板内容不能为空')
        }
        return Promise.resolve()
      },
      trigger: 'change'
    }
  ]
}

// 计算模板层级深度
const getTemplateDepth = (node, allTemplates) => {
  if (!node.parent_id) return 1
  const parent = allTemplates.find(t => t.id === node.parent_id)
  if (!parent) return 1
  return getTemplateDepth(parent, allTemplates) + 1
}

// 递归展平模板树数据并添加 disabled 属性防止选择自身或子孙作为父模板，以及层级限制
const flattenTemplatesAndDisable = (nodes, currentEditId = null, parentPathIds = [], allTemplates = []) => {
  let result = []
  for (const node of nodes) {
    const newPathIds = [...parentPathIds, node.id]
    let disabled = false

    if (currentEditId) {
      // 编辑时，不能选自己作为父模板
      if (node.id === currentEditId) {
        disabled = true
      }
    }

    // 层级限制：不能选择第5级模板作为父模板
    const nodeDepth = getTemplateDepth(node, allTemplates)
    if (nodeDepth >= 5) {
      disabled = true
    }

    const processedNode = { ...node, disabled }
    result.push(processedNode)
    if (node.children && node.children.length > 0) {
      processedNode.children = flattenTemplatesAndDisable(node.children, currentEditId, newPathIds, allTemplates)
      result = result.concat(processedNode.children.filter(c => !result.find(r => r.id === c.id)))
    }
  }
  return result
}

// 移除指定节点及其所有子节点
const removeNodeFromTree = (nodes, nodeIdToRemove) => {
  return nodes
    .filter(node => node.id !== nodeIdToRemove)
    .map(node => {
      if (node.children) {
        node.children = removeNodeFromTree(node.children, nodeIdToRemove)
      }
      return node
    })
}

// 获取父模板选项
const fetchParentTemplateOptions = async (currentTemplateId = null) => {
  try {
    // 使用新的HTTP API对象结构
    const response = await api.biddingDocumentTemplate.getTree(null)
    if (response.success && response.data) {
      // 先生成扁平列表用于层级计算
      allTemplatesFlat.value = flattenTree(response.data)

      let treeData = response.data
      if (isEditMode.value && currentTemplateId) {
        // 编辑时，从选项中移除当前模板及其子模板，防止循环引用
        treeData = removeNodeFromTree(response.data, currentTemplateId)
      }

      // 应用层级限制和其他禁用逻辑
      parentTemplateOptions.value = applyDisableLogicToTree(treeData, currentTemplateId, allTemplatesFlat.value)
    } else {
      message.error('获取父模板列表失败: ' + response.message)
      parentTemplateOptions.value = []
      allTemplatesFlat.value = []
    }
  } catch (error) {
    message.error('加载父模板列表错误: ' + error.message)
    parentTemplateOptions.value = []
    allTemplatesFlat.value = []
  }
}

// 应用禁用逻辑到树形数据
const applyDisableLogicToTree = (nodes, currentEditId, allTemplates) => {
  return nodes.map(node => {
    let disabled = false

    if (currentEditId && node.id === currentEditId) {
      disabled = true
    }

    // 层级限制：不能选择第5级模板作为父模板
    const nodeDepth = getTemplateDepth(node, allTemplates)
    if (nodeDepth >= 5) {
      disabled = true
    }

    const processedNode = { ...node, disabled }

    if (node.children && node.children.length > 0) {
      processedNode.children = applyDisableLogicToTree(node.children, currentEditId, allTemplates)
    }

    return processedNode
  })
}

// 辅助函数：扁平化树结构
const flattenTree = nodes => {
  let result = []
  nodes.forEach(node => {
    result.push(node)
    if (node.children) {
      result = result.concat(flattenTree(node.children))
    }
  })
  return result
}

// 加载编辑数据
const loadEditData = async id => {
  isLoading.value = true
  try {
    // 使用新的HTTP API对象结构
    const response = await api.biddingDocumentTemplate.getById({ id })
    if (response.success && response.data) {
      const data = response.data
      formState.id = data.id
      formState.name = data.name
      formState.file_type = data.file_type
      formState.parent_id = data.parent_id
      formState.remark = data.remark

      // 获取完整的模板内容（包括继承的父模板内容）
      let completeContent = typeof data.content === 'string' ? JSON.parse(data.content) : data.content || initialFormState().content

      if (!completeContent.type) {
        completeContent = initialFormState().content
      }

      // 计算只读段落ID：编辑模式下，templateId与当前模板ID不相等的段落是只读的
      if (data.actual_paragraph_refs && Array.isArray(data.actual_paragraph_refs)) {
        const currentTemplateId = parseInt(id, 10)
        console.log('[loadEditData] 当前模板ID:', currentTemplateId, '段落引用数量:', data.actual_paragraph_refs.length)

        readonlyParagraphIds.value = data.actual_paragraph_refs
          .filter(ref => ref.templateId != currentTemplateId) // 使用 != 而不是 !== 来处理类型转换
          .map(ref => ref.paragraphId)

        console.log(
          '[loadEditData] 只读段落ID:',
          readonlyParagraphIds.value,
          '当前模板段落数量:',
          data.actual_paragraph_refs.filter(ref => ref.templateId == currentTemplateId).length
        )
      } else {
        readonlyParagraphIds.value = []
      }

      formState.content = completeContent
    } else {
      message.error('加载模板数据失败: ' + response.message)
      router.push({ name: 'BiddingDocumentTemplateList' })
    }
  } catch (error) {
    message.error('加载编辑数据错误: ' + error.message)
    router.push({ name: 'BiddingDocumentTemplateList' })
  } finally {
    isLoading.value = false
    nextTick(() => {
      editorVisible.value = true
    })
  }
}

// 处理父模板选择变化
const handleParentTemplateChange = async (value, node) => {
  console.log('父模板选择变化:', value, node)

  if (value && node) {
    // 确认是否加载父模板内容 (仅在新建时或用户确认时)
    if (!isEditMode.value) {
      await confirmLoadParentContentById(value)
    } else {
      const currentContentIsEmpty =
        !formState.content ||
        !formState.content.content ||
        (formState.content.content.length === 1 &&
          formState.content.content[0].type === 'paragraph' &&
          (!formState.content.content[0].content || formState.content.content[0].content.length === 0))
      if (!currentContentIsEmpty) {
        Modal.confirm({
          title: '加载父模板内容？',
          content: '您更改了父模板，是否要加载新父模板的内容？这将覆盖当前模板内容。',
          okText: '加载',
          cancelText: '不加载',
          onOk: () => confirmLoadParentContentById(value)
        })
      } else {
        await confirmLoadParentContentById(value)
      }
    }
  } else if (!value) {
    // 清空父模板选择时，重置为空内容
    formState.content = { type: 'doc', content: [{ type: 'paragraph' }] }
    readonlyParagraphIds.value = []
  }
}

// 通过父模板ID加载父模板内容
const confirmLoadParentContentById = async parentTemplateId => {
  if (!parentTemplateId) return

  editorVisible.value = false
  isLoading.value = true
  try {
    console.log('正在获取父模板详情，ID:', parentTemplateId)
    // 使用新的HTTP API对象结构
    const parentTemplateDetails = await api.biddingDocumentTemplate.getById({ id: parentTemplateId })
    console.log('父模板详情响应:', parentTemplateDetails)

    if (parentTemplateDetails.success && parentTemplateDetails.data) {
      const parentData = parentTemplateDetails.data

      // 直接使用后端返回的完整内容（已包含继承内容）
      if (parentData.content) {
        formState.content = parentData.content

        // 新建模板时：所有段落都是只读的（因为都是作为父模板内容展示）
        if (parentData.actual_paragraph_refs && Array.isArray(parentData.actual_paragraph_refs)) {
          readonlyParagraphIds.value = parentData.actual_paragraph_refs.map(ref => ref.paragraphId)
        } else {
          readonlyParagraphIds.value = []
        }

        console.log('父模板内容加载成功')
        console.log('设置的只读段落ID:', readonlyParagraphIds.value)
        message.success('父模板内容加载成功')
      } else {
        console.warn('父模板没有内容')
        message.warn('父模板没有内容，将创建空模板')
        formState.content = { type: 'doc', content: [{ type: 'paragraph' }] }
        readonlyParagraphIds.value = []
      }
    } else {
      message.error('获取父模板详细内容失败: ' + (parentTemplateDetails.message || '未知错误'))
      readonlyParagraphIds.value = []
    }
  } catch (error) {
    console.error('加载父模板内容时出错:', error)
    message.error('加载父模板内容时出错: ' + error.message)
    readonlyParagraphIds.value = []
  } finally {
    isLoading.value = false
    nextTick(() => {
      editorVisible.value = true
    })
  }
}

// 保留原有函数作为备用（如果需要从node对象加载）
const confirmLoadParentContent = async parentNode => {
  if (!parentNode || !parentNode.id) return
  await confirmLoadParentContentById(parentNode.id)
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    isLoading.value = true

    const payload = { ...formState }

    // 新的存储方案中，直接提交用户编辑的内容
    // 后端会自动处理段落的存储和引用关系
    if (typeof payload.content !== 'string') {
      payload.content = JSON.stringify(payload.content)
    }
    if (payload.parent_id === '' || payload.parent_id === undefined) {
      payload.parent_id = null
    }

    let response
    if (isEditMode.value) {
      // 使用新的HTTP API对象结构
      response = await api.biddingDocumentTemplate.update(payload)
    } else {
      // 使用新的HTTP API对象结构
      response = await api.biddingDocumentTemplate.create(payload)
    }

    if (response.success) {
      message.success(isEditMode.value ? '模板更新成功' : '模板创建成功')
      router.push({ name: 'BiddingDocumentTemplateList' })
    } else {
      message.error(response.message || '操作失败')
    }
  } catch (errorInfo) {
    if (errorInfo && errorInfo.errorFields && errorInfo.errorFields.length > 0) {
      message.error('请填写所有必填项并检查内容格式。')
    } else if (errorInfo instanceof Error) {
      message.error('发生错误: ' + errorInfo.message)
    } else {
      message.error('提交失败，请检查表单。')
    }
    console.error('Submit Error:', errorInfo)
  } finally {
    isLoading.value = false
  }
}

// 导出为DOCX
const handleExportToDocx = async () => {
  if (!formState.content || !formState.content.content) {
    message.error('没有可导出的内容')
    return
  }

  isExportingDocx.value = true
  try {
    const fileName = formState.name || '招标文件模板'
    await exportTiptapContentToDocx(formState.content, fileName)
    message.success('导出成功')
  } catch (error) {
    console.error('导出DOCX失败:', error)
    message.error('导出失败: ' + error.message)
  } finally {
    isExportingDocx.value = false
  }
}

const handleCancel = () => {
  router.push({ name: 'BiddingDocumentTemplateList' })
}

// 监听路由参数变化
watch(
  () => route.params.id,
  newId => {
    templateId.value = newId || null
    Object.assign(formState, initialFormState())
    editorVisible.value = false

    if (newId) {
      fetchParentTemplateOptions(newId)
      loadEditData(newId)
    } else {
      fetchParentTemplateOptions()
      nextTick(() => {
        editorVisible.value = true
      })
    }
  },
  { immediate: true }
)

onMounted(() => {
  // 数据获取移到 watch 中，确保每次路由变化时刷新
})
</script>

<style lang="less" scoped>
.form-container {
  padding: 16px;
}

.editor-loading-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  color: #999;
  background-color: #fafafa;
}
</style>
