# AiChat 组件模块

经过重构后的 AI 聊天组件模块，采用了组件拆分和组合式函数的设计模式，提高了代码的可维护性和复用性。

## 📁 文件结构

```
AiChat/
├── index.vue                    # 主组件 - 聊天面板容器
├── index.js                     # 导出配置文件
├── ChatContent.vue              # 聊天内容组件 - 消息显示和输入
├── SessionListModal.vue         # 会话列表模态框组件
├── EditSessionModal.vue         # 编辑会话标题模态框组件
├── composables/
│   └── useChatService.js        # 聊天服务组合式函数
└── README.md                    # 说明文档
```

## 🔧 组件说明

### 1. **index.vue** - 主组件
- **功能**: 聊天面板的主容器，负责布局和组件协调
- **特性**: 
  - 可折叠的侧边栏设计
  - 头部工具栏（新对话、会话列表、折叠）
  - 当前会话信息显示
  - 响应式设计支持移动端

### 2. **ChatContent.vue** - 聊天内容组件
- **功能**: 消息显示和输入交互
- **特性**:
  - Welcome 欢迎界面
  - Prompts 建议问题
  - Bubble 消息气泡列表
  - Sender 消息输入框
  - Suggestion 快速建议

### 3. **SessionListModal.vue** - 会话列表模态框
- **功能**: 会话管理界面
- **特性**:
  - 会话搜索过滤
  - 会话切换和选择
  - 会话重命名和删除
  - 空状态处理

### 4. **EditSessionModal.vue** - 编辑模态框
- **功能**: 编辑会话标题
- **特性**:
  - 简洁的编辑界面
  - 实时标题更新
  - 输入验证

### 5. **useChatService.js** - 聊天服务
- **功能**: 聊天相关的业务逻辑
- **特性**:
  - 完整的会话管理
  - 消息收发处理
  - 流式响应支持
  - 用户身份管理

## 📝 使用方法

### 基本使用

```vue
<template>
  <AiChat />
</template>

<script setup>
import AiChat from '@/layouts/components/AiChat'
</script>
```

## 🎯 优势特点

### 1. **模块化设计**
- 组件职责清晰，便于维护
- 子组件可独立复用
- 降低了代码复杂度

### 2. **组合式函数**
- 业务逻辑集中管理
- 状态和方法可在多个组件间共享
- 便于单元测试

### 3. **易于扩展**
- 新功能可以通过添加新组件实现
- 组合式函数支持功能扩展
- 保持向后兼容性 