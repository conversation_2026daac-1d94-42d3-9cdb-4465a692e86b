<template>
  <div class="chat-content-wrapper">
    <!-- AI聊天核心区域 -->
    <div class="chat-content">
      <!-- 欢迎界面 -->
      <!-- <Welcome v-if="!currentSession || messages.length === 0" title="AI助手" description="我是您的智能助手，可以帮助您解答招标文件相关问题"> -->
      <Welcome v-if="!currentSession || messages.length === 0" title="我是您的智能助手，可以帮助您解答相关问题" description="">
        <template #extra>
          <Prompts :items="suggestedPrompts" @item-click="handlePromptClick" />
        </template>
      </Welcome>

      <!-- 对话消息列表 -->
      <Bubble.List v-else ref="bubbleListRef" :items="bubbleMessages" :roles="bubbleRoles" :auto-scroll="autoScroll" />
    </div>

    <!-- 消息输入区域 -->
    <div class="chat-input">
      <!-- 使用Suggestion包装Sender，按照官方文档示例 -->
      <Suggestion
        :items="suggestions"
        :open="showSuggestions"
        @select="handleSuggestionSelect"
        @open-change="handleSuggestionOpenChange"
      >
        <template #default="{ onTrigger, onKeyDown }">
          <Sender
            ref="senderRef"
            :value="inputMessage"
            :placeholder="inputPlaceholder"
            :loading="sending"
            :disabled="sending"
            @submit="handleSendMessage"
            @cancel="handleCancelMessage"
            @change="(value) => handleInputChange(value, onTrigger)"
            @keydown="onKeyDown"
          />
        </template>
      </Suggestion>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, h } from 'vue'
import { Typography } from 'ant-design-vue'
import { Welcome, Prompts, Bubble, Sender, Suggestion } from 'ant-design-x-vue'
import markdownit from 'markdown-it'

// Props
const props = defineProps({
  currentSession: { type: Object, default: null },
  messages: { type: Array, default: () => [] },
  inputMessage: { type: String, default: '' },
  sending: { type: Boolean, default: false },
  streamingMessage: { type: String, default: '' },
  autoScroll: { type: Boolean, default: true }
})

// Emits
const emit = defineEmits(['update:inputMessage', 'send-message', 'cancel-message'])

// 响应式数据
const showSuggestions = ref(false)

// Markdown渲染器配置
const md = markdownit({ html: true, breaks: true, linkify: true })

// 计算属性
const inputPlaceholder = computed(() => {
  if (props.sending) return '正在发送消息...'
  if (!props.currentSession) return '请先创建一个会话...'
  return '输入您的问题... (输入 / 获取建议)'
})

// Markdown自定义渲染函数，按照官方文档示例实现
const renderMarkdown = content => {
  if (!content || typeof content !== 'string') return content

  return h(Typography, null, {
    default: () => h('div', { innerHTML: md.render(content) })
  })
}

// Bubble组件的角色配置
const bubbleRoles = computed(() => ({
  user: {
    placement: 'end',
    avatar: {
      style: { backgroundColor: '#1890ff', color: 'white' }
    },
    variant: 'shadow'
  },
  assistant: {
    placement: 'start',
    avatar: {
      style: { backgroundColor: '#52c41a', color: 'white' }
    },
    variant: 'filled',
    // typing: { step: 5, interval: 20 }, // 启用打字机效果
    messageRender: renderMarkdown // 使用Markdown渲染
  }
}))

// 转换消息格式为Bubble组件格式
const bubbleMessages = computed(() => {
  const bubbles = props.messages.map(msg => {
    return {
      key: msg.id || `msg-${Date.now()}-${Math.random()}`,
      role: msg.role === 'user' ? 'user' : 'assistant',
      content: msg.content,
      timestamp: msg.timestamp,
      loading: !msg.content, // 流式消息显示加载状态
      // typing: msg.isStreaming || false // 流式消息启用打字效果
    }
  })

  return bubbles
})

// 建议的提示问题
const suggestedPrompts = ref([
  {
    key: 'tender-basics',
    label: '招标文件基础知识',
    description: '了解招标文件相关问题',
    children: [
      { key: 'what-is-tender', description: '什么是招标文件？' },
      { key: 'tender-process', description: '招标流程是怎样的？' },
      { key: 'tender-types', description: '招标文件有哪些类型？' }
    ]
  },
  {
    key: 'document-writing',
    label: '文件编写指导',
    description: '学习如何编写优质招标文件',
    children: [
      { key: 'writing-tips', description: '如何编写优质的招标文件？' },
      { key: 'common-mistakes', description: '招标文件编写常见错误' },
      { key: 'legal-requirements', description: '招标文件的法律要求' }
    ]
  }
])

// 快速建议问题 - 按照官方文档格式
const suggestions = ref([
  { label: '如何确定招标方式？', value: 'tender-method' },
  { label: '投标保证金如何设置？', value: 'bid-bond' },
  { label: '评标标准怎么制定？', value: 'evaluation-criteria' },
  { label: '招标公告要包含哪些内容？', value: 'announcement-content' }
])

// 方法
const handleSendMessage = () => {
  emit('send-message')
}

const handleCancelMessage = () => {
  emit('cancel-message')
}

const handlePromptClick = info => {
  if (info.data.children) return // 分组项不处理

  const message = info.data.label || info.data.description
  emit('update:inputMessage', message)
  nextTick(() => {
    emit('send-message')
  })
}

// 按照官方文档修正的建议选择处理
const handleSuggestionSelect = (value) => {
  // 根据value找到对应的label
  const suggestion = suggestions.value.find(item => item.value === value)
  if (suggestion) {
    emit('update:inputMessage', suggestion.label)
    showSuggestions.value = false
    nextTick(() => {
      emit('send-message')
    })
  }
}

// 建议面板开关状态变化
const handleSuggestionOpenChange = (open) => {
  showSuggestions.value = open
}

// 输入变化处理 - 按照官方文档添加触发逻辑
const handleInputChange = (value, onTrigger) => {
  emit('update:inputMessage', value)
  
  // 检测是否输入了触发字符
  if (value.endsWith('/')) {
    onTrigger()
  } else if (!value) {
    // 输入为空时关闭建议
    onTrigger(false)
  }
}
</script>

<style lang="less" scoped>
::v-deep .ant-welcome-title-wrapper {
  display: flex;
  flex-direction: column;
}
.chat-content-wrapper {
  height: calc(100vh - 90px);
  // height: calc(100vh - 40px);
  padding: 10px 0;
  display: flex;
  flex-direction: column;
}

.chat-content {
  flex: 1;
  overflow: hidden;
  position: relative;

  :deep(.ant-welcome) {
    padding: 24px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  :deep(.ant-bubble-list) {
    height: 100%;
    padding: 16px 20px;
    overflow-y: auto;

    .ant-bubble {
      margin-bottom: 16px;
    }
  }
}

.chat-input {
  background: white;
  border-radius: 0 0 0 12px;
  margin: 10px 10px 10px 0;

  :deep(.ant-sender) {
    padding: 0 15px 0 0;

    .ant-input {
      border-radius: 8px;
      resize: none;
    }

    .ant-btn {
      border-radius: 6px;
    }
  }

  .sender-action {
    color: #8c8c8c;
    cursor: pointer;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      color: #1890ff;
      background: #f0f7ff;
    }
  }

  // Suggestion面板样式
  :deep(.ant-suggestion) {
    .ant-popover-content {
      padding: 8px;
    }
    
    .ant-tag {
      margin: 4px 4px 4px 0;
      cursor: pointer;
      border-radius: 12px;
      transition: all 0.2s;

      &:hover {
        color: #1890ff;
        border-color: #1890ff;
        background: #f0f7ff;
      }
    }
  }
}

// Markdown内容基础样式优化
:deep(.ant-bubble-content) {
  // 只添加必要的代码样式，其他使用浏览器默认样式
  pre {
    background: #f6f8fa;
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 8px 0;
  }

  code {
    background: #f6f8fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', monospace;
  }

  blockquote {
    border-left: 4px solid #1890ff;
    margin: 8px 0;
    padding: 8px 16px;
    background: #f6f8fa;
    border-radius: 0 4px 4px 0;
  }
}
</style>
