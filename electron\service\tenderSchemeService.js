'use strict'

const { sqlitedbService } = require('./database/sqlitedb')
const TenderSchemeTemplateService = require('./tenderSchemeTemplateService') // 引入模板服务

/**
 * 招标方案服务
 * @class
 */
class TenderSchemeService {
  constructor(ctx) {
    this.ctx = ctx
    this.db = sqlitedbService.db
    this.tableName = sqlitedbService.tenderSchemesTableName
    this.historyTableName = sqlitedbService.tenderSchemeHistoryTableName
    this.templateTableName = sqlitedbService.tenderSchemeTemplatesTableName
    this.tenderSchemeTemplateService = new TenderSchemeTemplateService() // 实例化模板服务
  }

  /**
   * 创建招标方案
   * @param {object} data - 方案数据 { name, template_id, remark, content, created_by }
   * @returns {object} 创建的方案对象
   */
  async create(data) {
    const { name, template_id, remark = '', content = {}, created_by } = data

    if (!name) return { success: false, message: '方案名称不能为空', statusCode: 400 }
    if (template_id === undefined || template_id === null) return { success: false, message: '必须关联一个招标方案模板', statusCode: 400 }
    if (!created_by) return { success: false, message: '创建人不能为空', statusCode: 400 }

    // 验证模板是否存在
    const templateResponse = this.tenderSchemeTemplateService.getById(template_id)
    if (!templateResponse.success || !templateResponse.data) {
      return { success: false, message: templateResponse.message || `ID为 ${template_id} 的招标方案模板不存在或获取失败`, statusCode: templateResponse.statusCode || 404 }
    }

    const insertSql = `
      INSERT INTO ${this.tableName} 
      (name, template_id, remark, content, version_number, update_remark, updated_by, 
       share_status, share_users, created_by, created_at, updated_at)
      VALUES (@name, @template_id, @remark, @content, @version_number, @update_remark, 
              @updated_by, @share_status, @share_users, @created_by, 
              datetime('now', 'localtime'), datetime('now', 'localtime'))
    `

    try {
      const stmt = this.db.prepare(insertSql)
      const result = stmt.run({
        name: name,
        template_id,
        remark: remark,
        content: JSON.stringify(content),
        version_number: 1,
        update_remark: '初始版本',
        updated_by: created_by,
        share_status: 'private',
        share_users: null,
        created_by
      })

      if (result.lastInsertRowid) {
        const newSchemeResponse = await this.getById(result.lastInsertRowid)
        if (newSchemeResponse.success) {
          return { success: true, data: newSchemeResponse.data, message: '招标方案创建成功' }
        }
        return { success: false, message: newSchemeResponse.message || '创建招标方案后获取详情失败', statusCode: newSchemeResponse.statusCode || 500 }
      }
      return { success: false, message: '创建招标方案失败，未获取到ID', statusCode: 500 }
    } catch (error) {
      console.error('Error creating tender scheme:', error)
      return { success: false, message: `创建招标方案失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 获取招标方案列表（根据权限过滤）
   * @param {object} params - 查询参数 { page, pageSize, name, template_id, current_user_id }
   * @returns {object} { list, pagination }
   */
  async list(params = {}) {
    const { page = 1, pageSize = 10, name, template_id, current_user_id } = params
    const offset = (page - 1) * pageSize
    let whereClauses = []
    let queryParams = {}

    // 基础条件过滤
    if (name) {
      whereClauses.push('ts.name LIKE @name_like')
      queryParams.name_like = `%${name}%`
    }
    if (template_id) {
      whereClauses.push('ts.template_id = @template_id')
      queryParams.template_id = template_id
    }

    // 权限过滤：创建人、公开、指定分享人
    if (current_user_id) {
      whereClauses.push(`(
        ts.created_by = @current_user_id OR 
        ts.share_status = 'public' OR 
        (ts.share_status = 'specific_users' AND (
          ts.share_users LIKE @user_id_start OR 
          ts.share_users LIKE @user_id_middle OR 
          ts.share_users LIKE @user_id_end OR 
          ts.share_users = @user_id_exact
        ))
      )`)
      queryParams.current_user_id = current_user_id
      queryParams.user_id_start = `${current_user_id},%`
      queryParams.user_id_middle = `%,${current_user_id},%`
      queryParams.user_id_end = `%,${current_user_id}`
      queryParams.user_id_exact = current_user_id.toString()
    }

    const whereString = whereClauses.length > 0 ? `WHERE ${whereClauses.join(' AND ')}` : ''

    // 连表查询模板名称和创建人信息
    const dataSql = `
      SELECT ts.id, ts.name, ts.template_id, tst.name as template_name, ts.remark, 
             ts.version_number, ts.update_remark, ts.share_status, ts.share_users,
             ts.created_by, u1.nickname as created_by_name,
             ts.updated_by, u2.nickname as updated_by_name,
             ts.created_at, ts.updated_at,
             ts.risk_assessment_id,
             CASE 
               WHEN ts.risk_assessment_id IS NULL THEN 'none'
               WHEN ra.status IN ('pending', 'processing') THEN 'processing'
               ELSE ra.status
             END as display_risk_status
      FROM ${this.tableName} ts
      LEFT JOIN ${this.templateTableName} tst ON ts.template_id = tst.id
      LEFT JOIN ${sqlitedbService.appUsersTableName} u1 ON ts.created_by = u1.id
      LEFT JOIN ${sqlitedbService.appUsersTableName} u2 ON ts.updated_by = u2.id
      LEFT JOIN risk_assessment ra ON ts.risk_assessment_id = ra.id
      ${whereString}
      ORDER BY ts.updated_at DESC
      LIMIT @pageSize OFFSET @offset
    `

    const countSql = `
      SELECT COUNT(ts.id) as total 
      FROM ${this.tableName} ts 
      ${whereString}
    `

    try {
      const finalQueryParamsData = { ...queryParams, pageSize, offset }
      const list = this.db.prepare(dataSql).all(finalQueryParamsData)
      const row = this.db.prepare(countSql).get(queryParams)

      const total = row ? row.total : 0

      return {
        success: true,
        data: {
          list: list,
          pagination: {
            total,
            page: Number(page),
            pageSize: Number(pageSize),
            totalPages: Math.ceil(total / pageSize)
          }
        }
      }
    } catch (error) {
      console.error('Error listing tender schemes:', error)
      return { success: false, message: `获取招标方案列表失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 根据ID获取招标方案详情
   * @param {number} id - 方案ID
   * @returns {object} 方案对象
   */
  async getById(id) {
    const sql = `
      SELECT ts.*, tst.name as template_name,
             u1.nickname as created_by_name,
             u2.nickname as updated_by_name,
             CASE 
               WHEN ts.risk_assessment_id IS NULL THEN 'none'
               WHEN ra.status IN ('pending', 'processing') THEN 'processing'
               ELSE ra.status
             END as display_risk_status
      FROM ${this.tableName} ts
      LEFT JOIN ${this.templateTableName} tst ON ts.template_id = tst.id
      LEFT JOIN ${sqlitedbService.appUsersTableName} u1 ON ts.created_by = u1.id
      LEFT JOIN ${sqlitedbService.appUsersTableName} u2 ON ts.updated_by = u2.id
      LEFT JOIN risk_assessment ra ON ts.risk_assessment_id = ra.id
      WHERE ts.id = ?
    `

    try {
      const scheme = this.db.prepare(sql).get(id)
      if (scheme) {
        const processedScheme = scheme

        // 解析content字段
        if (processedScheme.content && typeof processedScheme.content === 'string') {
          try {
            processedScheme.content = JSON.parse(processedScheme.content)
            if (typeof processedScheme.content !== 'object' || processedScheme.content === null) {
              console.warn(`Scheme ID ${id}: Parsed content is not an object, resetting to empty object.`)
              processedScheme.content = {}
            }
          } catch (e) {
            console.error(`Error parsing content for scheme ID ${id}:`, e)
            processedScheme.content = {}
          }
        } else {
          processedScheme.content = processedScheme.content || {}
        }

        return { success: true, data: processedScheme }
      } else {
        return { success: false, message: `ID为 ${id} 的招标方案不存在`, statusCode: 404 }
      }
    } catch (error) {
      console.error('Error getting tender scheme by ID:', error)
      return { success: false, message: `获取招标方案详情失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 更新招标方案
   * @param {number} id - 方案ID
   * @param {object} data - 更新数据 { name?, remark?, content?, update_remark?, updated_by?, share_status?, share_users? }
   * @returns {object} 更新后的方案对象
   */
  async update(id, data) {
    const { name, remark, content: newFieldValues, update_remark, updated_by, share_status, share_users } = data

    if (!updated_by) {
      return { success: false, message: '更新人不能为空', statusCode: 400 }
    }

    // 获取现有数据
    const existingSchemeResponse = await this.getById(id)
    if (!existingSchemeResponse.success) {
      return existingSchemeResponse
    }
    const existingScheme = existingSchemeResponse.data

    let fieldsToUpdate = {}
    let paramsForRun = { id }

    // 检查content是否有变化
    let contentChanged = false
    if (newFieldValues !== undefined) {
      const currentContent = JSON.stringify(existingScheme.content || {})
      const newContent = JSON.stringify(newFieldValues || {})

      if (currentContent !== newContent) {
        contentChanged = true

        // 在更新前，将当前数据保存到历史版本表
        await this.saveToHistory(id, existingScheme)

        // 更新内容和版本号
        fieldsToUpdate.content = 'content = @content'
        fieldsToUpdate.version_number = 'version_number = @version_number'
        paramsForRun.content = JSON.stringify(newFieldValues)
        paramsForRun.version_number = (existingScheme.version_number || 1) + 1

        // 版本更新时重置风险评估ID
        fieldsToUpdate.risk_assessment_id = 'risk_assessment_id = NULL'
      }
    }

    // 其他字段更新
    if (name !== undefined) {
      fieldsToUpdate.name = 'name = @name'
      paramsForRun.name = name
    }
    if (remark !== undefined) {
      fieldsToUpdate.remark = 'remark = @remark'
      paramsForRun.remark = remark
    }
    if (update_remark !== undefined) {
      fieldsToUpdate.update_remark = 'update_remark = @update_remark'
      paramsForRun.update_remark = update_remark
    }
    if (share_status !== undefined) {
      fieldsToUpdate.share_status = 'share_status = @share_status'
      paramsForRun.share_status = share_status
    }
    if (share_users !== undefined) {
      fieldsToUpdate.share_users = 'share_users = @share_users'
      paramsForRun.share_users = share_users
    }

    // 更新updated_by和updated_at
    fieldsToUpdate.updated_by = 'updated_by = @updated_by'
    fieldsToUpdate.updated_at = "updated_at = datetime('now', 'localtime')"
    paramsForRun.updated_by = updated_by

    // 如果没有任何变化
    if (Object.keys(fieldsToUpdate).length <= 2) {
      // 只有updated_by和updated_at
      return { success: true, data: existingScheme, message: '数据无变化' }
    }

    const setClauses = Object.values(fieldsToUpdate).join(', ')
    const updateSql = `UPDATE ${this.tableName} SET ${setClauses} WHERE id = @id`

    try {
      const stmt = this.db.prepare(updateSql)
      const result = stmt.run(paramsForRun)

      if (result.changes > 0) {
        const updatedSchemeResponse = await this.getById(id)
        if (updatedSchemeResponse.success) {
          return { success: true, data: updatedSchemeResponse.data, message: '招标方案更新成功' }
        }
        return { success: false, message: updatedSchemeResponse.message || '更新招标方案后获取详情失败', statusCode: updatedSchemeResponse.statusCode || 500 }
      } else {
        return { success: true, data: existingScheme, message: '数据无实质变化' }
      }
    } catch (error) {
      console.error(`Error updating tender scheme ID ${id}:`, error)
      return { success: false, message: `更新招标方案失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 保存当前数据到历史版本表
   * @param {number} schemeId - 方案ID
   * @param {object} currentData - 当前数据
   */
  async saveToHistory(schemeId, currentData) {
    const insertHistorySql = `
      INSERT INTO ${this.historyTableName} 
      (scheme_id, content, version_number, update_remark, updated_by, created_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `

    try {
      const stmt = this.db.prepare(insertHistorySql)
      stmt.run(
        schemeId,
        JSON.stringify(currentData.content || {}),
        currentData.version_number || 1,
        currentData.update_remark || '',
        currentData.updated_by,
        currentData.updated_at
      )
    } catch (error) {
      console.error('Error saving to history:', error)
      throw error
    }
  }

  /**
   * 删除招标方案（同时删除历史记录）
   * @param {number} id - 方案ID
   * @returns {object} 操作结果
   */
  async delete(id) {
    try {
      // 使用事务确保数据一致性
      const deleteTransaction = this.db.transaction(() => {
        // 先删除历史记录
        const deleteHistoryStmt = this.db.prepare(`DELETE FROM ${this.historyTableName} WHERE scheme_id = ?`)
        deleteHistoryStmt.run(id)

        // 删除主记录
        const deleteMainStmt = this.db.prepare(`DELETE FROM ${this.tableName} WHERE id = ?`)
        const result = deleteMainStmt.run(id)

        return result
      })

      const result = deleteTransaction()

      if (result.changes > 0) {
        return { success: true, message: '招标方案删除成功' }
      } else {
        return { success: false, message: '未删除任何招标方案 (可能ID不存在或已被删除)', statusCode: 404 }
      }
    } catch (error) {
      console.error(`Error deleting tender scheme ID ${id}:`, error)
      return { success: false, message: `删除招标方案失败: ${error.message}`, statusCode: 500 }
    }
  }

  /**
   * 获取历史记录列表
   * @param {number} schemeId - 方案ID
   * @returns {object} 历史记录列表
   */
  async getHistory(schemeId) {
    const sql = `
      SELECT h.*, u.nickname as updated_by_name
      FROM ${this.historyTableName} h
      LEFT JOIN ${sqlitedbService.appUsersTableName} u ON h.updated_by = u.id
      WHERE h.scheme_id = ?
      ORDER BY h.version_number DESC
    `

    try {
      const historyList = this.db.prepare(sql).all(schemeId)

      // 解析content字段
      const processedList = historyList.map(item => {
        const processedItem = item
        if (processedItem.content && typeof processedItem.content === 'string') {
          try {
            processedItem.content = JSON.parse(processedItem.content)
          } catch (e) {
            console.error(`Error parsing history content for scheme ID ${schemeId}:`, e)
            processedItem.content = {}
          }
        }
        return processedItem
      })

      return { success: true, data: processedList }
    } catch (error) {
      console.error(`Error getting history for scheme ID ${schemeId}:`, error)
      return { success: false, message: `获取历史记录失败: ${error.message}`, statusCode: 500 }
    }
  }
}

module.exports = TenderSchemeService
