import { Extension } from '@tiptap/core'

/**
 * 段落间距和缩进扩展 ParagraphSpacingExtension
 * 支持首行缩进和行间距设置
 */
export const ParagraphSpacingExtension = Extension.create({
  name: 'paragraphSpacing',

  addOptions() {
    return {
      types: ['paragraph', 'heading']
    }
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          // 首行缩进
          textIndent: {
            default: null,
            parseHTML: element => element.style.textIndent?.replace(/['"]+/g, ''),
            renderHTML: attributes => {
              if (!attributes.textIndent) {
                return {}
              }
              return {
                style: `text-indent: ${attributes.textIndent}`
              }
            }
          },
          // 行间距
          lineHeight: {
            default: null,
            parseHTML: element => element.style.lineHeight?.replace(/['"]+/g, ''),
            renderHTML: attributes => {
              if (!attributes.lineHeight) {
                return {}
              }
              return {
                style: `line-height: ${attributes.lineHeight}`
              }
            }
          }
        }
      }
    ]
  },

  addCommands() {
    return {
      // 设置首行缩进
      setTextIndent:
        indent =>
        ({ chain }) => {
          return chain().updateAttributes('paragraph', { textIndent: indent }).updateAttributes('heading', { textIndent: indent }).run()
        },
      // 取消首行缩进
      unsetTextIndent:
        () =>
        ({ chain }) => {
          return chain().updateAttributes('paragraph', { textIndent: null }).updateAttributes('heading', { textIndent: null }).run()
        },
      // 设置行间距
      setLineHeight:
        height =>
        ({ chain }) => {
          return chain().updateAttributes('paragraph', { lineHeight: height }).updateAttributes('heading', { lineHeight: height }).run()
        },
      // 取消行间距
      unsetLineHeight:
        () =>
        ({ chain }) => {
          return chain().updateAttributes('paragraph', { lineHeight: null }).updateAttributes('heading', { lineHeight: null }).run()
        },
      // 设置首行缩进2字符（中文常用）
      setFirstLineIndent:
        () =>
        ({ chain }) => {
          return chain().setTextIndent('2em').run()
        }
    }
  }
})

export default ParagraphSpacingExtension
